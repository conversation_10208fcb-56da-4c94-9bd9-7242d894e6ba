{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "host": "vite --host", "build": "tsc -b && vite build", "preview": "vite preview --host", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@heroui/calendar": "^2.2.13", "@heroui/dropdown": "^2.3.15", "@heroui/input": "^2.4.13", "@heroui/link": "^2.2.8", "@heroui/system": "^2.4.7", "@heroui/theme": "^2.4.6", "@hookform/resolvers": "^4.1.3", "@liveblocks/client": "^2.5.1", "@liveblocks/react": "^2.5.1", "@liveblocks/react-ui": "^2.5.1", "@nextui-org/react": "^2.4.2", "@nextui-org/system": "^2.2.2", "@nextui-org/theme": "^2.2.6", "@omit/react-confirm-dialog": "^1.1.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-query": "^5.49.2", "@tanstack/react-query-devtools": "^5.66.5", "@tanstack/react-table": "^8.19.3", "@vis.gl/react-google-maps": "^1.5.2", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "embla-carousel-react": "^8.6.0", "fabric": "^6.3.0", "framer-motion": "^12.4.2", "jotai": "^2.12.1", "jwt-decode": "^4.0.0", "konva": "^9.3.18", "leaflet": "^1.9.4", "lottie-react": "^2.4.1", "lucide-react": "^0.399.0", "opencage-api-client": "^1.0.7", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hotkeys-hook": "^4.5.0", "react-konva": "^18.2.10", "react-leaflet": "^4.2.1", "react-range": "^1.10.0", "react-router-dom": "^7.2.0", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "universal-cookie": "^7.2.0", "uuid": "^10.0.0", "vaul": "^0.9.1", "zod": "^3.23.0-canary.20240409T033359"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.49.1", "@types/leaflet": "^1.9.12", "@types/node": "^20.14.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-tailwindcss": "^3.17.4", "postcss": "^8.4.39", "tailwindcss": "^3.4.4", "typescript": "^5.5.3", "vite": "^6.2.1"}}