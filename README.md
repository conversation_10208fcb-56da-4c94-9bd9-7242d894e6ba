# Vite front-end padrão

Este é um projeto padrão criado com Vite que utiliza Tailwind CSS, Framer Motion, Shadcn, NextUI e TanStack Query para construir uma aplicação web moderna e eficiente.

## Tecnologias Utilizadas

- **Vite**: Ferramenta de build rápida que fornece um ambiente de desenvolvimento ágil.
- **Tailwind CSS**: Framework de CSS utilitário para estilização rápida e customizável.
- **Framer Motion**: Biblioteca de animação para React.
- **Shadcn**: Componentes de interface de usuário acessíveis e customizáveis.
- **NextUI**: Componentes de interface de usuário modernos e elegantes.
- **TanStack Query**: Gerenciamento de estado e cache para consultas assíncronas.
- **React Router Dom** : Navegação por rotas.

1. Clone o repositório:

   ```sh git clone https://github.com/Toddynn/default-front-vite.git ```

2. Só instalar e rodar o projeto, atualize as preferências de tema do shadcn no index.css.

Esse projeto padrão já inclui algumas funcionalidades como um connection provider e hook, que identifica se o cliente está conectado à internet ou não.
Troca de temas já pronto. Entre outras coisas... Sinta-se Livre para explorar e trazer melhorias para agilizar ainda mais o desenvolvimento de softwares.

## **Explore**: https://awesome-shadcn-ui.vercel.app/
