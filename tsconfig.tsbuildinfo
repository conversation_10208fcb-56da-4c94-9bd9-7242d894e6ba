{"root": ["./liveblocks.config.ts", "./tailwind.config.ts", "./vite.config.ts", "./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/confirm-dialog/confirm-context.ts", "./src/components/confirm-dialog/confirm-dialog.tsx", "./src/components/confirm-dialog/types.ts", "./src/components/confirm-dialog/use-confirm.ts", "./src/components/customs/tooltip.tsx", "./src/components/error-boundary/error-boundary.tsx", "./src/components/error-boundary/error-fallback.tsx", "./src/components/error-boundary/error-message.tsx", "./src/components/error-boundary/index.ts", "./src/components/error-boundary/reload-button.tsx", "./src/components/global-search-filter/index.tsx", "./src/components/presentation-elements/elements-overlay-base/carrousel-demo.tsx", "./src/components/presentation-elements/elements-overlay-base/clock.tsx", "./src/components/presentation-elements/elements-overlay-base/image.tsx", "./src/components/presentation-elements/elements-overlay-base/rectangle.tsx", "./src/components/presentation-elements/elements-overlay-base/text.tsx", "./src/components/presentation-elements/elements-overlay-base/weather.tsx", "./src/components/presentation-elements/elements-overlay-base/calendar/calendar-component.tsx", "./src/components/presentation-elements/elements-overlay-base/calendar/calendar.tsx", "./src/components/presentation-elements/html-overlay/component-style.tsx", "./src/components/presentation-elements/html-overlay/components.tsx", "./src/components/presentation-elements/html-overlay/html-overlay.tsx", "./src/components/presentation-elements/html-overlay/item.tsx", "./src/components/presentation-elements/html-overlay/overlay-container.tsx", "./src/components/shadcnui/alert-dialog.tsx", "./src/components/shadcnui/badge.tsx", "./src/components/shadcnui/button.tsx", "./src/components/shadcnui/calendar.tsx", "./src/components/shadcnui/card.tsx", "./src/components/shadcnui/carousel.tsx", "./src/components/shadcnui/checkbox.tsx", "./src/components/shadcnui/collapsible.tsx", "./src/components/shadcnui/dropdown-menu.tsx", "./src/components/shadcnui/input-helper.tsx", "./src/components/shadcnui/input.tsx", "./src/components/shadcnui/label.tsx", "./src/components/shadcnui/navbar.tsx", "./src/components/shadcnui/particles.tsx", "./src/components/shadcnui/popover.tsx", "./src/components/shadcnui/select.tsx", "./src/components/shadcnui/sheet.tsx", "./src/components/shadcnui/table.tsx", "./src/components/shadcnui/tabs.tsx", "./src/components/shadcnui/textarea.tsx", "./src/components/shadcnui/theme-provider.tsx", "./src/components/shadcnui/tooltip.tsx", "./src/contexts/status-connection-context.tsx", "./src/contexts/auth/hooks/auth-check.hook.ts", "./src/contexts/auth/hooks/login-form.hook.ts", "./src/contexts/auth/hooks/login-mutation.hook.ts", "./src/contexts/auth/states/token-expiration.state.ts", "./src/contexts/auth/states/user.state.ts", "./src/contexts/auth/validators/login-form.ts", "./src/functions/get-headers-authorization.ts", "./src/functions/get-zod-errors.ts", "./src/functions/tanstack-query/devices/get-all.ts", "./src/hooks/use-auth-events.ts", "./src/hooks/use-clipboard.ts", "./src/hooks/use-connection.ts", "./src/hooks/use-debounce.ts", "./src/hooks/use-interval.ts", "./src/hooks/use-is-mobile.ts", "./src/hooks/use-theme.ts", "./src/hooks/use-token.ts", "./src/pages/devices/index.tsx", "./src/pages/devices/components/forms/device-basic-informations-form.tsx", "./src/pages/devices/components/forms/device-details-form.tsx", "./src/pages/devices/components/forms/device-location-form.tsx", "./src/pages/devices/components/modals/device-modal.tsx", "./src/pages/devices/components/table/device-card.tsx", "./src/pages/devices/components/table/components/cells/actions-cell.tsx", "./src/pages/devices/components/table/components/cells/location-cell.tsx", "./src/pages/devices/components/table/components/cells/token-cell.tsx", "./src/pages/devices/components/table/components/columns/devices-columns.tsx", "./src/pages/devices/components/table/components/modals/create-device-modal.tsx", "./src/pages/devices/components/table/components/modals/delete-device-modal.tsx", "./src/pages/devices/components/table/components/modals/edit-device-modal.tsx", "./src/pages/devices/components/table/components/modals/review-device-informations.tsx", "./src/pages/devices/components/table/components/table/device-table-body.tsx", "./src/pages/devices/components/table/components/table/device-table-header.tsx", "./src/pages/devices/components/table/components/table/devices-data-table-pagination.tsx", "./src/pages/devices/components/table/components/table/devices-data-table-toolbar.tsx", "./src/pages/devices/components/table/components/table/devices-data-table.tsx", "./src/pages/devices/data/query-keys.ts", "./src/pages/devices/functions/change-icon-by-device-type.tsx", "./src/pages/devices/functions/use-device-token-actions.tsx", "./src/pages/devices/hooks/index.ts", "./src/pages/devices/hooks/crud/create-device.hook.ts", "./src/pages/devices/hooks/crud/delete-device.hook.ts", "./src/pages/devices/hooks/crud/devices-table.hook.ts", "./src/pages/devices/hooks/crud/find-address.hook.ts", "./src/pages/devices/hooks/crud/find-all.hook.ts", "./src/pages/devices/hooks/crud/find-by-id.hook.ts", "./src/pages/devices/hooks/crud/generate-token.hook.ts", "./src/pages/devices/hooks/crud/remove-token.hook.ts", "./src/pages/devices/hooks/crud/update-device.hook.ts", "./src/pages/devices/hooks/form/use-create-device-form.hook.ts", "./src/pages/devices/hooks/form/use-device-location.hook.ts", "./src/pages/devices/hooks/form/use-device-steps.hook.ts", "./src/pages/devices/hooks/form/use-device-submit.hook.ts", "./src/pages/devices/interfaces/create-device-form-base-props.ts", "./src/pages/devices/interfaces/step.ts", "./src/pages/devices/services/endpoints/index.ts", "./src/pages/devices/services/request/create-device.ts", "./src/pages/devices/services/request/delete-device.ts", "./src/pages/devices/services/request/find-address.ts", "./src/pages/devices/services/request/find-all.ts", "./src/pages/devices/services/request/find-by-id.ts", "./src/pages/devices/services/request/generate-token.ts", "./src/pages/devices/services/request/remove-token.ts", "./src/pages/devices/services/request/update-device.ts", "./src/pages/devices/states/pagination.state.ts", "./src/pages/devices/states/search-devices.state.ts", "./src/pages/devices/validators/create-device-schema.form.ts", "./src/pages/edit-apresentation/index.tsx", "./src/pages/edit-apresentation/components/canvas-editor/index.tsx", "./src/pages/edit-apresentation/components/canvas-editor/config/konva-stage.tsx", "./src/pages/edit-apresentation/components/canvas-editor/config/snap-lines.tsx", "./src/pages/edit-apresentation/components/canvas-editor/shapes/base-shape.tsx", "./src/pages/edit-apresentation/components/canvas-editor/shapes/preview-shape.tsx", "./src/pages/edit-apresentation/components/canvas-editor/shapes/shape-utils.tsx", "./src/pages/edit-apresentation/components/canvas-editor/shapes/shapes-layer.tsx", "./src/pages/edit-apresentation/components/canvas-editor/shapes/tooltip.tsx", "./src/pages/edit-apresentation/components/cursors/context-menu.tsx", "./src/pages/edit-apresentation/components/cursors/global-context-menu.tsx", "./src/pages/edit-apresentation/components/cursors/index.tsx", "./src/pages/edit-apresentation/components/elements-layers/element-layer-item.tsx", "./src/pages/edit-apresentation/components/elements-layers/index.tsx", "./src/pages/edit-apresentation/components/elements-layers/status-icon.tsx", "./src/pages/edit-apresentation/components/error-container/index.tsx", "./src/pages/edit-apresentation/components/help/keyboard-shortcuts-modal.tsx", "./src/pages/edit-apresentation/components/help/shortcuts-list.tsx", "./src/pages/edit-apresentation/components/help/shortcuts-search-and-filter.tsx", "./src/pages/edit-apresentation/components/left-side-bar/index.tsx", "./src/pages/edit-apresentation/components/mobile-warning/index.tsx", "./src/pages/edit-apresentation/components/nav-actions/drop-menu.tsx", "./src/pages/edit-apresentation/components/nav-actions/index.tsx", "./src/pages/edit-apresentation/components/nav-actions/nav-item.tsx", "./src/pages/edit-apresentation/components/properties-panel/index.ts", "./src/pages/edit-apresentation/components/properties-panel/core/index.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/components/categorory-title.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/components/element-size.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/components/input-group.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/components/item-details.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/components/item-editor-selector.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/components/not-element-selected.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/components/size-input.tsx", "./src/pages/edit-apresentation/components/properties-panel/core/data/animations.ts", "./src/pages/edit-apresentation/components/properties-panel/core/data/animations/properties-panel/no-itens-selected.ts", "./src/pages/edit-apresentation/components/properties-panel/core/hooks/properties-panel.hook.ts", "./src/pages/edit-apresentation/components/properties-panel/core/types/properties.types.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/calendar/caledar.types.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/calendar/calendar.types.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/calendar/index.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/calendar/use-calendar-form.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/carousel/carousel-form.hook.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/carousel/carousel.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/carousel/carrousel.utils.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/carousel/corousel.types.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/image/image-editor.types.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/image/index.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/index.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/square-editor.helpers.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/square-editor.types.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/square.data.ts", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/fields/background/background.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/fields/background/linear.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/fields/background/opaque.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/shape/fields/background/radial.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/text/text-editor.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/weather/index.tsx", "./src/pages/edit-apresentation/components/properties-panel/item-editors/weather/weather-editor.type.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/alignment/align-component.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/alignment/align-elements.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/alignment/alignment-panel.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/alignment/index.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/index.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/section-header.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/containers/properties-card-container.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/media/index.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/media/media-types.data.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/media/media-upload.hook.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/media/media.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/media/preview.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/media/remove-media.hook.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/property-editors/index.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/property-editors/border/index.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/property-editors/shadow/presets-menu.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/property-editors/shadow/shadow-editor.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/ui/index.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/common/ui/input-item-with-icon.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/ui/input-with-label.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/ui/select-item-with-label.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/common/ui/tooltip.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/distribution/distribution-component.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/distribution/distribution-panel.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/distribution/index.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/organization/index.ts", "./src/pages/edit-apresentation/components/properties-panel/shared/organization/organization-component.tsx", "./src/pages/edit-apresentation/components/properties-panel/shared/organization/organization-panel.tsx", "./src/pages/edit-apresentation/components/right-side-bar/index.tsx", "./src/pages/edit-apresentation/data/base-format-elements/calendar.tsx", "./src/pages/edit-apresentation/data/base-format-elements/caroulsel.ts", "./src/pages/edit-apresentation/data/base-format-elements/image.ts", "./src/pages/edit-apresentation/data/base-format-elements/index.ts", "./src/pages/edit-apresentation/data/base-format-elements/square.ts", "./src/pages/edit-apresentation/data/base-format-elements/weather.ts", "./src/pages/edit-apresentation/data/constants/canvas-editor.constants.ts", "./src/pages/edit-apresentation/data/constants/keyboard-shortcuts-modal.constants.tsx", "./src/pages/edit-apresentation/data/cursor/cursor-type.ts", "./src/pages/edit-apresentation/data/items/shapes-menu.tsx", "./src/pages/edit-apresentation/data/properties-panel/align-items.ts", "./src/pages/edit-apresentation/data/properties-panel/distribution-items.ts", "./src/pages/edit-apresentation/data/properties-panel/organization-items.ts", "./src/pages/edit-apresentation/dtos/elements/create-element.dto.ts", "./src/pages/edit-apresentation/hooks/canvas/cache/canvas-cache-manager.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/config/index.ts", "./src/pages/edit-apresentation/hooks/canvas/config/core/canvas-config.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/config/core/canvas-editor.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/config/features/item-tranformer.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/config/features/socket-status.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/config/layout/container-width.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/core/use-canvas-editor-state.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/index.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/core/interactions.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/events/canvas-click.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/events/click-outside.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/events/shape-hover.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/selection/selection-box.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/selection/shape-selection.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/transform/drag-and-drop.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/transform/element-dimensions.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/transform/snap-lines.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/interactions/transform/transformer-handler.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/canvas-alignment.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/canvas-clipboard.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/canvas-layers.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/canvas-movement.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/canvas-selection.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/keyboards.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/use-dragging-state.hook.ts", "./src/pages/edit-apresentation/hooks/canvas/keyboards/use-movement-keys.hook.ts", "./src/pages/edit-apresentation/hooks/cursor/context-menu.hook.ts", "./src/pages/edit-apresentation/hooks/cursor/dynamic-cursor.hook.ts", "./src/pages/edit-apresentation/hooks/element/deleted-item.hook.ts", "./src/pages/edit-apresentation/hooks/element/element-layer.hook.ts", "./src/pages/edit-apresentation/hooks/element/item-selection.hook.ts", "./src/pages/edit-apresentation/hooks/element/layer-management.hook.ts", "./src/pages/edit-apresentation/hooks/element/manage-element.hook.ts", "./src/pages/edit-apresentation/hooks/element/new-item.hook.ts", "./src/pages/edit-apresentation/hooks/element/updated-item.hook.ts", "./src/pages/edit-apresentation/hooks/help/help-modal.hook.ts", "./src/pages/edit-apresentation/hooks/help/keyboard-shortcuts-data.hook.ts", "./src/pages/edit-apresentation/hooks/help/use-keyboard-shortcuts-filter.hook.ts", "./src/pages/edit-apresentation/hooks/history/history.hook.ts", "./src/pages/edit-apresentation/hooks/keyboards/keyboard-shotcuts.hook.ts", "./src/pages/edit-apresentation/hooks/navbar/nav-actions.tsx", "./src/pages/edit-apresentation/hooks/presentation/setup-presentation.hook.ts", "./src/pages/edit-apresentation/hooks/user/use-user-data.hook.ts", "./src/pages/edit-apresentation/hooks/utils/form-state.hook.ts", "./src/pages/edit-apresentation/lib/history/normalized-snapshot.ts", "./src/pages/edit-apresentation/lib/item/geometry.ts", "./src/pages/edit-apresentation/lib/item/get-alterations.ts", "./src/pages/edit-apresentation/lib/item/scale.ts", "./src/pages/edit-apresentation/lib/json-utils/json-converter.ts", "./src/pages/edit-apresentation/lib/presentation/index.ts", "./src/pages/edit-apresentation/lib/utils/canvas-utils.ts", "./src/pages/edit-apresentation/services/endpoints/index.ts", "./src/pages/edit-apresentation/services/manage/element.service.ts", "./src/pages/edit-apresentation/services/manage/socket-connection.service.ts", "./src/pages/edit-apresentation/services/requests/elements/add-media.ts", "./src/pages/edit-apresentation/services/requests/elements/create.ts", "./src/pages/edit-apresentation/services/requests/elements/delete.ts", "./src/pages/edit-apresentation/services/requests/elements/remove-medias.ts", "./src/pages/edit-apresentation/services/requests/elements/update.ts", "./src/pages/edit-apresentation/states/canvas/snap-lines.state.ts", "./src/pages/edit-apresentation/states/context-menu/context-menu.state.ts", "./src/pages/edit-apresentation/states/history/history.state.ts", "./src/pages/edit-apresentation/states/presentation/presentation-info.state.ts", "./src/pages/edit-apresentation/states/presentation/scale.state.ts", "./src/pages/edit-apresentation/types/canvas-editor.types.ts", "./src/pages/edit-apresentation/types/index.ts", "./src/pages/edit-apresentation/types/item.type.ts", "./src/pages/edit-apresentation/types/canvas/shape.ts", "./src/pages/edit-apresentation/types/canvas/text.ts", "./src/pages/edit-apresentation/types/canvas/shape/index.tsx", "./src/pages/edit-apresentation/types/cursor/interaction.type.ts", "./src/pages/edit-apresentation/types/elements/border-type.ts", "./src/pages/edit-apresentation/types/elements/layer.type.ts", "./src/pages/edit-apresentation/types/html-overlay/container.type.ts", "./src/pages/edit-apresentation/types/socket/socket-connection.type.ts", "./src/pages/edit-apresentation/types/socket/socket-elements.type.ts", "./src/pages/home/<USER>", "./src/pages/login/index.tsx", "./src/pages/presentation/index.tsx", "./src/pages/presentation/dtos/get-presentation.dto.ts", "./src/pages/presentation/hook/find-device.hook.ts", "./src/pages/presentation/services/endpoints/index.ts", "./src/pages/presentation/services/requests/find-device.ts", "./src/pages/presentations/index.tsx", "./src/pages/presentations/components/create-new-presentation/form/auto-open-check.tsx", "./src/pages/presentations/components/create-new-presentation/form/custom-dimension-fields.tsx", "./src/pages/presentations/components/create-new-presentation/form/dimension-type-selector.tsx", "./src/pages/presentations/components/create-new-presentation/form/index.tsx", "./src/pages/presentations/components/create-new-presentation/form/preset-selector.tsx", "./src/pages/presentations/components/create-new-presentation/form/submit-button.tsx", "./src/pages/presentations/components/create-new-presentation/form/title-description-fields.tsx", "./src/pages/presentations/components/create-new-presentation/modal/index.tsx", "./src/pages/presentations/components/table/card.tsx", "./src/pages/presentations/components/table/index.tsx", "./src/pages/presentations/components/table/body/index.tsx", "./src/pages/presentations/components/table/columns/index.tsx", "./src/pages/presentations/components/table/columns/actions/actions-dropdown.tsx", "./src/pages/presentations/components/table/columns/columns/actions-column.tsx", "./src/pages/presentations/components/table/columns/columns/date-column.tsx", "./src/pages/presentations/components/table/columns/columns/dimension-column.tsx", "./src/pages/presentations/components/table/columns/columns/select-column.tsx", "./src/pages/presentations/components/table/columns/columns/text-column.tsx", "./src/pages/presentations/components/table/columns/headers/create-header.tsx", "./src/pages/presentations/components/table/header/index.tsx", "./src/pages/presentations/components/table/pagination/container.tsx", "./src/pages/presentations/components/table/pagination/controls.tsx", "./src/pages/presentations/components/table/pagination/info.tsx", "./src/pages/presentations/components/table/pagination/rows-per-page.tsx", "./src/pages/presentations/components/table/toolbar/toolbar.tsx", "./src/pages/presentations/hooks/crud/create.hook.ts", "./src/pages/presentations/hooks/crud/delete.hook.ts", "./src/pages/presentations/hooks/crud/find-all.hook.ts", "./src/pages/presentations/hooks/crud/get-by-id.hook.ts", "./src/pages/presentations/hooks/form/create-presentation-form.hook.ts", "./src/pages/presentations/hooks/table/presentations-table.hook.ts", "./src/pages/presentations/services/endpoints/index.tsx", "./src/pages/presentations/services/requests/presentations/create.ts", "./src/pages/presentations/services/requests/presentations/delete.ts", "./src/pages/presentations/services/requests/presentations/find-all.ts", "./src/pages/presentations/services/requests/presentations/find-by-id.ts", "./src/pages/presentations/states/pagination.state.ts", "./src/pages/presentations/states/search.state.ts", "./src/pages/presentations/types/table/actions.type.ts", "./src/pages/presentations/types/table/index.type.ts", "./src/pages/presentations/utils/format-dimensions.ts", "./src/pages/presentations/validation/create-presentation.form.ts", "./src/pages/programing/index.tsx", "./src/pages/programing/api/endpoints/index.ts", "./src/pages/programing/api/requests/find-all.ts", "./src/pages/programing/components/devices-table.tsx", "./src/pages/programing/components/mock-data.ts", "./src/pages/programing/components/presentation-list.tsx", "./src/pages/programing/components/schedule-form.tsx", "./src/pages/programing/components/schedule-item.tsx", "./src/pages/programing/components/timeline-view.tsx", "./src/pages/programing/components/types.ts", "./src/pages/programing/components/utils.ts", "./src/pages/programing/components/weekly-view.tsx", "./src/routes/index.tsx", "./src/routes/private/private-routes.tsx", "./src/shared/constants/canvas/canvas.ts", "./src/shared/constants/canvas/shapes.ts", "./src/shared/constants/framer/slide-from-bottom.ts", "./src/shared/constants/framer/slide-from-top.ts", "./src/shared/constants/my-figma/index.tsx", "./src/shared/constants/roles/index.ts", "./src/shared/hooks/image-loader.hook.ts", "./src/shared/hooks/media-loader.hook.ts", "./src/shared/hooks/socket-connection.hook.ts", "./src/shared/interfaces/canvas.ts", "./src/shared/interfaces/users/current-user.ts", "./src/shared/interfaces/users/token-user.ts", "./src/shared/lib/utils.ts", "./src/shared/lib/api/api.ts", "./src/shared/lib/api/geocode.ts", "./src/shared/lib/auth/auth-config.ts", "./src/shared/lib/auth/auth-cookies.ts", "./src/shared/lib/auth/handle-auth-error.ts", "./src/shared/lib/auth/index.ts", "./src/shared/lib/auth/refresh-token-manager.ts", "./src/shared/lib/canvas/key-events.ts", "./src/shared/lib/canvas/use-max-z-index.ts", "./src/shared/lib/clients/cookies-client.ts", "./src/shared/lib/clients/tanstack-client.ts", "./src/shared/lib/errors/handle-global.error.ts", "./src/shared/lib/events/auth-events.ts", "./src/shared/lib/jwt-tools/decode-jwt.ts", "./src/shared/lib/jwt-tools/get-expiration-time.ts", "./src/shared/lib/jwt-tools/get-jwt-payload-property.lib.ts", "./src/shared/lib/toaster/toaster.ts", "./src/shared/lib/utils/format-date.ts", "./src/shared/services/endpoints/auth.ts", "./src/shared/services/requests/login.ts", "./src/shared/services/requests/logout.ts", "./src/shared/services/requests/request.ts", "./src/shared/states/items/next-items.state.ts", "./src/shared/states/items/object-item.state.ts", "./src/shared/states/socket/socket-status.state.ts", "./src/shared/types/response.ts", "./src/shared/types/elements/global.types.ts", "./src/shared/types/utils/pagination.type.ts", "./src/templates/loading/index.tsx", "./src/templates/lost/index.tsx", "./src/templates/main-page/index.tsx", "./src/templates/without-connection/index.tsx", "./src/templates/without-permission/index.tsx"], "version": "5.7.3"}