import { toast } from 'sonner';
import { z } from 'zod';

export function getZodErrors(error: unknown): boolean {
	if (error instanceof z.ZodError) {
		if (error.issues.length > 0) {
			error.issues.forEach((issue) => {
				toast.error(issue.message);
			});
			return true; // Indicando que houve erros
		} else {
			toast.error('Ocorreu um erro de validação, mas nenhuma informação específica foi fornecida.');
			return true;
		}
	} else {
		toast.error('Ocorreu um erro ao processar os dados.');
		return false; // Indicando que houve um erro não relacionado ao Zod
	}
}
