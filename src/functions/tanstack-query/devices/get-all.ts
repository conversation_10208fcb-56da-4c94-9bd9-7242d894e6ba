import { getHeadersAuthorization } from '@/functions/get-headers-authorization';
import api from '@/shared/lib/api/api';

export interface Device {
	id: number;
	name: string;
	status: boolean;
	modify: Date;
	type: string;
	width: number;
	height: number;
	resolution: string;
	brand: string;
	model: string;
	token: string | null;
	token_generated_at: Date | null;
	token_expires_at: Date | null;
	location: Location;
	created_at: Date;
	updated_at: Date | null;
}

interface Location {
	x: number;
	y: number;
}
export async function getAllDevices(token: string): Promise<Device[]> {
	const res = await api.get(`/devices/admin`, getHeadersAuthorization(token));
	return res.data;
}
