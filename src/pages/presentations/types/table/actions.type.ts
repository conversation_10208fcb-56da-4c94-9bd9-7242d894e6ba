import { PresentationActionsConfig } from './index.type';

export interface ActionsColumnOptions {
	id: string;
	header: string;
	config: PresentationActionsConfig;
}

export interface ActionsDropdownProps {
	itemId: string;
	config: PresentationActionsConfig;
	name: string;
}

export interface ActionItem {
	key: string;
	label: string;
	Icon: React.ElementType;
	onClick: (id: string) => void;
	className?: string;
	color?: string;
	active: boolean;
}
