export interface PresentationActionsConfig {
	onEdit: ((id: string) => void) | null;
	onDelete?: ((id: string) => void) | null;
	onView?: ((id: string) => void) | null;
	onDuplicate?: ((id: string) => void) | null;
}

export interface TextColumnOptions<T> {
	accessorKey: keyof T | string;
	header: string;
	className?: string;
	enableSorting?: boolean;
	enableHiding?: boolean;
}

export interface DateColumnOptions<T> {
	accessorKey: keyof T | string;
	header: string;
	formatter: (value: string) => string;
	enableSorting?: boolean;
	enableHiding?: boolean;
}

export interface DimensionsColumnOptions<T> {
	id: string;
	header: string;
	widthAccessor: keyof T;
	heightAccessor: keyof T;
	formatter: (width: number, height: number) => string;
	enableSorting?: boolean;
	enableHiding?: boolean;
}
