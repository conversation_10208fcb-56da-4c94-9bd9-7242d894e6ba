import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';

interface PaginationInfoProps<TData> {
	table: Table<TData>;
}

const SelectedRowsInfo = <TData,>({ table }: PaginationInfoProps<TData>) => {
	const selectedCount = table.getFilteredSelectedRowModel().rows.length;
	const totalRows = table.getRowModel().rows.length;

	return (
		<motion.div
			key="selected-info"
			initial={{ opacity: 0, y: -5 }}
			animate={{ opacity: 1, y: 0 }}
			className="flex w-full flex-col items-start gap-1 sm:flex-row sm:items-center sm:gap-2"
		>
			<span className="rounded-full bg-green-500 px-2.5 py-0.5 text-xs font-semibold text-white shadow">{selectedCount}</span>
			<span className="text-xs text-gray-300 sm:text-sm">
				selecionada{selectedCount > 1 ? 's' : ''} de {totalRows} apresentaç{totalRows > 1 ? 'ões' : 'ão'}
			</span>
		</motion.div>
	);
};

const DefaultRowsInfo = <TData,>({ table }: PaginationInfoProps<TData>) => {
	const totalRows = table.getRowModel().rows.length;
	const filteredRows = table.getFilteredRowModel().rows.length;

	return (
		<motion.div key="default-info" initial={{ opacity: 0, y: -5 }} animate={{ opacity: 1, y: 0 }} className="w-full text-xs text-gray-300 sm:text-sm">
			Mostrando <span className="font-semibold text-white drop-shadow">{totalRows}</span> de{' '}
			<span className="font-semibold text-white drop-shadow">{filteredRows}</span> apresentaç
			{filteredRows > 1 ? 'ões' : 'ão'}
		</motion.div>
	);
};

export const PaginationInfo = <TData,>({ table }: PaginationInfoProps<TData>) => {
	const hasSelectedRows = table.getSelectedRowModel().rows.length > 0;

	return (
		<div className="flex w-full flex-col items-start gap-1 text-xs text-muted-foreground sm:flex-row sm:items-center sm:gap-2">
			{hasSelectedRows ? <SelectedRowsInfo table={table} /> : <DefaultRowsInfo table={table} />}
		</div>
	);
};
