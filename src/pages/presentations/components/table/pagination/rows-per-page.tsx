import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/shadcnui/select';
import { useAtom } from 'jotai';
import { paginationPresentationState } from '../../../states/pagination.state';

export const RowsPerPageSelect = () => {
	const [pagination, setPagination] = useAtom(paginationPresentationState);

	return (
		<div className="flex items-center gap-2 rounded-md border border-muted-foreground/10 bg-black/30 px-2 py-1 shadow-sm">
			<p className={`hidden text-muted-foreground`}>Linhas por página:</p>
			<Select
				value={`${pagination.pageSize}`}
				onValueChange={(value) => {
					const newSize = Number(value);
					setPagination((prev) => ({ ...prev, pageSize: newSize, page: 1 }));
				}}
			>
				<SelectTrigger className="h-7 w-[60px] rounded border border-muted-foreground/10 bg-black/90 text-muted-foreground shadow-none hover:border-green-400/30 hover:bg-black/95 focus:ring-2 focus:ring-green-400/20">
					<SelectValue placeholder={pagination.pageSize} />
				</SelectTrigger>
				<SelectContent side="top" className="border border-muted-foreground/10 bg-black/95 text-muted-foreground shadow-lg">
					{[5, 10, 20, 30, 40, 50].map((pageSize) => (
						<SelectItem key={pageSize} value={`${pageSize}`} className="hover:bg-green-100/40 hover:text-white">
							{pageSize}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
		</div>
	);
};
