import { Table } from '@tanstack/react-table';
import { useAtom } from 'jotai';
import { paginationPresentationState } from '../../../states/pagination.state';
import { PaginationControls } from './controls';
import { PaginationInfo } from './info';
import { RowsPerPageSelect } from './rows-per-page';

interface IPresentationsDataTablePagination<TData> {
	table: Table<TData>;
	totalPages: number;
	isMobile?: boolean;
}

export const PresentationsDataTablePagination = <TData,>({ table, totalPages, isMobile }: IPresentationsDataTablePagination<TData>) => {
	const [pagination] = useAtom(paginationPresentationState);
	const dontShowPagination = totalPages <= 1;

	return (
		<div
			className={`${isMobile && 'rounded-lg'} flex flex-col items-center gap-4 bg-muted px-2 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-2`}
		>
			<div className={`flex w-full ${isMobile ? 'flex-row' : 'flex-col'} items-center gap-3 sm:flex-row sm:items-center sm:gap-4`}>
				<PaginationInfo table={table} />
				<RowsPerPageSelect />
			</div>
			{!dontShowPagination && (
				<div className="mt-3 flex w-full justify-center sm:mt-0 sm:w-auto sm:justify-end">
					<PaginationControls table={table} currentPage={pagination.page} totalPages={totalPages} />
				</div>
			)}
		</div>
	);
};
