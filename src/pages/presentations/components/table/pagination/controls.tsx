import { Button } from '@/components/shadcnui/button';
import { Table } from '@tanstack/react-table';
import { useAtom } from 'jotai';
import { LucideChevronLeft, LucideChevronRight, LucideChevronsLeft, LucideChevronsRight } from 'lucide-react';
import { paginationPresentationState } from '../../../states/pagination.state';

export const PaginationControls = <TData,>({ table, currentPage, totalPages }: { table: Table<TData>; currentPage: number; totalPages: number }) => {
	const [pagination, setPagination] = useAtom(paginationPresentationState);

	return (
		<div className="flex items-center space-x-3 rounded-md bg-black/20 px-2 py-1 shadow-inner">
			<div className="flex items-center space-x-1">
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30 lg:flex"
					onClick={() => setPagination((prev) => ({ ...prev, page: 1 }))}
					disabled={currentPage === 1}
				>
					<span className="sr-only">Ir para a primeira página</span>
					<LucideChevronsLeft className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30"
					onClick={() => setPagination((prev) => ({ ...prev, page: prev.page - 1 }))}
					disabled={currentPage === 1}
				>
					<span className="sr-only">Ir para a página anterior</span>
					<LucideChevronLeft className="h-4 w-4" />
				</Button>
				<div className="page-status flex min-w-[80px] items-center justify-center rounded-md bg-black/40 px-2 py-1 text-sm font-semibold text-white shadow-inner">
					{pagination.page}/{totalPages}
				</div>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30"
					onClick={() => setPagination((prev) => ({ ...prev, page: prev.page + 1 }))}
					disabled={currentPage === totalPages}
				>
					<span className="sr-only">Ir para a próxima página</span>
					<LucideChevronRight className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30 lg:flex"
					onClick={() => setPagination((prev) => ({ ...prev, page: table.getPageCount() }))}
					disabled={currentPage === totalPages}
				>
					<span className="sr-only">Ir para a última página</span>
					<LucideChevronsRight className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
};
