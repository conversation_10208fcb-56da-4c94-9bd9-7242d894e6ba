import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/shadcnui/alert-dialog';
import { Button } from '@/components/shadcnui/button';
import { PresentationActionsConfig } from '@/pages/presentations/types/table/index.type';
import { Edit, Trash2 } from 'lucide-react';

interface ActionsDropdownProps {
	itemId: string;
	config: PresentationActionsConfig;
	title: string;
}

export const ActionsDropdown = ({ itemId, config, title }: ActionsDropdownProps) => (
	<div className="flex items-center gap-1">
		<Button
			variant="ghost"
			size="icon"
			aria-label="Editar"
			disabled={typeof config.onEdit !== 'function'}
			onClick={() => config.onEdit?.(itemId)}
			className="p-2 text-primary hover:text-primary/80"
		>
			<Edit className="h-5 w-5" />
		</Button>
		<AlertDialog>
			<AlertDialogTrigger asChild>
				<Button
					variant="ghost"
					size="icon"
					aria-label="Excluir"
					disabled={typeof config.onDelete !== 'function'}
					className="p-2 text-red-500 hover:text-red-600"
				>
					<Trash2 className="h-5 w-5" />
				</Button>
			</AlertDialogTrigger>
			<AlertDialogContent className="border border-border/30 bg-muted/95 backdrop-blur-sm">
				<AlertDialogHeader>
					<div className="flex items-center gap-2">
						<Trash2 className="h-6 w-6 text-red-500" />
						<AlertDialogTitle className="text-lg font-semibold text-foreground">Confirmar exclusão</AlertDialogTitle>
					</div>
					<AlertDialogDescription className="mt-2 text-sm text-muted-foreground">
						Você está prestes a excluir a apresentação: <span className="font-semibold text-foreground">{title}</span>.<br />
						Esta ação <span className="font-semibold text-red-500">não pode ser desfeita</span>. Deseja continuar?
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter className="gap-2">
					<AlertDialogCancel className="flex items-center gap-1 border border-border/30 bg-background hover:bg-accent">
						<svg width="16" height="16" fill="none" viewBox="0 0 24 24" className="text-muted-foreground">
							<path stroke="currentColor" strokeWidth="2" d="M6 6l12 12M6 18L18 6" />
						</svg>
						Cancelar
					</AlertDialogCancel>
					<AlertDialogAction
						onClick={() => config.onDelete?.(itemId)}
						className="flex items-center gap-1 bg-destructive text-destructive-foreground hover:bg-destructive/90"
					>
						<Trash2 className="h-4 w-4" />
						Excluir
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	</div>
);
