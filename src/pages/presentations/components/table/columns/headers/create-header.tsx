import { Button } from '@/components/shadcnui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
import { cn } from '@/shared/lib/utils';
import { ArrowDownIcon, ArrowUpIcon, CaretSortIcon, EyeNoneIcon } from '@radix-ui/react-icons';
import { Column } from '@tanstack/react-table';
import { HTMLAttributes, ReactNode, useMemo } from 'react';

export type FilterType = 'orderAsc' | 'orderDesc' | 'hideColumn' | 'dateFilter' | 'textFilter' | 'clearFilter';

export interface IHeaderFilter {
	type: FilterType;
	label: string;
	icon?: ReactNode;
	onClick: () => void;
}

export interface CreateHeaderProps<TData, TValue> extends HTMLAttributes<HTMLDivElement> {
	column: Column<TData, TValue>;
	title: string;
	icon?: ReactNode;
	extraFilters?: IHeaderFilter[];
	active?: boolean;
}

export function CreateHeader<TData, TValue>(props: Readonly<CreateHeaderProps<TData, TValue>>) {
	const { column, title, icon, className, extraFilters, active = false, ...rest } = props;

	const sortIcon = useMemo(() => {
		const sortState = column.getIsSorted?.();
		if (sortState === 'desc') return <ArrowDownIcon className="ml-2 h-4 w-4" />;
		if (sortState === 'asc') return <ArrowUpIcon className="ml-2 h-4 w-4" />;
		return <CaretSortIcon className="ml-2 h-4 w-4" />;
	}, [column]);

	const defaultFilters: IHeaderFilter[] = useMemo(() => {
		if (extraFilters && extraFilters.length > 0) return [];
		const filters: IHeaderFilter[] = [
			{
				type: 'orderAsc',
				label: 'Crescente',
				icon: <ArrowUpIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />,
				onClick: () => {
					if (typeof column.toggleSorting === 'function') column.toggleSorting(false);
				},
			},
			{
				type: 'orderDesc',
				label: 'Decrescente',
				icon: <ArrowDownIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />,
				onClick: () => {
					if (typeof column.toggleSorting === 'function') column.toggleSorting(true);
				},
			},
		];

		if (column.getCanHide?.()) {
			filters.push({
				type: 'hideColumn',
				label: 'Ocultar coluna',

				icon: <EyeNoneIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />,
				onClick: () => {
					if (typeof column.toggleVisibility === 'function') column.toggleVisibility(false);
				},
			});
		}
		return filters;
	}, [column, extraFilters]);

	if ((!column.getCanSort?.() && !(extraFilters && extraFilters.length > 0)) || !active) {
		return (
			<div className={cn('flex items-center gap-2', className)} {...rest}>
				{icon}
				{title}
			</div>
		);
	}

	const finalFilters = [...defaultFilters, ...(extraFilters || [])];

	return (
		<div className={cn('flex items-center space-x-2', className)} {...rest}>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" size="sm" className="h-8 data-[state=open]:bg-accent">
						<span className="flex items-center gap-2">
							{icon}
							{title}
						</span>
						{sortIcon}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="start" className="w-48 text-white">
					{finalFilters.map((filter, index) => (
						<div key={filter.type + index}>
							{index > 0 && index % 2 === 0 && <DropdownMenuSeparator />}
							<DropdownMenuItem onClick={filter.onClick} className={cn('cursor-pointer')}>
								{filter.icon}
								{filter.label}
							</DropdownMenuItem>
						</div>
					))}
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
}
