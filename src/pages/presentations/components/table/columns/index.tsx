import { IPresentationData } from '@/pages/presentations/services/requests/presentations/find-all';
import { PresentationActionsConfig } from '@/pages/presentations/types/table/index.type';
import { formatDimensions } from '@/pages/presentations/utils/format-dimensions';
import { formatDate } from '@/shared/lib/utils/format-date';
import { ColumnDef } from '@tanstack/react-table';
import { createActionsColumn } from './columns/actions-column';
import { createDateColumn } from './columns/date-column';
import { createDimensionsColumn } from './columns/dimension-column';
import { createSelectColumn } from './columns/select-column';
import { createTextColumn } from './columns/text-column';

type Props = PresentationActionsConfig & {
	customColumns?: ColumnDef<IPresentationData>[];
};

export const createPresentationsColumns = ({ onEdit, onDelete, onView, onDuplicate, customColumns = [] }: Props): ColumnDef<IPresentationData>[] => [
	createSelectColumn<IPresentationData>(),
	createTextColumn<IPresentationData>({
		accessorKey: 'title',
		header: 'Título',
		className: 'font-medium text-white',
	}),
	createTextColumn<IPresentationData>({
		accessorKey: 'description',
		header: 'Descrição',
		className: 'text-gray-300',
	}),
	createDateColumn<IPresentationData>({
		accessorKey: 'createdAt',
		header: 'Criado em',
		formatter: formatDate,
	}),
	createDateColumn<IPresentationData>({
		accessorKey: 'updatedAt',
		header: 'Atualizado em',
		formatter: formatDate,
	}),
	createDimensionsColumn<IPresentationData>({
		id: 'dimensions',
		header: 'Dimensões',
		widthAccessor: 'width',
		heightAccessor: 'height',
		formatter: formatDimensions,
	}),
	createActionsColumn<IPresentationData>({
		id: 'actions',
		config: { onEdit, onDelete, onView, onDuplicate },
	}),
	...customColumns,
];
