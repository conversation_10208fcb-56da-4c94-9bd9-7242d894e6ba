import { DimensionsColumnOptions } from '@/pages/presentations/types/table/index.type';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowDown, ArrowUp, RotateCcw } from 'lucide-react';
import { CreateHeader, IHeaderFilter } from '../headers/create-header';

export const createDimensionsColumn = <T extends Record<string, any>>({
	id,
	header,
	widthAccessor,
	heightAccessor,
	formatter,
	enableSorting = true,
	enableHiding = true,
}: DimensionsColumnOptions<T>): ColumnDef<T> => {
	const calculateArea = (row: T): number => {
		const width = row[widthAccessor] ?? 0;
		const height = row[heightAccessor] ?? 0;
		return width * height;
	};

	const dimensionsSortingFn = (rowA: any, rowB: any): number => {
		const aArea = calculateArea(rowA.original);
		const bArea = calculateArea(rowB.original);
		return aArea - bArea;
	};

	const dimensionFilters: IHeaderFilter[] = [
		{
			type: 'orderDesc',
			label: 'Maior tamanho',
			icon: <ArrowDown className="mr-2 h-3.5 w-3.5" />,
			onClick: () => {},
		},
		{
			type: 'orderAsc',
			label: 'Menor tamanho',
			icon: <ArrowUp className="mr-2 h-3.5 w-3.5" />,
			onClick: () => {},
		},
		{
			type: 'clearFilter',
			label: 'Ordem padrão',
			icon: <RotateCcw className="mr-2 h-3.5 w-3.5" />,
			onClick: () => {},
		},
	];

	return {
		id,
		accessorFn: (row) => calculateArea(row),
		sortingFn: dimensionsSortingFn,
		enableSorting,
		enableHiding,

		header: ({ column }) => (
			<CreateHeader
				active={true}
				title={header}
				column={column}
				extraFilters={dimensionFilters.map((filter) => ({
					...filter,
					onClick:
						filter.type === 'orderDesc'
							? () => column.toggleSorting(true)
							: filter.type === 'orderAsc'
								? () => column.toggleSorting(false)
								: () => column.clearSorting(),
				}))}
			/>
		),

		cell: ({ row }) => {
			const width = row.original[widthAccessor] ?? 0;
			const height = row.original[heightAccessor] ?? 0;
			return <span className="text-gray-300">{formatter(width, height)}</span>;
		},
	};
};
