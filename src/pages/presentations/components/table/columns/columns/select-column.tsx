import { Checkbox } from '@/components/shadcnui/checkbox';
import { ColumnDef } from '@tanstack/react-table';

export function createSelectColumn<T>(): ColumnDef<T> {
	return {
		id: 'select',
		header: ({ table }) => {
			const isAllSelected = table.getIsAllPageRowsSelected();
			return (
				<Checkbox
					checked={isAllSelected}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Selecionar todos"
					className="translate-y-[2px] rounded-[5px]"
				/>
			);
		},
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Selecionar linha"
				className="translate-y-[2px] rounded-[5px]"
			/>
		),
		enableSorting: false,
		enableHiding: false,
	};
}
