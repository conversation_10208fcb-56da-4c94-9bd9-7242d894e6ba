import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/shadcnui/alert-dialog';
import { Button } from '@/components/shadcnui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
import { PresentationActionsConfig } from '@/pages/presentations/types/table/index.type';
import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Settings, Trash } from 'lucide-react';
import { ActionsDropdown } from '../actions/actions-dropdown';

interface ActionsColumnOptions {
	id: string;
	config: PresentationActionsConfig;
}

export const createActionsColumn = <T extends { id: string; title: string }>({ id, config }: ActionsColumnOptions): ColumnDef<T> => ({
	id,
	header: () => (
		<div className="b flex w-full items-end justify-center gap-2">
			<Settings className="h-4 w-4 text-gray-400 transition-transform duration-300 hover:rotate-180" />
		</div>
	),
	cell: ({ row, table }) => {
		const selectedRows = table.getSelectedRowModel().rows;
		const isSelected = row.getIsSelected();
		if (selectedRows.length > 1 && isSelected) {
			return (
				<div className="flex items-center justify-end">
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 rounded-full border border-[#232323] bg-[#232323] p-0 hover:bg-[#232323]/80"
							>
								<MoreHorizontal className="h-4 w-4 text-gray-300" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-[220px] rounded-lg border border-[#232323] bg-[#181818] p-1 shadow-lg">
							<AlertDialog>
								<AlertDialogTrigger asChild>
									<DropdownMenuItem
										onSelect={(e) => e.preventDefault()}
										className="flex cursor-pointer items-center justify-between rounded-md px-3 py-2 text-red-500 transition-colors hover:bg-[#232323]"
									>
										<span className="text-sm font-medium">Excluir Selecionados ({selectedRows.length})</span>
										<Trash className="ml-2 h-4 w-4" />
									</DropdownMenuItem>
								</AlertDialogTrigger>
								<AlertDialogContent className="border border-border/30 bg-muted/95 backdrop-blur-sm">
									<AlertDialogHeader>
										<div className="flex items-center gap-2">
											<Trash className="h-6 w-6 text-red-500" />
											<AlertDialogTitle className="text-lg font-semibold text-foreground">
												Confirmar exclusão em massa
											</AlertDialogTitle>
										</div>
										<AlertDialogDescription className="mt-2 text-sm text-muted-foreground">
											Você está prestes a excluir{' '}
											<span className="font-semibold text-foreground">{selectedRows.length} apresentações</span>.<br />
											Esta ação <span className="font-semibold text-red-500">não pode ser desfeita</span>. Deseja continuar?
										</AlertDialogDescription>
									</AlertDialogHeader>
									<AlertDialogFooter className="gap-2">
										<AlertDialogCancel className="flex items-center gap-1 border border-border/30 bg-background hover:bg-accent">
											<svg width="16" height="16" fill="none" viewBox="0 0 24 24" className="text-muted-foreground">
												<path stroke="currentColor" strokeWidth="2" d="M6 6l12 12M6 18L18 6" />
											</svg>
											Cancelar
										</AlertDialogCancel>
										<AlertDialogAction
											onClick={() => {
												if (config.onDelete) selectedRows.forEach((row) => config.onDelete!(row.original.id));

												table.toggleAllPageRowsSelected(false);
											}}
											className="flex items-center gap-1 bg-destructive text-destructive-foreground hover:bg-destructive/90"
										>
											<Trash className="h-4 w-4" />
											Excluir Todos
										</AlertDialogAction>
									</AlertDialogFooter>
								</AlertDialogContent>
							</AlertDialog>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			);
		}

		return (
			<div className="flex items-center justify-end gap-1">
				<ActionsDropdown itemId={row.original.id} title={row.original.title} config={config} />
			</div>
		);
	},
	enableSorting: false,
	enableHiding: false,
});
