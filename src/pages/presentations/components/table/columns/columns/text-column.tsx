import { TextColumnOptions } from '@/pages/presentations/types/table/index.type';
import { ColumnDef } from '@tanstack/react-table';
import { CreateHeader } from '../headers/create-header';

export const createTextColumn = <T,>({
	accessorKey,
	header,
	className = 'text-gray-300',
	enableSorting = true,
	enableHiding = true,
}: TextColumnOptions<T>): ColumnDef<T> => ({
	accessorKey,
	header: ({ column }) => <CreateHeader title={header} column={column} />,
	cell: ({ getValue }) => {
		const value = getValue();
		let displayValue: string;
		if (value == null) {
			displayValue = '';
		} else if (typeof value === 'object') {
			displayValue = JSON.stringify(value);
		} else if (typeof value === 'string') {
			displayValue = value;
		} else {
			displayValue = JSON.stringify(value);
		}
		return <span className={className}>{displayValue}</span>;
	},
	enableSorting,
	enableHiding,
});
