import { Label } from '@/components/shadcnui/label';
import { ICreatePresentation } from '@/pages/presentations/validation/create-presentation.form';
import { Radio, RadioGroup } from '@nextui-org/react';
import { RadioIcon } from 'lucide-react';
import { Controller, UseFormReturn } from 'react-hook-form';

export enum DimensionType {
	PRESET = 'preset',
	CUSTOM = 'custom',
}

interface DimensionTypeSelectorProps {
	methods: UseFormReturn<ICreatePresentation>;
}

export function DimensionTypeSelector({ methods }: Readonly<DimensionTypeSelectorProps>) {
	const { control } = methods;

	return (
		<div className="flex w-full flex-col gap-2">
			<Label className="flex flex-row items-center gap-1 text-white">
				<RadioIcon className="pointer-events-none h-4 w-4 text-green-500" />
				Tipo de Dimensão:
			</Label>
			<Controller
				name="sizeType"
				control={control}
				defaultValue={DimensionType.PRESET}
				render={({ field: { onChange, value } }) => (
					<div className="flex items-center gap-4">
						<RadioGroup orientation="horizontal" value={value} onChange={onChange} className="flex items-center gap-4">
							<Radio value={DimensionType.PRESET} id="sizeTypePreset">
								Predefinido
							</Radio>
							<Radio value={DimensionType.CUSTOM} id="sizeTypeCustom">
								Personalizado
							</Radio>
						</RadioGroup>
					</div>
				)}
			/>
		</div>
	);
}
