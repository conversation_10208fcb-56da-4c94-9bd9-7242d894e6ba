import { Button } from '@/components/shadcnui/button';
import { ICreatePresentation } from '@/pages/presentations/validation/create-presentation.form';
import { Presentation } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

interface SubmitButtonProps {
	methods: UseFormReturn<ICreatePresentation>;
	label?: string;
}

export function SubmitButton({ methods, label = 'Criar Apresentação' }: Readonly<SubmitButtonProps>) {
	const { formState } = methods;
	const isSubmitting = formState.isSubmitting;
	const isDirty = formState.isDirty;

	return (
		<div className="mt-2 flex w-full justify-end">
			<Button
				type="submit"
				disabled={!isDirty || isSubmitting}
				className="flex h-12 w-[220px] items-center justify-center gap-2 rounded-lg bg-green-600 text-base font-semibold text-white shadow-md transition hover:bg-green-500 disabled:cursor-not-allowed disabled:opacity-50"
			>
				<Presentation className="h-5 w-5" />
				{isSubmitting ? 'Criando...' : label}
			</Button>
		</div>
	);
}
