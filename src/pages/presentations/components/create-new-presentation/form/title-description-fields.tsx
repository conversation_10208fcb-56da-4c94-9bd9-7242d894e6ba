import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { ICreatePresentation } from '@/pages/presentations/validation/create-presentation.form';
import { AlignLeft, FileText } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

interface TitleDescriptionFieldsProps {
	methods: UseFormReturn<ICreatePresentation>;
}

export function TitleDescriptionFields({ methods }: Readonly<TitleDescriptionFieldsProps>) {
	const { register, formState } = methods;

	return (
		<div className="flex w-full justify-between gap-4">
			<div className="flex w-1/2 flex-col gap-2">
				<Label htmlFor="title" className="flex flex-row items-center gap-1 text-white">
					<FileText className="h-4 w-4 text-primary" />
					Título
				</Label>
				<Input
					id="title"
					placeholder="Ex: Apresentação do Telão"
					{...register('title')}
					className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
					autoFocus
				/>
				{formState.errors.title && <span className="text-sm text-red-500">{formState.errors.title.message}</span>}
			</div>
			<div className="flex w-1/2 flex-col gap-2">
				<Label htmlFor="description" className="flex flex-row items-center gap-1 text-white">
					<AlignLeft className="h-4 w-4 text-primary" />
					Descrição
				</Label>
				<Input
					id="description"
					placeholder="Ex: Apresentação do Telão"
					{...register('description')}
					className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
				/>
				{formState.errors.description && <span className="text-sm text-red-500">{formState.errors.description.message}</span>}
			</div>
		</div>
	);
}
