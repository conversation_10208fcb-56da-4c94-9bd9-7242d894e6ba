import { useIsMobile } from '@/hooks/use-is-mobile';
import { useCreatePresentationForm } from '@/pages/presentations/hooks/form/create-presentation-form.hook';
import { ICreatePresentation, PresentationSizePresets } from '@/pages/presentations/validation/create-presentation.form';
import { useEffect } from 'react';
import { AutoOpenCheckbox } from './auto-open-check';
import { CustomDimensionFields } from './custom-dimension-fields';
import { DimensionType, DimensionTypeSelector } from './dimension-type-selector';
import { PresetSelector } from './preset-selector';
import { SubmitButton } from './submit-button';
import { TitleDescriptionFields } from './title-description-fields';

interface ICreateNewPresentationFormProps {
	onSubmit?: (data: ICreatePresentation) => void;
}

export function CreateNewPresentationForm({ onSubmit }: Readonly<ICreateNewPresentationFormProps>) {
	const methods = useCreatePresentationForm();
	const { watch, handleSubmit, setValue } = methods;
	const isMobile = useIsMobile(640);

	const sizeType = watch('sizeType');
	const selectedPreset = watch('preset');

	const handleCreate = async (data: ICreatePresentation) => {
		try {
			if (data.sizeType === DimensionType.PRESET && data.preset) {
				const { width, height } = PresentationSizePresets[data.preset];
				data.width = width;
				data.height = height;
			}
			onSubmit?.(data);
		} catch (error) {
			console.error('Erro ao criar apresentação:', error);
		}
	};

	useEffect(() => {
		if (selectedPreset) {
			const { width, height } = PresentationSizePresets[selectedPreset];
			setValue('width', width);
			setValue('height', height);
		}
	}, [selectedPreset, setValue]);

	return (
		<form onSubmit={handleSubmit(handleCreate)} className="flex flex-col items-center gap-6 rounded-md">
			<TitleDescriptionFields methods={methods} />
			<DimensionTypeSelector methods={methods} />
			{sizeType === DimensionType.CUSTOM && <CustomDimensionFields methods={methods} />}
			{sizeType === DimensionType.PRESET && <PresetSelector methods={methods} />}
			{!isMobile && <AutoOpenCheckbox methods={methods} />}
			<SubmitButton methods={methods} />
		</form>
	);
}
