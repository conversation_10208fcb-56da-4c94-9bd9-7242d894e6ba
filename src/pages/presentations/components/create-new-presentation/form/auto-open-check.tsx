import { Checkbox } from '@/components/shadcnui/checkbox';
import { Label } from '@/components/shadcnui/label';
import { ICreatePresentation } from '@/pages/presentations/validation/create-presentation.form';
import { Controller, UseFormReturn } from 'react-hook-form';

interface AutoOpenCheckboxProps {
	methods: UseFormReturn<ICreatePresentation>;
}

export function AutoOpenCheckbox({ methods }: Readonly<AutoOpenCheckboxProps>) {
	const { control } = methods;

	return (
		<div className="flex w-full items-center gap-2">
			<Controller
				name="autoOpen"
				control={control}
				defaultValue={false}
				render={({ field: { onChange, value } }) => (
					<Checkbox id="autoOpen" checked={value} onCheckedChange={onChange} className="h-5 w-5 rounded-md border border-white/10 bg-black/20" />
				)}
			/>
			<Label htmlFor="autoOpen">Abrir automaticamente após a criação</Label>
		</div>
	);
}
