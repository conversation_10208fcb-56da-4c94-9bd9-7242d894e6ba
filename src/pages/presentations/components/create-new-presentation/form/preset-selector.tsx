import { Label } from '@/components/shadcnui/label';
import { ICreatePresentation, PresentationSizePresets } from '@/pages/presentations/validation/create-presentation.form';
import { Select, SelectItem } from '@nextui-org/react';
import { Controller, UseFormReturn } from 'react-hook-form';

interface PresetSelectorProps {
	methods: UseFormReturn<ICreatePresentation>;
}

export function PresetSelector({ methods }: Readonly<PresetSelectorProps>) {
	const { control } = methods;

	return (
		<Controller
			name="preset"
			control={control}
			render={({ field }) => (
				<div className="flex w-full flex-col gap-2">
					<Label>Tamanho Predefinido</Label>
					<Select
						placeholder="Selecione um tamanho"
						{...field}
						onChange={field.onChange}
						classNames={{
							trigger: 'rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0',
						}}
					>
						{Object.entries(PresentationSizePresets).map(([key, { width, height }]) => (
							<SelectItem key={key} value={key}>
								{`${key} (${width} x ${height})`}
							</SelectItem>
						))}
					</Select>
				</div>
			)}
		/>
	);
}
