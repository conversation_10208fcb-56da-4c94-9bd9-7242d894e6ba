import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { ICreatePresentation } from '@/pages/presentations/validation/create-presentation.form';
import { MoveHorizontal, MoveVertical } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

interface CustomDimensionFieldsProps {
	methods: UseFormReturn<ICreatePresentation>;
}

export function CustomDimensionFields({ methods }: Readonly<CustomDimensionFieldsProps>) {
	const { register, formState } = methods;

	return (
		<div className="flex w-full justify-between gap-4">
			<div className="flex w-1/2 flex-col gap-2">
				<Label htmlFor="width" className="flex flex-row items-center gap-1 text-white">
					<MoveHorizontal className="h-4 w-4 text-green-500" />
					Largura
				</Label>
				<Input
					id="width"
					type="number"
					placeholder="Ex: 1920"
					{...register('width', { valueAsNumber: true })}
					className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
				/>
				{formState.errors.width && <span className="text-sm text-red-500">{formState.errors.width.message}</span>}
			</div>

			<div className="flex w-1/2 flex-col gap-2">
				<Label htmlFor="height" className="flex flex-row items-center gap-1 text-white">
					<MoveVertical className="h-4 w-4 text-green-500" />
					Altura
				</Label>
				<Input
					id="height"
					type="number"
					placeholder="Ex: 1080"
					{...register('height', { valueAsNumber: true })}
					className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
				/>
				{formState.errors.height && <span className="text-sm text-red-500">{formState.errors.height.message}</span>}
			</div>
		</div>
	);
}
