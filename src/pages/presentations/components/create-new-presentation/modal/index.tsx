import { useCreatePresentation } from '@/pages/presentations/hooks/crud/create.hook';
import { ICreatePresentation } from '@/pages/presentations/validation/create-presentation.form';
import { Modal, ModalBody, ModalContent, ModalHeader, ModalProps } from '@nextui-org/react';
import { Monitor } from 'lucide-react';
import { CreateNewPresentationForm } from '../form';

interface ICreatePresentationProps extends Omit<ModalProps, 'children'> {
	onClose?: () => void;
}

export const CreatePresentationModal = ({ onClose, ...modalConfig }: ICreatePresentationProps) => {
	const { createPresentation, isLoading } = useCreatePresentation(() => onClose?.());

	const handleSubmit = async (data: ICreatePresentation) => {
		await createPresentation({
			data: {
				title: data.title,
				description: data.description,
				width: Number(data.width),
				height: Number(data.height),
			},
			autoOpen: data.autoOpen,
		});
		onClose?.();
	};

	return (
		<Modal {...modalConfig} isDismissable={!isLoading} className="rounded-xl border border-[#232728] bg-muted shadow-lg">
			<ModalContent>
				<>
					<ModalHeader className="flex flex-col gap-2 pb-0">
						<div className="flex items-center gap-2">
							<Monitor className="h-6 w-6 text-primary" />
							<h1 className="text-lg font-semibold text-white">Adicionar Apresentação</h1>
						</div>
						<h3 className="inline-flex items-center gap-2 text-sm font-normal text-muted-foreground">
							Utilize os campos abaixo para criar uma nova apresentação
						</h3>
					</ModalHeader>
					<ModalBody className="pb-6 pt-2">
						<CreateNewPresentationForm onSubmit={handleSubmit} />
					</ModalBody>
				</>
			</ModalContent>
		</Modal>
	);
};
