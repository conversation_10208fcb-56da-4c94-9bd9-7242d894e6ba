import { useNavigate } from 'react-router-dom';
import { TablePresentations } from './components/table';
import { createPresentationsColumns } from './components/table/columns';
import { useDeletePresentation } from './hooks/crud/delete.hook';
import { useFindAllPresentations } from './hooks/crud/find-all.hook';

const Presentations = () => {
	const { data, isLoading } = useFindAllPresentations();
	const { deletePresentation } = useDeletePresentation();
	const navigate = useNavigate();

	const columns = createPresentationsColumns({
		onEdit: (id: string) => navigate(`/new/${id}`),
		onDelete: (id: string) => void deletePresentation(id),
		onView: null,
		onDuplicate: null,
	});

	const defaultData = {
		data: [],
		currentPage: 1,
		pageSize: 10,
		totalCount: 0,
		totalPages: 1,
	};

	return (
		<TablePresentations
			data={data?.success ? data.data : defaultData}
			columns={columns}
			isLoading={isLoading}
			messageError={!data?.success ? data?.data.message : undefined}
		/>
	);
};

export default Presentations;
