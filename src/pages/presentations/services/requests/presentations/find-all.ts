import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { IPagination } from '@/shared/types/utils/pagination.type';
import { PRESENTATIONS_ROUTES } from '../../endpoints';

export interface IPresentationFindAll extends IPagination {
	data: IPresentationData[];
}

export interface IPresentationData {
	id: string;
	createdAt: string;
	updatedAt: string;
	title: string;
	description: string;
	width: number;
	height: number;
}

export interface IFindAllProps {
	page: number;
	pageSize: number;
	search?: string;
}

export const findAllPresentations = async ({ page, pageSize, search }: IFindAllProps): Promise<ApiResponse<IPresentationFindAll>> => {
	try {
		const response = await api.get<IPresentationFindAll>(PRESENTATIONS_ROUTES.FIND_ALL({ page, pageSize, search }));
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
