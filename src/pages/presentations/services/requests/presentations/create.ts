import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { PRESENTATIONS_ROUTES } from '../../endpoints';

export interface ICreatePresentationDto {
	title: string;
	description: string;
	width: number;
	height: number;
}

export interface ICreatePresentationReturn {
	id: string;
	title: string;
	description: string;
	width: number;
	height: number;
	createdAt: string;
	updatedAt: string;
}

export const createPresentation = async ({ items }: { items: ICreatePresentationDto }): Promise<ApiResponse<ICreatePresentationReturn>> => {
	try {
		const response = await api.post(PRESENTATIONS_ROUTES.CREATE, items);
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
