import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { PRESENTATIONS_ROUTES } from '../../endpoints';

export const deletePresentationRequest = async (id: string): Promise<ApiResponse<string>> => {
	try {
		const response = await api.delete<string>(PRESENTATIONS_ROUTES.DELETE({ id }));

		return { success: true, data: 'Apresentação excluida com sucesso', status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
