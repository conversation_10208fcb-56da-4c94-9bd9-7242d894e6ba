import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { createPresentationSchema, ICreatePresentation } from '../../validation/create-presentation.form';

export const useCreatePresentationForm = () => {
	return useForm<ICreatePresentation>({
		resolver: zodResolver(createPresentationSchema),
		defaultValues: {
			title: '',
			description: '',
			width: undefined,
			height: undefined,
			sizeType: 'preset',
			preset: undefined,
			autoOpen: false,
		},
	});
};
