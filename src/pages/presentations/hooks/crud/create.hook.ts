import { createPresentation, ICreatePresentationDto } from '@/pages/presentations/services/requests/presentations/create';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface CreatePresentationParams {
	data: ICreatePresentationDto;
	autoOpen: boolean;
}

export const useCreatePresentation = (onSuccess?: () => void) => {
	const navigate = useNavigate();
	const queryClient = useQueryClient();

	const { mutateAsync, isPending } = useMutation({
		mutationKey: ['create-presentation'],
		mutationFn: async ({ data, autoOpen }: CreatePresentationParams) => {
			const response = await createPresentation({ items: data });
			if (!response.success) throw new Error(response.data.message);
			return { data: response.data, autoOpen };
		},
		onSuccess: ({ data, autoOpen }) => {
			toast.success('Apresentação criada com sucesso!');
			onSuccess?.();
			queryClient.invalidateQueries({
				queryKey: ['presentations'],
				exact: false,
			});

			if (autoOpen) navigate(`/new/${data.id}`);
		},
		onError: (error: Error) => {
			toast.error(error.message);
		},
	});

	return {
		createPresentation: mutateAsync,
		isLoading: isPending,
	};
};
