import { z } from 'zod';

export const PresentationSizePresets = {
	HD: { width: 1280, height: 720 },
	'Full HD': { width: 1920, height: 1080 },
	'2K': { width: 2048, height: 1080 },
	'4K': { width: 3840, height: 2160 },
	'8K': { width: 7680, height: 4320 },
} as const;

export type PresentationSizeType = 'preset' | 'custom';

export const createPresentationSchema = z
	.object({
		title: z.string().min(1, { message: 'Título é obrigatório' }).max(100, { message: 'Título deve ter no máximo 100 caracteres' }),
		description: z.string().min(1, { message: 'Descrição é obrigatória' }).max(100, { message: 'Descrição deve ter no máximo 100 caracteres' }),
		sizeType: z.enum(['preset', 'custom'] as const),
		preset: z.enum(['HD', 'Full HD', '2K', '4K', '8K'] as const).optional(),
		width: z.coerce.number().optional(),
		height: z.coerce.number().optional(),
		autoOpen: z.boolean().default(false),
	})
	.refine(
		(data) => {
			if (data.sizeType === 'preset') {
				return data.preset !== undefined;
			} else {
				return data.width !== undefined && data.height !== undefined;
			}
		},
		{
			message: 'Você precisa selecionar um tamanho predefinido ou fornecer dimensões personalizadas',
		},
	);

export type ICreatePresentation = z.infer<typeof createPresentationSchema>;
