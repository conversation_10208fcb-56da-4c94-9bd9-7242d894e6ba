import { atom } from 'jotai';

interface IPaginationState {
	page: number;
	pageSize: number;
}

export const paginationPresentationState = atom<IPaginationState>({
	page: 1,
	pageSize: 10,
});

export const paginationSetSizeState = atom(
	(pagination: IPaginationState) => pagination.pageSize,
	(get, set, newSize: number) => {
		const pagination = get(paginationPresentationState);
		set(paginationPresentationState, { ...pagination, pageSize: newSize });
	},
);
