import { Button } from '@/components/shadcnui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/shadcnui/card';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { useLoginForm } from '@/contexts/auth/hooks/login-form.hook';
import { useLoginMutation } from '@/contexts/auth/hooks/login-mutation.hook';
import { CircularProgress, Image, Tab, Tabs } from '@nextui-org/react';
import { LucideEye, LucideEyeOff } from 'lucide-react';
import { useState } from 'react';

export function Login() {
	const methods = useLoginForm();
	const { login, isLoading } = useLoginMutation();

	const [isVisible, setIsVisible] = useState(false);

	function alterVisibility(event: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
		event.preventDefault();

		const input = document.querySelector('#password') as HTMLInputElement | null;

		if (input?.getAttribute('type') === 'password') {
			setIsVisible(true);
			input.setAttribute('type', 'text');
		} else {
			setIsVisible(false);
			input?.setAttribute('type', 'password');
		}
	}

	function renderAdminForm() {
		return (
			<form
				onSubmit={methods.handleSubmit((data) => {
					login(data);
				})}
				className="flex w-full flex-col gap-4 text-center"
			>
				<CardContent className="flex flex-col gap-4 p-0">
					<Label htmlFor="user" className="relative flex w-full flex-col gap-2 text-start">
						Digite seu usuário
						<Input id="user" {...methods.register('user')} className="h-11" autoComplete="off" type="text" placeholder="Digite seu usuário" />
					</Label>
					<Label htmlFor="password" className="relative flex w-full flex-col gap-2 text-start">
						Senha
						<fieldset className="relative flex w-full">
							<Input
								id="password"
								className="h-11"
								type="password"
								{...methods.register('password')}
								autoComplete="off"
								placeholder="Digite sua senha"
								maxLength={25}
								size={25}
							/>
							<Button
								type="button"
								variant="ghost"
								onClick={alterVisibility}
								className="absolute right-1.5 top-1/2 flex -translate-y-1/2 items-center justify-center rounded-sm"
							>
								<LucideEyeOff data-hide={isVisible} className="data-[hide=true]:hidden" size={14} />
								<LucideEye data-hide={!isVisible} className="data-[hide=true]:hidden" size={14} />
							</Button>
						</fieldset>
					</Label>

					{isLoading ? (
						<CircularProgress className="mx-auto" aria-label="Loading" />
					) : (
						<Button type="submit" className="mx-auto flex h-11 w-full items-center justify-center">
							Acessar
						</Button>
					)}
				</CardContent>
			</form>
		);
	}

	return (
		<main className="relative flex h-screen w-full items-center justify-center gap-2 self-center p-4 md:justify-evenly">
			<Card className="max-w-[450px] space-y-4 p-4">
				<CardHeader>
					<Image src="/assets/svgs/logo.svg" />
				</CardHeader>
				<Tabs
					variant="solid"
					placement="top"
					classNames={{
						base: 'w-full',
						tabList: 'w-full',
						tabContent: 'w-full',
						panel: 'space-y-4 px-0',
					}}
				>
					<Tab key={'admin'} title="Administrador">
						<CardContent className="flex flex-col gap-4 p-2">{renderAdminForm()}</CardContent>
					</Tab>
					<Tab key={'device'} title="Dispositivo">
						<CardContent className="flex flex-col gap-4 p-2">
							<Label>
								Token
								<Input />
							</Label>
						</CardContent>
					</Tab>
				</Tabs>
			</Card>
			<Image className="hidden aspect-auto h-auto md:block" src="/assets/svgs/illustration.svg" />
		</main>
	);
}
