import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { DEVICES_ENDPOINTS } from '../endpoints';

export const removeDeviceTokenRequest = async (id: number): Promise<ApiResponse<any>> => {
	try {
		const { status, data } = await api.delete(DEVICES_ENDPOINTS.REMOVE_DEVICE_TOKEN(id));
		return { success: true, status, data };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
