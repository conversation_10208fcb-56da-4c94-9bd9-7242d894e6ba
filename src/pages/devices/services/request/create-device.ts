import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse, IGlobalMessageReturn } from '@/shared/types/response';
import { DEVICES_ENDPOINTS } from '../endpoints';

export interface CreateDeviceDto {
	name: string;
	type: string;
	width: number;
	height: number;
	resolution: string;
	brand: string;
	model: string;
	latitude: number;
	longitude: number;
}

export const createDeviceRequest = async (payload: CreateDeviceDto): Promise<ApiResponse<IGlobalMessageReturn>> => {
	try {
		const { status, data } = await api.post(DEVICES_ENDPOINTS.CREATE_DEVICE(), payload);
		return { success: true, status, data };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
