import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { DEVICES_ENDPOINTS } from '../endpoints';

export interface IDevice {
	id: string;
	createdAt: string;
	updatedAt: string;
	name: string;
	status: boolean;
	type: string;
	width: number;
	height: number;
	resolution: string;
	brand: string;
	model: string;
	token: string;
	token_generated_at: string;
	latitude: number;
	longitude: number;
	token_expires_at: string;
	// programings?: any[];
}

export interface IDeviceFindAllResponse {
	data: IDevice[];
	currentPage: number;
	pageSize: number;
	totalPages: number;
	totalCount: number;
}

export interface IFindAllDevicesParams {
	page: number;
	pageSize: number;
	search: string;
}

export const findAllDevicesRequest = async (params: IFindAllDevicesParams): Promise<ApiResponse<IDeviceFindAllResponse>> => {
	try {
		const { status, data } = await api.get(DEVICES_ENDPOINTS.FIND_ALL_DEVICES(params));
		return { success: true, status, data };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
