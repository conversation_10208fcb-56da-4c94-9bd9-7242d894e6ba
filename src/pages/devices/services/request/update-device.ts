import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { DEVICES_ENDPOINTS } from '../endpoints';
import { CreateDeviceDto } from './create-device';

export const updateDeviceRequest = async (payload: CreateDeviceDto, id: number): Promise<ApiResponse<any>> => {
	try {
		const { status, data } = await api.put(DEVICES_ENDPOINTS.UPDATE_DEVICE(id), payload);
		return { success: true, status, data };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
