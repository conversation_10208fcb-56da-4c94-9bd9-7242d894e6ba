import { useDisclosure } from '@nextui-org/react';
import { useState } from 'react';
import { createDevicesColumns } from './components/table/components/columns/devices-columns';
import { DeleteDeviceModal } from './components/table/components/modals/delete-device-modal';
import { UpdateDeviceModal } from './components/table/components/modals/edit-device-modal';
import { DevicesDataTable } from './components/table/components/table/devices-data-table';
import { useDeleteDevice } from './hooks/crud/delete-device.hook';
import { useFindAllDevices } from './hooks/crud/find-all.hook';
import { useGenerateToken } from './hooks/crud/generate-token.hook';
import { useRemoveToken } from './hooks/crud/remove-token.hook';
import { IDevice, IDeviceFindAllResponse } from './services/request/find-all';

const initialDeviceData = {
	data: [] as IDevice[],
	currentPage: 1,
	pageSize: 50,
	totalCount: 0,
	totalPages: 1,
};

export function Devices() {
	const { data, isLoading } = useFindAllDevices();
	const generateToken = useGenerateToken();
	const removeToken = useRemoveToken();
	const editModal = useDisclosure();
	const [selectedDeviceId, setSelectedDeviceId] = useState<number | null>(null);
	const [deleteModalOpen, setDeleteModalOpen] = useState(false);
	const [devicesToDelete, setDevicesToDelete] = useState<Array<{ id: string; name: string }>>([]);
	const deleteDeviceMutation = useDeleteDevice();

	const handleEditDevice = (device: IDevice) => {
		setSelectedDeviceId(Number(device.id));
		editModal.onOpen();
	};

	const handleCloseEditModal = () => {
		setSelectedDeviceId(null);
		editModal.onClose();
	};

	const handleDeleteDevices = async ({ id }: { id: string }) => {
		if (!data?.success) return;
		const deviceIds = id.split(',');
		const currentDevices = data.data as IDeviceFindAllResponse;
		const devicesToDelete = deviceIds
			.map((deviceId) => {
				const device = currentDevices.data.find((d: IDevice) => d.id === deviceId);
				return device ? { id: deviceId, name: device.name } : null;
			})
			.filter((device): device is { id: string; name: string } => device !== null);

		if (devicesToDelete.length > 0) {
			setDevicesToDelete(devicesToDelete);
			setDeleteModalOpen(true);
		}
	};

	const handleConfirmDelete = async () => {
		try {
			for (const device of devicesToDelete) {
				await deleteDeviceMutation.mutateAsync(Number(device.id));
			}
			setDeleteModalOpen(false);
			setDevicesToDelete([]);
		} catch (error) {
			console.error('Erro ao excluir dispositivos:', error);
		}
	};

	const handleCloseDeleteModal = () => {
		setDeleteModalOpen(false);
		setDevicesToDelete([]);
	};

	const handleNewToken = async ({ id }: { id: string }) => {
		try {
			await generateToken.mutateAsync(Number(id));
		} catch (error) {
			console.error('Erro ao gerar novo token:', error);
		}
	};

	const handleRemoveToken = async ({ id }: { id: string }) => {
		try {
			await removeToken.mutateAsync(Number(id));
		} catch (error) {
			console.error('Erro ao remover token:', error);
		}
	};

	const columns = createDevicesColumns({
		onEdit: handleEditDevice,
		onDelete: handleDeleteDevices,
		onNewToken: handleNewToken,
		onRemoveToken: handleRemoveToken,
	});

	return (
		<>
			<DevicesDataTable
				columns={columns}
				data={data?.success ? data.data : initialDeviceData}
				isLoading={isLoading}
				messageError={!data?.success ? data?.data.message : undefined}
			/>
			{selectedDeviceId && (
				<UpdateDeviceModal
					isOpen={editModal.isOpen}
					onOpenChange={editModal.onOpenChange}
					onClose={handleCloseEditModal}
					deviceId={selectedDeviceId}
					backdrop="blur"
					size="3xl"
				/>
			)}
			<DeleteDeviceModal
				isOpen={deleteModalOpen}
				onClose={handleCloseDeleteModal}
				onConfirm={handleConfirmDelete}
				deviceName={devicesToDelete.map((d) => d.name).join(', ')}
				isLoading={deleteDeviceMutation.isPending}
			/>
		</>
	);
}
