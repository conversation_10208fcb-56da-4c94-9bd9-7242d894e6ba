// import { useConfirm } from '@/components/confirm-dialog/use-confirm';
// import api from '@/shared/lib/api/api';
// import { useQueryClient } from '@tanstack/react-query';
// import { toast } from 'sonner';

// const useDeviceTokenActions = () => {
// 	const confirm = useConfirm();
// 	const queryClient = useQueryClient();

// 	const handleCreateToken = async (device_id: number, device_name: string) => {
// 		const result = await confirm({
// 			title: 'Gera<PERSON> Acesso',
// 			description: 'Deseja gerar um novo token de acesso?',
// 		});

// 		if (result) {
// 			await api
// 				.post(`devices/admin/token/${device_id}`)
// 				.then(async () => {
// 					toast.success(`Token gerado com sucesso para o dispositivo: ${device_name}!`);
// 					return await queryClient.invalidateQueries({ queryKey: ['get-all-devices'] });
// 				})
// 				.catch((err) => toast.error(err.response.data.message));
// 		}
// 	};

// 	const handleRemoveToken = async (device_id: number, device_name: string) => {
// 		const result = await confirm({
// 			title: 'Remover Token',
// 			description: `Deseja remover o token de acesso do dispositivo: ${device_name}?`,
// 		});

// 		if (result) {
// 			await api
// 				.delete(`devices/admin/token/${device_id}`)
// 				.then(async () => {
// 					toast.success(`Token removido com sucesso do dispositivo: ${device_name}!`);
// 					return await queryClient.invalidateQueries({ queryKey: ['get-all-devices'] });
// 				})
// 				.catch((err) => toast.error(err.response.data.message));
// 		}
// 	};

// 	return { handleCreateToken, handleRemoveToken };
// };

// export default useDeviceTokenActions;
