import { LucideFullscreen, LucideLaptop, LucideMonitor, LucideTv2 } from 'lucide-react';
import { DeviceTypeSchema } from '../validators/create-device-schema.form';

export const changeIconByDeviceType = (device_type: DeviceTypeSchema) => {
	switch (device_type) {
		case 'TV':
			return <LucideTv2 size={18} />;
		case 'Computador':
			return <LucideMonitor size={18} />;
		case 'Notebook':
			return <LucideLaptop size={18} />;
		case 'Telão':
			return <LucideFullscreen size={18} />;
		default:
			return <LucideTv2 size={18} />;
	}
};
