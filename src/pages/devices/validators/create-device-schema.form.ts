import { z } from 'zod';

export const DeviceTypeSchema = z.enum(['TV', 'Telão', 'Notebook', 'Computador'], { required_error: 'Campo Tipo é obrigatório' });
export type DeviceTypeSchema = z.infer<typeof DeviceTypeSchema>;

export const NewDeviceFormSchema = z.object({
	name: z
		.string({ required_error: `Campo nome é obrigatório` })
		.min(2, { message: 'Campo nome deve ter no mínimo 2 caracteres' })
		.min(1, { message: 'Campo nome não pode ser vazio' }),
	type: DeviceTypeSchema,
	width: z.number().positive(),
	height: z.number().positive(),
	resolution: z.string({ required_error: `Campo resolução é obrigatório ` }).min(1, { message: 'Campo resolução não pode ser vazio' }),
	brand: z.string({ required_error: `Campo marca é obrigatório ` }).min(1, { message: 'Campo marca não pode ser vazio' }),
	model: z.string({ required_error: `Campo modelo é obrigatório ` }).min(1, { message: 'Campo modelo não pode ser vazio' }),
	location: z.tuple([z.number({ required_error: `Coordenada X é obrigatória` }), z.number({ required_error: `Coordenada Y é obrigatória` })], {
		required_error: 'Campo localização deve conter dois números (x e y)',
	}),
});
export type IDeviceSchema = z.infer<typeof NewDeviceFormSchema>;
