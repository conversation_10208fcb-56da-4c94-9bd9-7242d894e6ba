import { useMutation, useQueryClient } from '@tanstack/react-query';
import { DEVICES_KEYS } from '../../data/query-keys';
import { deleteDeviceRequest } from '../../services/request/delete-device';

export const useDeleteDevice = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (id: number) => deleteDeviceRequest(id),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: DEVICES_KEYS.FIND_ALL_DEVICES({ page: 1, pageSize: 10, search: '' }) });
		},
	});
};
