import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { generateDeviceTokenRequest } from '../../services/request/generate-token';

export const useGenerateToken = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: number) => {
			const response = await generateDeviceTokenRequest(id);
			if (!response.success) {
				throw new Error(response.data.message);
			}
			return response.data;
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success('Token gerado com sucesso!');
			queryClient.invalidateQueries({ queryKey: ['find-all-devices'], exact: false });
		},
		onError: (error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
