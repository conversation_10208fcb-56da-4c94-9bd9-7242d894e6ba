import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { CreateDeviceDto, createDeviceRequest } from '../../services/request/create-device';

export const useCreateDevice = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (payload: CreateDeviceDto) => {
			const response = await createDeviceRequest(payload);
			if (!response.success) {
				throw new Error(response.data.message);
			}
			return response.data;
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success('Dispositivo criado com sucesso!');
			queryClient.invalidateQueries({ queryKey: ['find-all-devices'], exact: false });
		},
		onError: (error: any) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
