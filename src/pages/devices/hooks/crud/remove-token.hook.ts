import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { DEVICES_KEYS } from '../../data/query-keys';
import { removeDeviceTokenRequest } from '../../services/request/remove-token';

export const useRemoveToken = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: number) => {
			const response = await removeDeviceTokenRequest(id);
			if (!response.success) {
				throw new Error(response.data.message);
			}
			return response.data;
		},
		onSuccess: (_, id) => {
			toast.dismiss();
			toast.success('Token removido com sucesso!');
			queryClient.invalidateQueries({ queryKey: ['find-all-devices'], exact: false });
			queryClient.invalidateQueries({ queryKey: DEVICES_KEYS.FIND_DEVICE_BY_ID(id) });
		},
		onError: (error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
