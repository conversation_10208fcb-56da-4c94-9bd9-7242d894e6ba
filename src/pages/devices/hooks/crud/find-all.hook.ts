import { useQuery } from '@tanstack/react-query';
import { useAtomValue } from 'jotai';
import { DEVICES_KEYS } from '../../data/query-keys';
import { findAllDevicesRequest } from '../../services/request/find-all';
import { paginationDevicesState } from '../../states/pagination.state';
import { searchDevicesState } from '../../states/search-devices.state';

export const useFindAllDevices = () => {
	const search = useAtomValue(searchDevicesState);
	const pagination = useAtomValue(paginationDevicesState);

	return useQuery({
		queryKey: DEVICES_KEYS.FIND_ALL_DEVICES({
			page: pagination.page,
			pageSize: pagination.pageSize,
			search: search,
		}),
		queryFn: async () => findAllDevicesRequest({ page: pagination.page, pageSize: pagination.pageSize, search: search }),
	});
};
