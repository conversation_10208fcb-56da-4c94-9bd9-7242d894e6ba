import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { DEVICES_KEYS } from '../../data/query-keys';
import { CreateDeviceDto } from '../../services/request/create-device';
import { updateDeviceRequest } from '../../services/request/update-device';

export const useUpdateDevice = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ payload, id }: { payload: CreateDeviceDto; id: number }) => {
			toast.loading('Atualizando dispositivo...');
			const res = await updateDeviceRequest(payload, id);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: (_, { id }) => {
			queryClient.invalidateQueries({ queryKey: DEVICES_KEYS.FIND_ALL_DEVICES({ page: 1, pageSize: 10, search: '' }) });
			queryClient.invalidateQueries({ queryKey: DEVICES_KEYS.FIND_DEVICE_BY_ID(id) });
			toast.dismiss();
			toast.success('Dispositivo atualizado com sucesso');
		},
		onError: (error: any) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
