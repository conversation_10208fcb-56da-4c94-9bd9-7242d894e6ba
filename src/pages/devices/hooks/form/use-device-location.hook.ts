import { useCallback, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { IDeviceSchema } from '../../validators/create-device-schema.form';

const INITIAL_LOCATION = '<PERSON><PERSON><PERSON>, 940 - <PERSON>, União da Vitória - PR, 84600-905';
const INITIAL_MARKER = { lat: -26.2390745, lng: -51.0938556502429 };

interface UseDeviceLocationReturn {
	stringLocation: string;
	locationSearch: string;
	markerPosition: { lat: number; lng: number };
	setLocationSearch: (value: string) => void;
	setStringLocation: (value: string) => void;
	onMarkerDragEnd: (e: google.maps.MapMouseEvent) => void;
	resetLocation: () => void;
}

export function useDeviceLocation(methods: UseFormReturn<IDeviceSchema>): UseDeviceLocationReturn {
	const { setValue } = methods;
	const [stringLocation, setStringLocation] = useState(INITIAL_LOCATION);
	const [locationSearch, setLocationSearch] = useState('');
	const [markerPosition, setMarkerPosition] = useState(INITIAL_MARKER);

	const onMarkerDragEnd = useCallback(
		(e: google.maps.MapMouseEvent) => {
			if (!e.latLng) return;
			const lat = e.latLng.lat();
			const lng = e.latLng.lng();
			setValue('location', [lat, lng]);
			setMarkerPosition({ lat, lng });
			new google.maps.Geocoder().geocode({ location: { lat, lng } }, (results, status) => {
				if (status === google.maps.GeocoderStatus.OK && results?.length) {
					setStringLocation(results[0].formatted_address);
				}
			});
		},
		[setValue],
	);

	const resetLocation = useCallback(() => {
		setStringLocation(INITIAL_LOCATION);
		setLocationSearch('');
		setMarkerPosition(INITIAL_MARKER);
	}, []);

	return {
		stringLocation,
		locationSearch,
		markerPosition,
		setLocationSearch,
		setStringLocation,
		onMarkerDragEnd,
		resetLocation,
	};
}
