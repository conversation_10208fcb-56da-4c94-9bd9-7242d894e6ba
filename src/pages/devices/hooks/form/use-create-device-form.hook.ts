import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { IDeviceSchema, NewDeviceFormSchema } from '../../validators/create-device-schema.form';

export const useCreateDeviceForm = () => {
	return useForm<IDeviceSchema>({
		resolver: zodResolver(NewDeviceFormSchema),
		defaultValues: {
			brand: '',
			height: undefined,
			width: undefined,
			location: [-26.2390745, -51.0938556502429],
			model: '',
			name: '',
			resolution: '',
			type: 'TV',
		},
	});
};
