import { IGlobalMessageReturn } from '@/shared/types/response';
import { UseMutationResult } from '@tanstack/react-query';
import { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { CreateDeviceDto } from '../../services/request/create-device';
import { IDeviceSchema } from '../../validators/create-device-schema.form';

type CreateMutation = UseMutationResult<IGlobalMessageReturn, any, CreateDeviceDto, unknown>;
type UpdateMutation = UseMutationResult<any, any, { payload: CreateDeviceDto; id: number }, unknown>;

interface UseDeviceSubmitProps {
	onSubmit: CreateMutation | UpdateMutation;
	methods: UseFormReturn<IDeviceSchema>;
	modalType?: 'create' | 'update';
	deviceId?: number;
	onClose: () => void;
	resetLocation: () => void;
}

export function useDeviceSubmit({ onSubmit, methods, modalType = 'create', deviceId, onClose, resetLocation }: UseDeviceSubmitProps) {
	const { reset, handleSubmit } = methods;

	const resetFormAndClose = useCallback(() => {
		reset();
		resetLocation();
		onClose?.();
	}, [reset, resetLocation, onClose]);

	const handleFormSubmit = useCallback(
		async (data: IDeviceSchema) => {
			const { location, ...rest } = data;
			const [latitude, longitude] = location;
			const deviceData = { ...rest, latitude, longitude };

			if (modalType === 'update') {
				if (!deviceId) return;
				await (onSubmit as UpdateMutation).mutateAsync({ payload: deviceData, id: deviceId }, { onSuccess: resetFormAndClose });
			} else {
				await (onSubmit as CreateMutation).mutateAsync(deviceData, { onSuccess: resetFormAndClose });
			}
		},
		[modalType, deviceId, onSubmit, resetFormAndClose],
	);

	return {
		handleFormSubmit: handleSubmit(handleFormSubmit),
		resetFormAndClose,
	};
}
