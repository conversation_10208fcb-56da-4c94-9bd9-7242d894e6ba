import { useCallback, useState } from 'react';
import { IDeviceSchema } from '../../validators/create-device-schema.form';

export const STEPS = ['basic_informations', 'details', 'location', 'confirmation'] as const;
export type StepKeys = (typeof STEPS)[number];

export function useDeviceSteps(modalType: 'create' | 'update' = 'create') {
	const [currentStepKey, setCurrentStepKey] = useState<StepKeys>(modalType === 'create' ? STEPS[0] : STEPS[3]);
	const [isNextButtonDisabled, setIsNextButtonDisabled] = useState(true);

	const currentStepIndex = STEPS.indexOf(currentStepKey);

	const handleStep = (dir: 1 | -1) => {
		const idx = currentStepIndex + dir;
		if (STEPS[idx]) setCurrentStepKey(STEPS[idx]);
	};

	const validateStep = useCallback((values: IDeviceSchema, currentStep: StepKeys) => {
		switch (currentStep) {
			case 'basic_informations':
				return !!values.name?.trim() && !!values.brand?.trim() && !!values.model?.trim() && !!values.type;
			case 'details':
				return values.width > 0 && values.height > 0 && !!values.resolution?.trim();
			case 'location':
				return !!values.location?.[0] && !!values.location?.[1];
			default:
				return true;
		}
	}, []);

	return {
		currentStepKey,
		setCurrentStepKey,
		isNextButtonDisabled,
		setIsNextButtonDisabled,
		handleStep,
		validateStep,
		currentStepIndex,
		STEPS,
	};
}
