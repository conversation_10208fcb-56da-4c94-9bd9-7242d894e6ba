import { Button } from '@/components/shadcnui/button';
import { Step } from '@/pages/devices/interfaces/step';
import { IDeviceSchema } from '@/pages/devices/validators/create-device-schema.form';
import { IGlobalMessageReturn } from '@/shared/types/response';
import { CircularProgress, Modal, ModalBody, ModalContent, ModalFooter, ModalHeader, ModalProps } from '@nextui-org/react';
import { UseMutationResult } from '@tanstack/react-query';
import { ArrowRight, Monitor } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useCreateDeviceForm } from '../../hooks/form/use-create-device-form.hook';
import { useDeviceLocation } from '../../hooks/form/use-device-location.hook';
import { STEPS, useDeviceSteps } from '../../hooks/form/use-device-steps.hook';
import { useDeviceSubmit } from '../../hooks/form/use-device-submit.hook';
import { CreateDeviceDto } from '../../services/request/create-device';
import { DeviceBasicInformationForm } from '../forms/device-basic-informations-form';
import { DeviceDetailsForm } from '../forms/device-details-form';
import { DeviceLocationForm } from '../forms/device-location-form';
import { ReviewDeviceInformations } from '../table/components/modals/review-device-informations';

type CreateMutation = UseMutationResult<IGlobalMessageReturn, any, CreateDeviceDto, unknown>;
type UpdateMutation = UseMutationResult<any, any, { payload: CreateDeviceDto; id: number }, unknown>;

type DeviceModalProps = Omit<ModalProps, 'children' | 'onSubmit'> & {
	onClose: () => void;
	title: string;
	submitButtonLabel?: string;
	defaultValues?: Partial<IDeviceSchema>;
	methods?: UseFormReturn<IDeviceSchema>;
	deviceId?: number;
} & ({ modalType?: 'create'; onSubmit: CreateMutation } | { modalType: 'update'; onSubmit: UpdateMutation });
export type StepKeys = (typeof STEPS)[number];

export function DeviceModal({
	onClose,
	onSubmit,
	title,
	submitButtonLabel = 'Concluir',
	methods: propsMethods,
	modalType = 'create',
	deviceId,
	defaultValues,
	...modalRest
}: Readonly<DeviceModalProps>) {
	const defaultMethods = useCreateDeviceForm();
	const methods = propsMethods ?? defaultMethods;

	const { currentStepKey, setCurrentStepKey, isNextButtonDisabled, setIsNextButtonDisabled, handleStep, validateStep, currentStepIndex } =
		useDeviceSteps(modalType);

	const { stringLocation, locationSearch, markerPosition, setLocationSearch, setStringLocation, onMarkerDragEnd, resetLocation } =
		useDeviceLocation(methods);

	const { handleFormSubmit, resetFormAndClose } = useDeviceSubmit({
		onSubmit,
		methods,
		modalType,
		deviceId,
		onClose,
		resetLocation,
	});

	const {
		watch,
		setValue,
		formState: { isSubmitting },
	} = methods;

	useEffect(() => {
		if (modalType === 'update' && defaultValues) {
			methods.reset(defaultValues);
			if (defaultValues.location?.length === 2 && window.google) {
				const [lat, lng] = defaultValues.location;
				setStringLocation(`${lat}, ${lng}`);
				setValue('location', [lat, lng]);
				new google.maps.Geocoder().geocode({ location: { lat, lng } }, (results, status) => {
					if (status === google.maps.GeocoderStatus.OK && results?.[0]) {
						setStringLocation(results[0].formatted_address);
					}
				});
			}
		}
	}, [modalType, defaultValues, methods, setValue, setStringLocation]);

	const watchedValues = watch();

	useEffect(() => {
		setIsNextButtonDisabled(!validateStep(watchedValues, currentStepKey));
	}, [validateStep, watchedValues, currentStepKey, setIsNextButtonDisabled]);

	const steps = useMemo<Step[]>(
		() => [
			{
				accessor_key: 'basic_informations' as const,
				title: 'Informações Básicas',
				body: <DeviceBasicInformationForm methods={methods} />,
			},
			{
				accessor_key: 'details' as const,
				title: 'Detalhes do Dispositivo',
				description: 'O que você preencher será utilizado para a criação das apresentações nesse dispositivo.',
				body: <DeviceDetailsForm methods={methods} />,
			},
			{
				accessor_key: 'location' as const,
				title: 'Localização do Dispositivo',
				body: (
					<DeviceLocationForm
						methods={methods}
						locationSearch={locationSearch}
						setLocationSearch={setLocationSearch}
						stringLocation={stringLocation}
						markerPosition={markerPosition}
						onMarkerDragEnd={onMarkerDragEnd}
					/>
				),
			},
			{
				accessor_key: 'confirmation' as const,
				title: 'Confirmação',
				body: (
					<ReviewDeviceInformations
						stringLocation={stringLocation}
						currentStepKey={currentStepKey}
						newDevice={watchedValues}
						setCurrentStepKey={setCurrentStepKey}
					/>
				),
			},
		],
		[methods, currentStepKey, locationSearch, stringLocation, watchedValues, markerPosition, onMarkerDragEnd, setCurrentStepKey, setLocationSearch],
	);

	return (
		<Modal {...modalRest} isDismissable={!isSubmitting} className="rounded-xl border border-[#232728] bg-muted shadow-lg" onClose={resetFormAndClose}>
			<ModalContent>
				<form onSubmit={handleFormSubmit}>
					<ModalHeader className="flex flex-col gap-2 pb-0">
						<div className="flex items-center gap-2">
							<Monitor className="h-6 w-6 text-primary" />
							<h1 className="text-lg font-semibold text-white">{title}</h1>
						</div>
						<h3 className="inline-flex items-center gap-2 text-sm font-normal text-muted-foreground">
							{steps[currentStepIndex].description ?? `Etapa ${currentStepIndex + 1} de ${steps.length}: ${steps[currentStepIndex].title}`}
						</h3>
					</ModalHeader>
					<ModalBody className="pt-2">{steps[currentStepIndex].body}</ModalBody>
					<ModalFooter className="pb-6">
						{currentStepIndex > 0 && (
							<Button
								size="sm"
								className="flex h-12 w-[150px] items-center justify-center gap-2 rounded-lg bg-green-800 text-base font-semibold text-white shadow-md transition hover:bg-green-500 disabled:cursor-not-allowed disabled:opacity-50"
								variant="ghost"
								type="button"
								onClick={() => handleStep(-1)}
							>
								<ArrowRight className="h-4 w-4 rotate-180" />
								Voltar
							</Button>
						)}
						{currentStepIndex < steps.length - 1 ? (
							<Button
								size="sm"
								variant="outline"
								type="button"
								onClick={() => handleStep(1)}
								className={`flex h-12 w-[150px] items-center justify-center gap-2 rounded-lg text-base font-semibold text-white shadow-md transition disabled:cursor-not-allowed disabled:opacity-50 ${
									isNextButtonDisabled ? 'bg-green-600/50' : 'bg-green-600 hover:bg-green-500'
								}`}
								disabled={isNextButtonDisabled}
							>
								<ArrowRight className="h-4 w-4" />
								Próximo
							</Button>
						) : onSubmit.isPending ? (
							<CircularProgress />
						) : (
							<Button
								size="sm"
								type="button"
								onClick={handleFormSubmit}
								variant="outline"
								className="flex h-12 w-[150px] items-center justify-center gap-2 rounded-lg bg-green-600 text-base font-semibold text-white shadow-md transition hover:bg-green-500 disabled:cursor-not-allowed disabled:opacity-50"
							>
								<Monitor className="h-4 w-4" />
								{submitButtonLabel}
							</Button>
						)}
					</ModalFooter>
				</form>
			</ModalContent>
		</Modal>
	);
}
