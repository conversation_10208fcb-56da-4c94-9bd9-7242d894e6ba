import { IDevice } from '@/pages/devices/services/request/find-all';
import { Card, CardBody, CardHeader } from '@nextui-org/react';
import { CalendarDays, Edit3, MonitorSmartphone, Smartphone } from 'lucide-react';

interface DeviceCardProps {
	readonly device: IDevice;
}

export function DeviceCard({ device }: DeviceCardProps) {
	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return `${date.toLocaleDateString()} às ${date.toLocaleTimeString()}`;
	};

	return (
		<Card className="mb-3 w-full rounded-lg bg-muted text-white shadow-lg transition-all hover:shadow-primary/40">
			<CardHeader className="flex items-center gap-3 border-b border-white/10 pb-3">
				<MonitorSmartphone size={24} className="text-primary" />
				<div className="flex flex-col">
					<span className="text-lg font-bold text-primary">{device.name}</span>
					<span className="text-xs text-gray-400">
						{device.brand} {device.model}
					</span>
				</div>
			</CardHeader>
			<CardBody className="space-y-3 py-4">
				<div className="flex items-center gap-2">
					<Smartphone size={16} className="text-gray-400" />
					<p className="text-sm text-gray-300">Tipo: {device.type}</p>
				</div>
				<div className="flex items-center gap-2">
					<span className="rounded bg-primary/20 px-2 py-0.5 text-xs font-semibold text-primary">{device.status ? 'Ativo' : 'Inativo'}</span>
				</div>
				<div className="flex items-center gap-2">
					<p className="text-sm text-gray-300">
						Resolução: {device.width}x{device.height} ({device.resolution})
					</p>
				</div>
				<div className="flex items-center gap-2">
					<CalendarDays size={16} className="text-gray-400" />
					<p className="text-sm text-gray-300">Criado em: {formatDate(device.createdAt)}</p>
				</div>
				<div className="flex items-center gap-2">
					<Edit3 size={16} className="text-gray-400" />
					<p className="text-sm text-gray-300">Atualizado em: {formatDate(device.updatedAt)}</p>
				</div>
			</CardBody>
		</Card>
	);
}
