import { Button } from '@/components/shadcnui/button';
import { <PERSON>ircular<PERSON>rogress, <PERSON>dal, Modal<PERSON>ody, Modal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>dalHeader, ModalProps } from '@nextui-org/react';
import { Trash2 } from 'lucide-react';
import { useState } from 'react';

interface DeleteDeviceModalProps extends Omit<ModalProps, 'children'> {
	onClose: () => void;
	onConfirm: () => Promise<void> | void;
	deviceName?: string;
	isLoading?: boolean;
}

export function DeleteDeviceModal({ onClose, onConfirm, deviceName, isLoading = false, ...rest }: Readonly<DeleteDeviceModalProps>) {
	const [loading, setLoading] = useState(false);

	const handleConfirm = async () => {
		setLoading(true);
		try {
			await onConfirm();
			onClose();
		} finally {
			setLoading(false);
		}
	};

	return (
		<Modal {...rest} isDismissable={!loading && !isLoading} onClose={onClose} className="rounded-xl border border-[#232728] bg-muted shadow-lg">
			<ModalContent>
				<ModalHeader className="flex items-center gap-2 pb-0">
					<Trash2 className="h-6 w-6 text-red-500" />
					<span className="text-lg font-semibold text-white">Confirmar exclusão</span>
				</ModalHeader>
				<ModalBody className="pt-2">
					<p className="text-base text-muted-foreground">
						Tem certeza que deseja excluir o dispositivo
						{deviceName ? <span className="font-semibold text-foreground"> "{deviceName}"</span> : null}
						?<br />
						Esta ação <span className="font-semibold text-red-500">não pode ser desfeita</span>.
					</p>
				</ModalBody>
				<ModalFooter className="gap-2 pb-6">
					<Button
						variant="outline"
						onClick={onClose}
						disabled={loading || isLoading}
						className="border border-border/30 bg-background hover:bg-accent"
					>
						Cancelar
					</Button>
					<Button
						onClick={handleConfirm}
						disabled={loading || isLoading}
						className="flex items-center gap-1 bg-destructive text-destructive-foreground hover:bg-destructive/90"
					>
						{loading || isLoading ? <CircularProgress size="sm" aria-label="Excluindo" /> : <Trash2 className="h-4 w-4" />}
						Excluir
					</Button>
				</ModalFooter>
			</ModalContent>
		</Modal>
	);
}
