import { useCreateDevice } from '@/pages/devices/hooks/crud/create-device.hook';
import { ModalProps } from '@nextui-org/react';
import { DeviceModal } from '../../../modals/device-modal';

interface CreateDeviceModalProps extends Omit<ModalProps, 'children'> {
	onClose: () => void;
}

export function CreateDeviceModal({ onClose, ...rest }: Readonly<CreateDeviceModalProps>) {
	const createDevice = useCreateDevice();
	return <DeviceModal {...rest} onClose={onClose} onSubmit={createDevice} title="Adicionar Dispositivo" submitButtonLabel="Concluir" />;
}
