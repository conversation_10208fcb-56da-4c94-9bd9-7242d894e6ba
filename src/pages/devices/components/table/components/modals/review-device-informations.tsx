import { Badge } from '@/components/shadcnui/badge';
import { Button } from '@/components/shadcnui/button';
import { changeIconByDeviceType } from '@/pages/devices/functions/change-icon-by-device-type';
import { IDeviceSchema } from '@/pages/devices/validators/create-device-schema.form';
import { motion } from 'framer-motion';
import { <PERSON>ideCheck, LucidePen } from 'lucide-react';
import { Dispatch, SetStateAction } from 'react';
import { StepKeys } from '../../../modals/device-modal';

interface ReviewDeviceInformationsProps {
	newDevice: IDeviceSchema;
	currentStepKey: StepKeys;
	setCurrentStepKey: Dispatch<SetStateAction<StepKeys>>;
	stringLocation: string;
}

export function ReviewDeviceInformations(props: Readonly<ReviewDeviceInformationsProps>) {
	const { stringLocation, currentStepKey, newDevice, setCurrentStepKey } = props;
	const goToStep = (key: StepKeys) => {
		if (currentStepKey !== key) setCurrentStepKey(key);
	};

	return (
		<div className="flex flex-col gap-5">
			<motion.div
				className="mb-2 rounded-lg border border-primary/30 bg-primary/10 p-4 shadow-lg"
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.3 }}
			>
				<div className="flex items-center gap-2 pb-2">
					<div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20 text-primary">
						<LucideCheck size={20} />
					</div>
					<h3 className="text-lg font-bold text-white">Revise as informações</h3>
				</div>
				<p className="text-sm text-gray-300">Verifique os detalhes do dispositivo abaixo e clique em "Concluir" para salvar.</p>
			</motion.div>

			<div className="grid gap-5 md:grid-cols-2">
				<motion.div
					className="flex flex-col gap-3 rounded-lg border border-white/10 bg-black/20 p-4 shadow-md"
					initial={{ opacity: 0, x: -10 }}
					animate={{ opacity: 1, x: 0 }}
					transition={{ duration: 0.3, delay: 0.1 }}
				>
					<div className="flex items-center justify-between">
						<h4 className="flex items-center gap-2 font-medium text-primary">
							<Badge className="bg-primary/20 text-primary hover:bg-primary/30">Informações Básicas</Badge>
						</h4>
						<Button size="sm" variant="ghost" onClick={() => goToStep('basic_informations')}>
							<LucidePen size={14} /> <span className="ml-1 text-xs">Editar</span>
						</Button>
					</div>

					<div className="space-y-3 pl-1">
						<div className="flex items-center gap-2">
							<span className="w-16 text-sm font-medium text-gray-400">Nome:</span>
							<span className="text-base text-white">{newDevice.name}</span>
						</div>

						<div className="flex items-center gap-2">
							<span className="w-16 text-sm font-medium text-gray-400">Tipo:</span>
							<span className="flex items-center gap-2 text-base text-white">
								{newDevice.type}
								<span className="rounded-md bg-primary/10 p-1 text-primary">{changeIconByDeviceType(newDevice.type)}</span>
							</span>
						</div>

						<div className="flex items-center gap-2">
							<span className="w-16 text-sm font-medium text-gray-400">Marca:</span>
							<span className="text-base text-white">{newDevice.brand}</span>
						</div>

						<div className="flex items-center gap-2">
							<span className="w-16 text-sm font-medium text-gray-400">Modelo:</span>
							<span className="text-base text-white">{newDevice.model}</span>
						</div>
					</div>
				</motion.div>
				<motion.div
					className="flex flex-col gap-3 rounded-lg border border-white/10 bg-black/20 p-4 shadow-md"
					initial={{ opacity: 0, x: 10 }}
					animate={{ opacity: 1, x: 0 }}
					transition={{ duration: 0.3, delay: 0.2 }}
				>
					<div className="flex items-center justify-between">
						<h4 className="flex items-center gap-2 font-medium text-primary">
							<Badge className="bg-primary/20 text-primary hover:bg-primary/30">Especificações</Badge>
						</h4>
						<Button size="sm" variant="ghost" onClick={() => goToStep('details')}>
							<LucidePen size={14} /> <span className="ml-1 text-xs">Editar</span>
						</Button>
					</div>
					<div className="space-y-3 pl-1">
						<div className="flex items-center gap-2">
							<span className="w-24 text-sm font-medium text-gray-400">Largura:</span>
							<span className="text-base text-white">{newDevice.width}</span>
						</div>
						<div className="flex items-center gap-2">
							<span className="w-24 text-sm font-medium text-gray-400">Altura:</span>
							<span className="text-base text-white">{newDevice.height}</span>
						</div>
						<div className="flex items-center gap-2">
							<span className="w-24 text-sm font-medium text-gray-400">Resolução:</span>
							<span className="text-base text-white">{newDevice.resolution}</span>
						</div>
					</div>
				</motion.div>
				<motion.div
					className="flex flex-col gap-3 rounded-lg border border-white/10 bg-black/20 p-4 shadow-md md:col-span-2"
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.3, delay: 0.3 }}
				>
					<div className="flex items-center justify-between">
						<h4 className="flex items-center gap-2 font-medium text-primary">
							<Badge className="bg-primary/20 text-primary hover:bg-primary/30">Localização</Badge>
						</h4>
						<Button size="sm" variant="ghost" onClick={() => goToStep('location')}>
							<LucidePen size={14} /> <span className="ml-1 text-xs">Editar</span>
						</Button>
					</div>
					<div className="pl-1">
						<div className="rounded-md bg-primary/5 p-3 text-white">{stringLocation || newDevice.location || 'Localização não definida'}</div>
					</div>
				</motion.div>
			</div>
		</div>
	);
}
