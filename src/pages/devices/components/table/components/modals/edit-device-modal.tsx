import { useFindDeviceById } from '@/pages/devices/hooks/crud/find-by-id.hook';
import { useUpdateDevice } from '@/pages/devices/hooks/crud/update-device.hook';
import { IDevice } from '@/pages/devices/services/request/find-all';
import { IDeviceSchema } from '@/pages/devices/validators/create-device-schema.form';
import { CircularProgress, ModalProps } from '@nextui-org/react';
import { useEffect } from 'react';
import { DeviceModal } from '../../../modals/device-modal';

interface UpdateDeviceModalProps extends Omit<ModalProps, 'children'> {
	onClose: () => void;
	deviceId: number;
}

export function UpdateDeviceModal({ onClose, deviceId, ...rest }: Readonly<UpdateDeviceModalProps>) {
	const updateDeviceMutation = useUpdateDevice();

	const { data: device, isLoading, isError } = useFindDeviceById(deviceId);

	const mapDeviceDataToForm = (deviceData: IDevice): IDeviceSchema => ({
		brand: deviceData.brand,
		height: deviceData.height,
		width: deviceData.width,
		location: [deviceData.latitude, deviceData.longitude],
		model: deviceData.model,
		name: deviceData.name,
		resolution: deviceData.resolution,
		type: deviceData.type as IDeviceSchema['type'],
	});

	useEffect(() => {
		if (isError) onClose();
	}, [isError, onClose]);

	if (isLoading) {
		return (
			<div className="flex h-40 w-full items-center justify-center">
				<CircularProgress aria-label="Carregando dados do dispositivo" />
			</div>
		);
	}

	return (
		<DeviceModal
			{...rest}
			deviceId={deviceId}
			onClose={onClose}
			modalType="update"
			onSubmit={updateDeviceMutation}
			title="Editar Dispositivo"
			submitButtonLabel="Salvar"
			defaultValues={device?.data ? mapDeviceDataToForm(device.data as IDevice) : undefined}
		/>
	);
}
