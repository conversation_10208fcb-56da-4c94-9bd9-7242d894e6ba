import { TableHead, TableHeader, TableRow } from '@/components/shadcnui/table';
import { IDevice } from '@/pages/devices/services/request/find-all';
import { flexRender, Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';

interface TableHeaderProps {
	table: Table<IDevice>;
}

export const DevicesTableHeader = ({ table }: TableHeaderProps) => {
	return (
		<TableHeader className="presentations-header sticky top-0 z-10 bg-[#232323] shadow-md">
			{table.getHeaderGroups().map((headerGroup) => (
				<TableRow key={headerGroup.id} className="hover:bg-transparent">
					{headerGroup.headers.map((header) => (
						<TableHead
							key={header.id}
							colSpan={header.colSpan}
							className="border-b-2 border-primary/60 bg-[#232323] px-4 py-3 text-base font-bold tracking-wide text-white shadow-inner"
						>
							{header.isPlaceholder ? null : (
								<motion.div
									initial={{ opacity: 0, y: -10 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.3, type: 'spring' }}
									className="flex items-center gap-2"
								>
									{flexRender(header.column.columnDef.header, header.getContext())}
								</motion.div>
							)}
						</TableHead>
					))}
				</TableRow>
			))}
		</TableHeader>
	);
};
