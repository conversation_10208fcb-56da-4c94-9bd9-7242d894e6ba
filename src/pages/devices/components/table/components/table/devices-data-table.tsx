import { Button } from '@/components/shadcnui/button';
import { Table } from '@/components/shadcnui/table';
import { useIsMobile } from '@/hooks/use-is-mobile';
import { useDevicesTable } from '@/pages/devices/hooks/crud/devices-table.hook';
import { IDevice, IDeviceFindAllResponse } from '@/pages/devices/services/request/find-all';
import { CircularProgress, useDisclosure } from '@nextui-org/react';
import { ColumnDef } from '@tanstack/react-table';
import { LucideSmartphone } from 'lucide-react';
import { DeviceCard } from '../../device-card';
import { CreateDeviceModal } from '../modals/create-device-modal';
import { DevicesTableBody } from './device-table-body';
import { DevicesTableHeader } from './device-table-header';
import { DevicesDataTablePagination } from './devices-data-table-pagination';
import { DevicesDataTableToolbar } from './devices-data-table-toolbar';

interface DevicesDataTableProps {
	readonly columns: ColumnDef<IDevice>[];
	readonly data: IDeviceFindAllResponse;
	readonly isLoading?: boolean;
	readonly messageError?: string;
}

export function DevicesDataTable({ columns, data, isLoading, messageError }: DevicesDataTableProps) {
	const { table } = useDevicesTable({ data: data.data, columns });
	const isMobile = useIsMobile(640);
	const modalCreateDevice = useDisclosure();

	const renderContent = () => {
		if (messageError) return <div className="py-4 text-center font-medium text-red-500">{messageError}</div>;
		if (isLoading) return <CircularProgress className="mx-auto" aria-label="loading..." />;
		if (isMobile) {
			return (
				<div className="flex flex-col gap-2">
					<div className="mb-2 rounded-lg bg-primary/10 px-3 py-2 text-center text-xs text-primary">
						No mobile, só é possível adicionar e visualizar as apresentações existentes.
					</div>
					{data.data.map((device) => (
						<DeviceCard key={device.id} device={device} />
					))}
				</div>
			);
		}
		return (
			<Table>
				<DevicesTableHeader table={table} />
				<DevicesTableBody table={table} columns={columns} />
			</Table>
		);
	};

	return (
		<section className="relative h-full space-y-4 overflow-hidden rounded-xl px-2 py-2 transition-all duration-100 sm:space-y-6 sm:px-4 sm:py-4">
			<DevicesDataTableToolbar table={table} />
			<div className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground sm:text-sm">Gerencie todos os seus dispositivos em um só lugar</p>
				</div>
				<Button
					size="lg"
					className="mt-2 w-full border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 focus:ring-4 focus:ring-primary/30 sm:mt-0 sm:w-auto"
					onClick={modalCreateDevice.onOpen}
				>
					<LucideSmartphone size={22} className="transition-transform duration-300 group-hover:rotate-[15deg] group-hover:scale-110" />
					<span className="ml-2 inline">Novo Dispositivo</span>
				</Button>
			</div>
			<div className={isMobile ? undefined : 'overflow-x-auto rounded-lg border-2 border-white/10'}>
				{renderContent()}
				<DevicesDataTablePagination table={table} />
			</div>

			<CreateDeviceModal
				size={'3xl'}
				onClose={modalCreateDevice.onClose}
				backdrop="blur"
				isOpen={modalCreateDevice.isOpen}
				onOpenChange={modalCreateDevice.onOpenChange}
				className="border border-input bg-background"
			/>
		</section>
	);
}
