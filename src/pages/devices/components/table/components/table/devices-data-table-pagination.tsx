import { Button } from '@/components/shadcnui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/shadcnui/select';
import { paginationDevicesState } from '@/pages/devices/states/pagination.state';
import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { LucideChevronLeft, LucideChevronRight, LucideChevronsLeft, LucideChevronsRight } from 'lucide-react';
import { useEffect } from 'react';

interface DevicesDataTablePaginationProps<TData> {
	table: Table<TData>;
}

export function DevicesDataTablePagination<TData>({ table }: Readonly<DevicesDataTablePaginationProps<TData>>) {
	const [pagination, setPagination] = useAtom(paginationDevicesState);
	const dontShowPagination = table.getPageCount() <= 1;
	const isMobile = false;

	const PaginationInfo = () => {
		const selectedCount = table.getFilteredSelectedRowModel().rows.length;
		const totalRows = table.getRowModel().rows.length;
		const filteredRows = table.getFilteredRowModel().rows.length;
		const hasSelectedRows = table.getSelectedRowModel().rows.length > 0;

		if (hasSelectedRows) {
			return (
				<motion.div
					key="selected-info"
					initial={{ opacity: 0, y: -5 }}
					animate={{ opacity: 1, y: 0 }}
					className="flex w-full flex-col items-start gap-1 sm:flex-row sm:items-center sm:gap-2"
				>
					<span className="rounded-full bg-green-500 px-2.5 py-0.5 text-xs font-semibold text-white shadow">{selectedCount}</span>
					<span className="text-xs text-gray-300 sm:text-sm">
						selecionado{selectedCount > 1 ? 's' : ''} de {totalRows} dispositivo{totalRows > 1 ? 's' : ''}
					</span>
				</motion.div>
			);
		}
		return (
			<motion.div
				key="default-info"
				initial={{ opacity: 0, y: -5 }}
				animate={{ opacity: 1, y: 0 }}
				className="w-full text-xs text-gray-300 sm:text-sm"
			>
				Mostrando <span className="font-semibold text-white drop-shadow">{totalRows}</span> de{' '}
				<span className="font-semibold text-white drop-shadow">{filteredRows}</span> dispositivo{filteredRows > 1 ? 's' : ''}
			</motion.div>
		);
	};

	const RowsPerPageSelect = () => (
		<div className="flex items-center gap-2 rounded-md border border-muted-foreground/10 bg-black/30 px-2 py-1 shadow-sm">
			<p className={`hidden text-muted-foreground`}>Linhas por página:</p>
			<Select
				value={`${pagination.pageSize}`}
				onValueChange={(value) => {
					const newSize = Number(value);
					setPagination((prev) => ({ ...prev, pageSize: newSize, page: 1 }));
				}}
			>
				<SelectTrigger className="h-7 w-[60px] rounded border border-muted-foreground/10 bg-black/90 text-muted-foreground shadow-none hover:border-green-400/30 hover:bg-black/95 focus:ring-2 focus:ring-green-400/20">
					<SelectValue placeholder={pagination.pageSize} />
				</SelectTrigger>
				<SelectContent side="top" className="border border-muted-foreground/10 bg-black/95 text-muted-foreground shadow-lg">
					{[5, 10, 20, 30, 40, 50].map((pageSize) => (
						<SelectItem key={pageSize} value={`${pageSize}`} className="hover:bg-green-100/40 hover:text-white">
							{pageSize}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
		</div>
	);

	const PaginationControls = () => (
		<div className="flex items-center space-x-3 rounded-md bg-black/20 px-2 py-1 shadow-inner">
			<div className="flex items-center space-x-1">
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30 lg:flex"
					onClick={() => setPagination((prev) => ({ ...prev, page: 1 }))}
					disabled={pagination.page === 1}
				>
					<span className="sr-only">Ir para a primeira página</span>
					<LucideChevronsLeft className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30"
					onClick={() => setPagination((prev) => ({ ...prev, page: prev.page - 1 }))}
					disabled={pagination.page === 1}
				>
					<span className="sr-only">Ir para a página anterior</span>
					<LucideChevronLeft className="h-4 w-4" />
				</Button>
				<div className="page-status flex min-w-[80px] items-center justify-center rounded-md bg-black/40 px-2 py-1 text-sm font-semibold text-white shadow-inner">
					{pagination.page}/{table.getPageCount()}
				</div>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30"
					onClick={() => setPagination((prev) => ({ ...prev, page: prev.page + 1 }))}
					disabled={pagination.page === table.getPageCount()}
				>
					<span className="sr-only">Ir para a próxima página</span>
					<LucideChevronRight className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-[#232323]/80 p-0 text-white shadow hover:border-green-500/30 hover:bg-[#232323]/90 hover:text-green-400 focus:ring-2 focus:ring-green-500/30 lg:flex"
					onClick={() => setPagination((prev) => ({ ...prev, page: table.getPageCount() }))}
					disabled={pagination.page === table.getPageCount()}
				>
					<span className="sr-only">Ir para a última página</span>
					<LucideChevronsRight className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);

	useEffect(() => {
		table.setPageIndex(pagination.page - 1);
		table.setPageSize(pagination.pageSize);
	}, [pagination.page, pagination.pageSize, table]);

	useEffect(() => {
		const { pageIndex, pageSize } = table.getState().pagination;
		setPagination({
			page: pageIndex + 1,
			pageSize: pageSize,
		});
	}, [table, setPagination]);

	return (
		<div
			className={`${isMobile && 'rounded-lg'} flex flex-col items-center gap-4 bg-muted px-2 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-2`}
		>
			<div className={`flex w-full ${isMobile ? 'flex-row' : 'flex-col'} items-center gap-3 sm:flex-row sm:items-center sm:gap-4`}>
				<PaginationInfo />
				<RowsPerPageSelect />
			</div>
			{!dontShowPagination && (
				<div className="mt-3 flex w-full justify-center sm:mt-0 sm:w-auto sm:justify-end">
					<PaginationControls />
				</div>
			)}
		</div>
	);
}
