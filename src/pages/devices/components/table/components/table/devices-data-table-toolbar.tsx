import { GlobalFilter } from '@/components/global-search-filter';
import useDebounce from '@/hooks/use-debounce';
import { searchDevicesState } from '@/pages/devices/states/search-devices.state';
import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { LucideSmartphone } from 'lucide-react';
import { useEffect } from 'react';

export interface DevicesDataTableToolbarProps<TData> {
	table: Table<TData>;
	showTableViewOptions?: boolean;
}

export const DevicesDataTableToolbar = <TData,>({ table }: DevicesDataTableToolbarProps<TData>) => {
	const [search, setSearch] = useAtom(searchDevicesState);

	const debouncedSearch = useDebounce(search, 500);

	useEffect(() => {
		table.setGlobalFilter(debouncedSearch);
	}, [debouncedSearch, table]);

	return (
		<div className="group flex w-full flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
			<motion.div
				className="flex items-center gap-4"
				initial={{ opacity: 0, y: -20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.6, type: 'spring' }}
			>
				<div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10 shadow-inner">
					<LucideSmartphone className="text-primary" size={28} />
				</div>
				<div>
					<h1 className="text-2xl font-extrabold tracking-tight text-white drop-shadow-sm">Dispositivos</h1>
					<motion.span
						className="mt-1 inline-block rounded-full border border-primary/30 bg-primary/10 px-3 py-0.5 text-xs font-semibold text-primary/90 shadow-sm"
						initial={{ scale: 0.8, opacity: 0 }}
						animate={{ scale: 1, opacity: 1 }}
						transition={{ delay: 0.3, type: 'spring' }}
					>
						{table.getRowModel().rows.length} no total
					</motion.span>
				</div>
			</motion.div>
			<motion.div
				className="flex w-full flex-col items-end gap-3 sm:w-auto sm:flex-row"
				initial={{ opacity: 0, x: 20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ duration: 0.5, delay: 0.2 }}
			>
				<GlobalFilter placeholder="Buscar dispositivos..." globalFilter={search} setGlobalFilter={setSearch} />
				{/* {showTableViewOptions && <DevicesDataTableViewOptions table={table} />} */}
			</motion.div>
		</div>
	);
};
