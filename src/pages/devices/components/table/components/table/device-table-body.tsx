import { TableBody, TableCell, TableRow } from '@/components/shadcnui/table';
import { IDevice } from '@/pages/devices/services/request/find-all';
import { ColumnDef, flexRender, Table } from '@tanstack/react-table';

interface TableBodyProps {
	table: Table<IDevice>;
	columns: ColumnDef<IDevice>[];
}

export const DevicesTableBody = ({ table, columns }: TableBodyProps) => {
	return (
		<TableBody>
			{table?.getRowModel()?.rows.length ? (
				table?.getRowModel()?.rows.map((row) => (
					<TableRow
						key={row.id}
						className={`cursor-pointer transition-colors hover:bg-[#232323]/70 hover:text-white ${
							row.getIsSelected() ? 'bg-[#232323] text-white' : ''
						}`}
					>
						{row.getVisibleCells().map((cell) => (
							<TableCell key={cell.id} className={`px-4 py-3 text-base font-medium`}>
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				))
			) : (
				<TableRow>
					<TableCell colSpan={columns.length} className="h-24 text-center">
						Sem resultados.
					</TableCell>
				</TableRow>
			)}
		</TableBody>
	);
};
