// import { useConfirm } from '@/components/confirm-dialog/use-confirm';
// import { Button } from '@/components/shadcnui/button';
// import {
// 	DropdownMenu,
// 	DropdownMenuContent,
// 	DropdownMenuItem,
// 	DropdownMenuLabel,
// 	DropdownMenuSeparator,
// 	DropdownMenuTrigger,
// } from '@/components/shadcnui/dropdown-menu';
// import { Device } from '@/functions/tanstack-query/devices/get-all';

// import { DeviceTypeSchema, IDeviceSchema } from '@/pages/devices/validators/create-device-schema.form';
// import api from '@/shared/lib/api/api';
// import { useDisclosure } from '@nextui-org/react';
// import { useMutation, useQueryClient } from '@tanstack/react-query';
// import { Row } from '@tanstack/react-table';
// import copy from 'copy-to-clipboard';
// import { LucideClipboard, LucideKeySquare, LucideMoreHorizontal, LucidePenSquare, LucideShieldClose, LucideTrash2 } from 'lucide-react';
// import { Fragment } from 'react/jsx-runtime';
// import { toast } from 'sonner';

// interface ActionsCellProps {
// 	row: Row<Device>;
// 	selectedRows: number[];
// }

// export function ActionsCell({ row, selectedRows }: ActionsCellProps) {
// 	const modalEditDevice = useDisclosure();
// 	const queryClient = useQueryClient();
// 	const confirm = useConfirm();

// 	const { handleCreateToken, handleRemoveToken } = useDeviceTokenActions();

// 	const originalDevice: IDeviceSchema = {
// 		brand: row.original.brand,
// 		height: row.original.height,
// 		width: row.original.width,
// 		location: [row.original.location?.x, row.original.location?.y],
// 		model: row.original.model,
// 		name: row.original.name,
// 		resolution: row.original.resolution,
// 		type: row.original.type as DeviceTypeSchema,
// 	};

// 	async function deleteDevice(device_id: number, device_name: string): Promise<void> {
// 		const result = await confirm({
// 			title: 'Excluir Dispositivo',
// 			description: `Deseja excluir o dispositivo "${device_name}"?`,
// 		});

// 		if (result) {
// 			await api
// 				.delete(`devices/admin/${device_id}`)
// 				.then(async () => {
// 					toast.success(`Dispositivo excluído com sucesso!`);
// 					return await queryClient.invalidateQueries({ queryKey: ['get-all-devices'] });
// 				})
// 				.catch((err) => toast.error(err.response.data.message));
// 		}
// 		return;
// 	}

// 	const { mutateAsync: handleDeleteDevice } = useMutation({
// 		mutationFn: async ({ device_id, device_name }: { device_id: number; device_name: string }) => await deleteDevice(device_id, device_name),
// 		onSuccess: async () => {
// 			return await queryClient.invalidateQueries({ queryKey: ['get-all-devices'] });
// 		},
// 	});
// 	return (
// 		<div className="flex flex-1 items-center justify-center">
// 			<DropdownMenu>
// 				<DropdownMenuTrigger asChild>
// 					<Button variant="outline" size={'icon'}>
// 						<LucideMoreHorizontal className="h-4 w-4" />
// 					</Button>
// 				</DropdownMenuTrigger>
// 				<DropdownMenuContent align="end">
// 					<DropdownMenuLabel className="flex items-center justify-between">
// 						<h1 className="line-clamp-1">{row.original.name}</h1>
// 					</DropdownMenuLabel>

// 					<DropdownMenuSeparator />
// 					{!selectedRows.length && (
// 						<Fragment>
// 							{row.original.token && (
// 								<DropdownMenuItem
// 									className="justify-between gap-2"
// 									onClick={() => {
// 										copy(row.original.token!);
// 										return toast.info(`Token de acesso copiado para área de transferência.`);
// 									}}
// 								>
// 									Copiar Token
// 									<LucideClipboard size={16} />
// 								</DropdownMenuItem>
// 							)}
// 							<DropdownMenuItem className="text- justify-between gap-2 text-secondary-600" onClick={modalEditDevice.onOpen}>
// 								Editar Dispositivo
// 								<LucidePenSquare size={16} />
// 							</DropdownMenuItem>
// 							<DropdownMenuItem
// 								className="justify-between gap-2 text-primary"
// 								onClick={() => handleCreateToken(row.original.id, row.original.name)}
// 							>
// 								Gerar Novo Token
// 								<LucideKeySquare size={16} />
// 							</DropdownMenuItem>
// 						</Fragment>
// 					)}

// 					{!selectedRows.length && <DropdownMenuSeparator />}
// 					{row.original.token && (
// 						<>
// 							<DropdownMenuItem
// 								className="justify-between gap-2 text-warning"
// 								onClick={() => handleRemoveToken(row.original.id, row.original.name)}
// 							>
// 								Apagar Token
// 								<LucideShieldClose size={16} />
// 							</DropdownMenuItem>
// 							<DropdownMenuSeparator />
// 						</>
// 					)}
// 					<DropdownMenuItem
// 						className="justify-between gap-2 text-danger-500"
// 						onClick={() => handleDeleteDevice({ device_id: row.original.id, device_name: row.original.name })}
// 					>
// 						Deletar
// 						<LucideTrash2 size={16} />
// 					</DropdownMenuItem>
// 				</DropdownMenuContent>
// 			</DropdownMenu>
// 			{/*
// 			<EditDeviceModal
// 				originalDevice={originalDevice}
// 				deviceId={row.original.id}
// 				size={'3xl'}
// 				backdrop="blur"
// 				isOpen={modalEditDevice.isOpen}
// 				onOpenChange={modalEditDevice.onOpenChange}
// 				onClose={modalEditDevice.onClose}
// 				className="border border-input bg-background"
// 			/> */}
// 		</div>
// 	);
// }
