import { Badge } from '@/components/shadcnui/badge';
import { Button } from '@/components/shadcnui/button';
import { IDevice } from '@/pages/devices/services/request/find-all';
import { Modal, ModalBody, ModalContent, ModalHeader, useDisclosure } from '@nextui-org/react';
import { Row } from '@tanstack/react-table';
import { Map, Marker } from '@vis.gl/react-google-maps';
import { motion } from 'framer-motion';
import { MapPin, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { mapStyles } from '../../../forms/device-location-form';

interface LocationCellProps {
	row: Row<IDevice>;
}

export function LocationCell({ row }: LocationCellProps) {
	const device = row.original;
	const modalLocation = useDisclosure();
	const [locationName, setLocationName] = useState<string | null>(null);

	const position = {
		lat: row.original.latitude,
		lng: row.original.longitude,
	};

	useEffect(() => {
		if (modalLocation.isOpen) {
			if (device.latitude && device.longitude) {
				const geocoder = new google.maps.Geocoder();
				const latLng = new google.maps.LatLng(device.latitude, device.longitude);
				geocoder.geocode({ location: latLng }, (results, status) => {
					if (status === google.maps.GeocoderStatus.OK && results?.[0]) setLocationName(results[0].formatted_address);
				});
			} else {
				setLocationName(null);
			}
		}
	}, [modalLocation.isOpen, device.latitude, device.longitude]);

	function renderMap() {
		if (position.lat && position.lng) {
			return (
				<ModalContent className="bg-gradient-to-b from-background to-background/95 backdrop-blur-lg">
					<ModalHeader className="flex items-center gap-3 border-b pb-4">
						<div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20 text-primary">
							<MapPin size={20} className="animate-pulse" />
						</div>
						<div className="flex flex-col">
							<span className="text-sm text-muted-foreground">Localização do dispositivo</span>
							<span className="text-lg font-bold text-white">{device.name}</span>
						</div>
					</ModalHeader>
					<ModalBody className="py-6">
						<motion.div
							className="flex flex-col gap-4"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5 }}
						>
							{locationName && (
								<motion.div
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									transition={{ delay: 0.2, duration: 0.5 }}
									className="group rounded-lg bg-black/30 p-3 shadow-lg"
								>
									<div className="flex items-start gap-2">
										<MapPin size={16} className="mt-0.5 text-primary" />
										<div>
											<div className="text-xs text-muted-foreground">Endereço completo</div>
											<div className="text-sm font-medium text-white">{locationName}</div>
										</div>
									</div>
								</motion.div>
							)}
							<motion.div
								initial={{ opacity: 0, scale: 0.95 }}
								animate={{ opacity: 1, scale: 1 }}
								transition={{ delay: 0.4, type: 'spring', stiffness: 200, damping: 20 }}
								className="overflow-hidden rounded-lg border-2 border-primary/30 bg-black/40 shadow-[0_0_15px_rgba(56,209,106,0.15)]"
							>
								<div className="border-b border-white/10 bg-black/60 px-4 py-2 text-xs font-medium text-muted-foreground">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<span>Visualização do mapa</span>
											<Badge variant="outline" className="text-xs">
												Somente visualização
											</Badge>
										</div>
										<div className="flex items-center gap-1">
											<span className="text-primary">{position.lat.toFixed(6)}</span>,
											<span className="text-primary">{position.lng.toFixed(6)}</span>
										</div>
									</div>
								</div>
								<motion.div
									className="pointer-events-none relative overflow-hidden"
									style={{ height: 400, width: '100%' }}
									whileHover={{ scale: 1.01 }}
									transition={{ type: 'spring', stiffness: 300, damping: 30 }}
								>
									<Map zoom={15} center={position} gestureHandling="greedy" disableDefaultUI colorScheme="DARK" styles={mapStyles}>
										<Marker position={position} title={device.name} />
									</Map>
								</motion.div>
							</motion.div>
						</motion.div>
					</ModalBody>
				</ModalContent>
			);
		} else {
			return (
				<ModalContent className="bg-gradient-to-b from-background to-background/95 backdrop-blur-lg">
					<ModalHeader className="flex items-center gap-3 border-b border-white/10 pb-4">
						<div className="flex h-10 w-10 items-center justify-center rounded-full bg-orange-500/20 text-orange-400">
							<MapPin size={20} className="animate-pulse" />
						</div>
						<div className="flex flex-col">
							<span className="text-sm text-muted-foreground">Sem localização</span>
							<span className="text-lg font-bold text-white">{device.name}</span>
						</div>
						<Button variant="ghost" size="icon" className="absolute right-4 top-4 ml-auto rounded-full" onClick={modalLocation.onClose}>
							<X size={18} />
						</Button>
					</ModalHeader>
					<ModalBody className="flex items-center justify-center py-12">
						<motion.div
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							className="flex flex-col items-center gap-4 text-center"
						>
							<div className="flex h-20 w-20 items-center justify-center rounded-full bg-orange-500/10">
								<MapPin size={40} className="text-orange-400/70" />
							</div>
							<div className="flex flex-col gap-2">
								<h3 className="text-xl font-bold">Localização não disponível</h3>
								<p className="text-muted-foreground">Este dispositivo não possui coordenadas de localização cadastradas.</p>
							</div>
						</motion.div>
					</ModalBody>
				</ModalContent>
			);
		}
	}

	return (
		<div className="flex flex-1 items-center justify-start">
			<Button
				size="sm"
				className="group border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 px-2 py-1 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 hover:shadow-md hover:shadow-primary/10 focus:ring-4 focus:ring-primary/30"
				onClick={modalLocation.onOpen}
			>
				<MapPin size={18} className="transition-transform duration-300 group-hover:rotate-[15deg] group-hover:scale-110" />
				<span className="ml-1 inline">Ver Localização</span>
			</Button>
			<Modal
				size={'3xl'}
				backdrop="blur"
				isOpen={modalLocation.isOpen}
				onOpenChange={modalLocation.onOpenChange}
				className="border border-white/10 bg-background/95 shadow-lg shadow-primary/20"
			>
				{renderMap()}
			</Modal>
		</div>
	);
}
