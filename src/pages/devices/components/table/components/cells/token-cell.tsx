import { Button } from '@/components/shadcnui/button';
import { useClipboard } from '@/hooks/use-clipboard';
import { IDevice } from '@/pages/devices/services/request/find-all';
import { Tooltip } from '@nextui-org/react';
import { Row } from '@tanstack/react-table';
import { Copy } from 'lucide-react';

export const TokenCell = ({ token, row, generateNewToken }: { token: string | null; row: Row<IDevice>; generateNewToken: () => void }) => {
	const { copy, isCopied } = useClipboard();

	return (
		<Tooltip
			showArrow
			isDisabled={!row?.original.token_expires_at || !row?.original.token_generated_at}
			className="w-[200px] rounded-lg border-[0.5px] border-primary/50 bg-background/80 text-sm font-normal text-white backdrop-blur-2xl"
			content={
				<div className="px-1 py-2">
					<div className="text-tiny">Gerado {new Date(row.original.token_generated_at).toLocaleString('pt-BR')}</div>
					<div className="text-tiny">Expira {new Date(row.original.token_expires_at).toLocaleString('pt-BR')}</div>
				</div>
			}
		>
			<div className="flex items-center gap-2">
				{token ? (
					<>
						<span className="font-mono text-sm text-gray-300">{token}</span>
						<Button variant="ghost" size="icon" className="h-6 w-6 p-0 hover:bg-gray-800" onClick={() => copy(token)}>
							{isCopied ? (
								<span className="text-green-500">
									<svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
										<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
									</svg>
								</span>
							) : (
								<Copy className="h-4 w-4 text-gray-400" />
							)}
						</Button>
					</>
				) : (
					<Button variant="outline" size="sm" onClick={generateNewToken} className="flex items-center gap-2 text-primary hover:bg-primary/10">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="16"
							height="16"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							strokeLinecap="round"
							strokeLinejoin="round"
						>
							<circle cx="12" cy="12" r="3" />
							<path d="M3 12h3m15 0h-3M12 3v3m0 15v-3" />
						</svg>
						Gerar Token
					</Button>
				)}
			</div>
		</Tooltip>
	);
};
