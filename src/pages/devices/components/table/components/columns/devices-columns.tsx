import { Button } from '@/components/shadcnui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
import { IDevice } from '@/pages/devices/services/request/find-all';
import { createSelectColumn } from '@/pages/presentations/components/table/columns/columns/select-column';
import { createTextColumn } from '@/pages/presentations/components/table/columns/columns/text-column';
import { CreateHeader } from '@/pages/presentations/components/table/columns/headers/create-header';
import { ColumnDef, Row } from '@tanstack/react-table';
import { Edit, KeyboardOff, MoreVertical, RefreshCw, Settings, Trash2 } from 'lucide-react';
import { LocationCell } from '../cells/location-cell';
import { TokenCell } from '../cells/token-cell';

export interface IDevicesActionsConfig {
	onEdit: (device: IDevice) => void;
	onDelete: ({ id }: { id: string }) => void;
	onNewToken: ({ id }: { id: string }) => void;
	onRemoveToken: ({ id }: { id: string }) => void;
}

type Props = IDevicesActionsConfig & {
	customColumns?: ColumnDef<IDevice>[];
};

const handleMultipleDevices = async (rows: Row<IDevice>[], callback: ({ id }: { id: string }) => void) => {
	for (const row of rows) {
		await callback({ id: row.original.id.toString() });
	}
};

export const createDevicesColumns = ({ onEdit, onDelete, onNewToken, onRemoveToken, customColumns = [] }: Props): ColumnDef<IDevice>[] => [
	createSelectColumn<IDevice>(),
	createTextColumn<IDevice>({
		accessorKey: 'name',
		header: 'Nome',
		className: 'font-medium text-white',
	}),
	{
		accessorKey: 'status',
		id: 'status',
		header: ({ column }) => {
			return <CreateHeader title="Status" column={column} />;
		},
		cell: ({ row }) => {
			const online = row.original.status;
			return (
				<div className="flex flex-1 items-center justify-start">
					<div
						className={`flex items-center rounded-full border px-2 py-1 ${online ? 'bg-green-500 bg-green-500/10 text-green-500' : 'border-red-500 bg-red-500/10 text-red-500'}`}
					>
						<span
							className={`mr-2 inline-block h-2 w-2 rounded-full ${online ? 'bg-green-500' : 'bg-red-500'}`}
							aria-label={online ? 'Online' : 'Offline'}
						></span>
						<span className="text-sm font-medium">{online ? 'Online' : 'Inativo'}</span>
					</div>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true,
	},
	createTextColumn<IDevice>({
		accessorKey: 'type',
		header: 'Tipo',
		className: 'text-gray-300',
	}),
	createTextColumn<IDevice>({
		accessorKey: 'brand',
		header: 'Marca',
		className: 'text-gray-300',
	}),
	createTextColumn<IDevice>({
		accessorKey: 'model',
		header: 'Modelo',
		className: 'text-gray-300',
	}),
	createTextColumn<IDevice>({
		accessorKey: 'resolution',
		header: 'Resolução',
		className: 'text-gray-300',
	}),
	{
		accessorKey: 'location',
		id: 'localização',
		header: ({ column }) => {
			return <CreateHeader title="Localização" column={column} />;
		},
		cell: ({ row }) => {
			return <LocationCell row={row} />;
		},
		enableSorting: true,
		enableHiding: true,
	},
	{
		id: 'token',
		header: ({ column }) => {
			return <CreateHeader title="Token" column={column} />;
		},
		cell: ({ row }) => {
			const token = row.original.token;
			return <TokenCell token={token} row={row} generateNewToken={() => onNewToken({ id: row.original.id })} />;
		},
		enableSorting: true,
	},
	{
		id: 'actions',
		header: () => (
			<div className="flex w-full items-center justify-end pr-2">
				<Settings className="h-4 w-4" />
			</div>
		),
		cell: ({ row, table }) => {
			const isSelected = row.getIsSelected();
			const selectedRows = table.getSelectedRowModel().rows;
			const hasMultipleSelected = selectedRows.length > 1;
			if (hasMultipleSelected && !isSelected) return null;
			const handleAction = async (action: ({ id }: { id: string }) => void, multiple: boolean) => {
				if (multiple) {
					await handleMultipleDevices(selectedRows, action);
				} else {
					await action({ id: row.original.id.toString() });
				}
			};

			return (
				<div className="flex items-center justify-end">
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="ghost" size="icon" className="h-8 w-8 p-0">
								<MoreVertical className="h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-48">
							{!hasMultipleSelected && (
								<DropdownMenuItem
									onClick={() => onEdit(row.original)}
									className="cursor-pointer text-blue-500 hover:bg-blue-100 focus:text-blue-700"
								>
									<Edit className="mr-2 h-4 w-4" />
									<span>Editar</span>
								</DropdownMenuItem>
							)}
							<DropdownMenuItem
								onClick={() => handleAction(onNewToken, hasMultipleSelected)}
								className="cursor-pointer text-green-500 hover:bg-green-100 focus:text-green-700"
							>
								<RefreshCw className="mr-2 h-4 w-4" />
								<span>{hasMultipleSelected ? 'Gerar novos Tokens' : 'Gerar novo Token'}</span>
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() => handleAction(onRemoveToken, hasMultipleSelected)}
								className="cursor-pointer text-yellow-500 hover:bg-yellow-100 focus:text-yellow-700"
							>
								<KeyboardOff className="mr-2 h-4 w-4" />
								<span>{hasMultipleSelected ? 'Remover Tokens' : 'Remover Token'}</span>
							</DropdownMenuItem>
							<DropdownMenuSeparator />
							<DropdownMenuItem
								onClick={() => {
									if (hasMultipleSelected) {
										onDelete({ id: selectedRows.map((r) => r.original.id.toString()).join(',') });
									} else {
										onDelete({ id: row.original.id.toString() });
									}
								}}
								className="cursor-pointer text-destructive focus:text-destructive"
							>
								<Trash2 className="mr-2 h-4 w-4" />
								<span>{hasMultipleSelected ? 'Excluir Selecionados' : 'Excluir'}</span>
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			);
		},
		enableSorting: false,
		enableHiding: true,
	},
	...customColumns,
];
