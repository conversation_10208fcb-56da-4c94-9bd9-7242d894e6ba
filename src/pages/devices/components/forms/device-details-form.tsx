import { Input } from '@/components/shadcnui/input';
import { InputHelper } from '@/components/shadcnui/input-helper';
import { ArrowUpDown, Maximize2, Ruler } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

interface DeviceDetailsFormProps {
	readonly methods: UseFormReturn<any>;
}

export function DeviceDetailsForm({ methods }: Readonly<DeviceDetailsFormProps>) {
	const {
		register,
		formState: { errors },
	} = methods;

	return (
		<div className="mt-4 flex flex-col gap-6">
			<div className="flex flex-wrap gap-4">
				<div className="min-w-[180px] flex-1">
					<label htmlFor="width" className="mb-1 flex items-center gap-2 font-medium text-white">
						<Maximize2 className="h-5 w-5 text-green-500" />
						Largura (px)
					</label>
					<Input
						type="number"
						min={0}
						id="width"
						placeholder="Largura do dispositivo"
						{...register('width', { valueAsNumber: true })}
						className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
					/>
					<span className="block min-h-[20px]">
						{errors.width && <span className="text-sm text-red-500">{errors.width.message as string}</span>}
					</span>
				</div>
				<div className="min-w-[180px] flex-1">
					<label htmlFor="height" className="mb-1 flex items-center gap-2 font-medium text-white">
						<ArrowUpDown className="h-5 w-5 text-green-500" />
						Altura (px)
					</label>
					<Input
						type="number"
						min={0}
						id="height"
						placeholder="Altura do dispositivo"
						{...register('height', { valueAsNumber: true })}
						className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
					/>
					<span className="block min-h-[20px]">
						{errors.height && <span className="text-sm text-red-500">{errors.height.message as string}</span>}
					</span>
				</div>
			</div>
			<div>
				<label htmlFor="resolution" className="mb-1 flex items-center gap-2 font-medium text-white">
					<Ruler className="h-5 w-5 text-green-500" />
					Resolução
				</label>
				<Input
					id="resolution"
					placeholder="Ex: 1080p, FullHD, 4K, etc..."
					{...register('resolution')}
					className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
				/>
				<InputHelper>Resolução da tela do dispositivo</InputHelper>
				<span className="block min-h-[20px]">
					{errors.resolution && <span className="text-sm text-red-500">{errors.resolution.message as string}</span>}
				</span>
			</div>
		</div>
	);
}
