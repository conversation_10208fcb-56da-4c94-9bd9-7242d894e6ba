import { Input } from '@/components/shadcnui/input';
import { InputHelper } from '@/components/shadcnui/input-helper';
import { Select, SelectItem } from '@nextui-org/react';
import { BadgeInfo, Monitor, Tag, Type } from 'lucide-react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { DeviceTypeSchema, IDeviceSchema } from '../../validators/create-device-schema.form';

interface DeviceBasicInformationFormProps {
	readonly methods: UseFormReturn<IDeviceSchema>;
}

const deviceTypes = ['TV', 'Telão', 'Notebook', 'Computador'] as const;

export function DeviceBasicInformationForm({ methods }: Readonly<DeviceBasicInformationFormProps>) {
	const {
		register,
		formState: { errors },
	} = methods;

	return (
		<div className="mt-4 flex flex-col gap-6">
			<div className="flex flex-wrap gap-4">
				<div className="min-w-[180px] flex-1">
					<label htmlFor="name" className="mb-1 flex items-center gap-2 font-medium text-white">
						<Monitor className="h-5 w-5 text-green-500" />
						Nome
					</label>
					<Input
						id="name"
						placeholder="Ex: Telão Showroom Camboriú"
						{...register('name')}
						className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
					/>
					<InputHelper>Nome identificador do dispositivo</InputHelper>
					<span className="block min-h-[20px]">
						{errors.name && <span className="text-sm text-red-500">{errors.name.message as string}</span>}
					</span>
				</div>
				<div className="min-w-[180px] flex-1">
					<label htmlFor="type" className="mb-1 flex items-center gap-2 font-medium text-white">
						<Type className="h-5 w-5 text-green-500" />
						Tipo
					</label>
					<Controller
						control={methods.control}
						name="type"
						render={({ field }) => (
							<Select
								placeholder="Selecione um tipo"
								variant="bordered"
								classNames={{
									trigger: 'rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0',
								}}
								selectedKeys={field.value ? [field.value] : []}
								onChange={(e) => field.onChange(e.target.value as DeviceTypeSchema)}
							>
								{deviceTypes.map((type) => (
									<SelectItem key={type}>{type}</SelectItem>
								))}
							</Select>
						)}
					/>
				</div>
			</div>
			<div className="flex flex-wrap gap-4">
				<div className="min-w-[180px] flex-1">
					<label htmlFor="brand" className="mb-1 flex items-center gap-2 font-medium text-white">
						<Tag className="h-5 w-5 text-green-500" />
						Marca
					</label>
					<Input
						id="brand"
						placeholder="Marca do dispositivo"
						{...register('brand')}
						className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
					/>
					<InputHelper>Marca do dispositivo</InputHelper>
					<span className="block min-h-[20px]">
						{errors.brand && <span className="text-sm text-red-500">{errors.brand.message as string}</span>}
					</span>
				</div>
				<div className="min-w-[180px] flex-1">
					<label htmlFor="model" className="mb-1 flex items-center gap-2 font-medium text-white">
						<BadgeInfo className="h-5 w-5 text-green-500" />
						Modelo
					</label>
					<Input
						id="model"
						placeholder="Modelo do dispositivo"
						{...register('model')}
						className="rounded-md border border-white/10 bg-black/20 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
					/>
					<InputHelper>Modelo do dispositivo</InputHelper>
					<span className="block min-h-[20px]">
						{errors.model && <span className="text-sm text-red-500">{errors.model.message as string}</span>}
					</span>
				</div>
			</div>
		</div>
	);
}
