import { Badge } from '@/components/shadcnui/badge';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import useDebounce from '@/hooks/use-debounce';
import { Map, Marker } from '@vis.gl/react-google-maps';
import { AnimatePresence, motion } from 'framer-motion';
import { Search } from 'lucide-react';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface DeviceLocationFormProps {
	children?: ReactNode;
	methods: UseFormReturn<any>;
	locationSearch: string;
	setLocationSearch: (value: string) => void;
	stringLocation: string;
	markerPosition: google.maps.LatLngLiteral;
	onMarkerDragEnd: (e: google.maps.MapMouseEvent) => void;
}

interface LocationSuggestion {
	description: string;
	position: {
		lat: number;
		lng: number;
	};
}

export const mapStyles = [
	{ elementType: 'geometry', stylers: [{ color: '#212121' }] },
	{ elementType: 'labels.text.stroke', stylers: [{ color: '#242424' }] },
	{ elementType: 'labels.text.fill', stylers: [{ color: '#a0a0a0' }] },
	{ featureType: 'administrative', elementType: 'geometry.fill', stylers: [{ color: '#1a1a1a' }] },
	{ featureType: 'administrative.locality', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'poi', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'poi.park', elementType: 'geometry', stylers: [{ color: '#1e3229' }] },
	{ featureType: 'poi.business', elementType: 'labels.icon', stylers: [{ saturation: -25 }] },
	{ featureType: 'road', elementType: 'geometry', stylers: [{ color: '#2c2c2c' }] },
	{ featureType: 'road', elementType: 'geometry.stroke', stylers: [{ color: '#242424' }] },
	{ featureType: 'road', elementType: 'labels.text.fill', stylers: [{ color: '#9ca3af' }] },
	{ featureType: 'road.arterial', elementType: 'geometry', stylers: [{ color: '#373737' }] },
	{ featureType: 'road.highway', elementType: 'geometry', stylers: [{ color: '#3c3c3c' }] },
	{ featureType: 'road.highway', elementType: 'geometry.stroke', stylers: [{ color: '#242424' }] },
	{ featureType: 'road.highway', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'transit', elementType: 'geometry', stylers: [{ color: '#2d2d2d' }] },
	{ featureType: 'transit.station', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'water', elementType: 'geometry', stylers: [{ color: '#151a1f' }] },
	{ featureType: 'water', elementType: 'labels.text.fill', stylers: [{ color: '#5e7b91' }] },
];

export function DeviceLocationForm({
	methods,
	locationSearch,
	setLocationSearch,
	stringLocation,
	markerPosition,
	onMarkerDragEnd,
}: Readonly<DeviceLocationFormProps>) {
	const error = methods.formState.errors.location?.message as string | undefined;
	const [open, setOpen] = useState(false);
	const [isSearching, setIsSearching] = useState(false);
	const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);
	const mapRef = useRef<HTMLDivElement>(null);
	const [searchTerm, setSearchTerm] = useState(locationSearch);
	const debouncedSearchTerm = useDebounce(searchTerm, 500);
	const [isFocused, setIsFocused] = useState(false);

	useEffect(() => {
		if (debouncedSearchTerm?.trim()) {
			handleGeocodingSearch(debouncedSearchTerm);
		} else {
			setSuggestions([]);
			setOpen(false);
		}
	}, [debouncedSearchTerm]);

	const handleSearchChange = (input: string) => {
		setSearchTerm(input);
		setLocationSearch(input);

		if (!input.trim()) {
			setSuggestions([]);
			setOpen(false);
		} else {
			setOpen(true);
		}
	};

	const handleGeocodingSearch = async (input: string) => {
		if (!input.trim()) return;
		setIsSearching(true);

		try {
			if (window.google?.maps) {
				const geocoder = new window.google.maps.Geocoder();
				geocoder.geocode(
					{
						address: input,
						componentRestrictions: { country: 'br' },
					},
					(results, status) => {
						setIsSearching(false);
						if (status === window.google.maps.GeocoderStatus.OK && results && results.length > 0) {
							const formattedSuggestions = results.map((result) => ({
								description: result.formatted_address || '',
								position: {
									lat: result.geometry.location.lat(),
									lng: result.geometry.location.lng(),
								},
							}));
							setSuggestions(formattedSuggestions);
						} else {
							setSuggestions([]);
						}
					},
				);
			}
		} catch {
			setIsSearching(false);
			setSuggestions([]);
		}
	};

	return (
		<motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="flex flex-col gap-6">
			<div className="mt-4 rounded-lg">
				<Label htmlFor="location-search" className="w-full">
					<motion.div
						initial={false}
						animate={{ scale: isFocused ? 1.02 : 1 }}
						transition={{ type: 'spring', stiffness: 200, damping: 20 }}
						className="mb-3 flex items-center gap-2"
					>
						<motion.div animate={{ rotate: isFocused ? 360 : 0 }} transition={{ duration: 0.5 }}>
							<Search className="h-5 w-5 text-primary" />
						</motion.div>
						<span className="text-lg font-medium">Digite o endereço do dispositivo</span>
					</motion.div>
					<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.2 }} className="mb-4 mt-1 text-xs text-gray-400">
						Digite e selecione o endereço completo para localizar o dispositivo
					</motion.div>
					<div className="flex w-full items-end gap-2">
						<div className="relative w-full">
							<div className="relative w-full">
								<motion.div
									animate={{
										scale: isFocused ? 1.01 : 1,
									}}
									transition={{ type: 'spring', stiffness: 300, damping: 25 }}
								>
									<Input
										id="location-search"
										placeholder="Ex: Avenida Brasil, São Paulo..."
										value={locationSearch}
										onChange={(e) => handleSearchChange(e.target.value)}
										onFocus={() => setIsFocused(true)}
										onBlur={() => setIsFocused(false)}
										onKeyDown={(e) => {
											if (e.key === 'Enter') {
												e.preventDefault();
											}
										}}
										className="rounded-md border border-white/10 bg-black/20 pr-10 text-white shadow-sm placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
									/>
								</motion.div>
								<motion.div
									animate={{
										scale: isSearching ? 1.1 : 1,
										rotate: isSearching ? 360 : 0,
									}}
									transition={{
										duration: 0.5,
										repeat: isSearching ? Infinity : 0,
										ease: 'linear',
									}}
									className="pointer-events-none absolute inset-y-0 right-3 flex items-center"
									style={{ top: 0, bottom: 0 }}
								>
									<Search className="h-4 w-4 text-gray-400" />
								</motion.div>
								{open &&
									(() => {
										let dropdownContent;
										if (isSearching) {
											dropdownContent = (
												<div className="flex items-center gap-2 px-3 py-4 text-sm text-gray-400">
													<div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
													Buscando endereços...
												</div>
											);
										} else if (suggestions.length === 0) {
											dropdownContent = (
												<div className="flex flex-col gap-2 px-3 py-4 text-sm">
													<div className="flex items-center gap-2 text-gray-400">
														<Search className="h-4 w-4 text-gray-400" />
														Nenhum resultado encontrado.
													</div>
													<div className="text-xs italic text-gray-500">
														Tente digitar um endereço mais completo, incluindo cidade e estado
													</div>
												</div>
											);
										} else {
											dropdownContent = (
												<div className="max-h-[250px] overflow-y-auto py-1">
													<div className="mb-1 rounded bg-primary/10 p-2 text-xs text-gray-300">
														Selecione um endereço da lista abaixo:
													</div>
													<div className="flex flex-col gap-1">
														{suggestions.map((suggestion) => (
															<button
																type="button"
																key={`location-${suggestion.position.lat}-${suggestion.position.lng}`}
																className="flex w-full items-start gap-2 rounded px-3 py-2 text-left text-sm text-white hover:bg-primary/20 focus:bg-primary/30"
																onClick={() => {
																	methods.setValue('location', [
																		suggestion.position.lat,
																		suggestion.position.lng,
																	]);
																	setLocationSearch(suggestion.description);
																	onMarkerDragEnd({
																		latLng: new window.google.maps.LatLng(
																			suggestion.position.lat,
																			suggestion.position.lng,
																		),
																	} as google.maps.MapMouseEvent);
																	setOpen(false);
																}}
															>
																{suggestion.description}
															</button>
														))}
													</div>
												</div>
											);
										}
										return (
											<AnimatePresence>
												<motion.div
													className="absolute z-50 mt-1 w-full"
													initial={{ opacity: 0, y: -8, scale: 0.95 }}
													animate={{ opacity: 1, y: 0, scale: 1 }}
													exit={{ opacity: 0, y: -8, scale: 0.95 }}
													transition={{
														duration: 0.2,
														type: 'spring',
														stiffness: 500,
														damping: 30,
													}}
												>
													<div className="w-full overflow-hidden rounded-md border border-white/10 bg-black/90 p-1 shadow-lg backdrop-blur-sm">
														{dropdownContent}
													</div>
												</motion.div>
											</AnimatePresence>
										);
									})()}
							</div>
						</div>
					</div>
				</Label>
				<motion.span
					className="block min-h-[20px]"
					initial={false}
					animate={{ scale: error ? 1.05 : 1 }}
					transition={{ type: 'spring', stiffness: 300, damping: 20 }}
				>
					{error && (
						<motion.span initial={{ opacity: 0, x: -10 }} animate={{ opacity: 1, x: 0 }} className="text-sm text-red-500">
							{error}
						</motion.span>
					)}
				</motion.span>
				<motion.div
					className="flex flex-col gap-2"
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.3, duration: 0.5 }}
				>
					<div className="flex items-center gap-2">
						<motion.div
							initial={false}
							animate={{ scale: locationSearch ? 1.05 : 1 }}
							transition={{ type: 'spring', stiffness: 300, damping: 20 }}
						>
							<Badge variant="default" className="w-fit bg-primary/20 text-primary">
								{stringLocation}
							</Badge>
						</motion.div>
					</div>
					<motion.div
						initial={{ opacity: 0, scale: 0.95 }}
						animate={{ opacity: 1, scale: 1 }}
						transition={{ delay: 0.4, type: 'spring', stiffness: 200, damping: 20 }}
						className="rounded-md border border-white/10 bg-black/30 p-3"
					>
						<div className="mb-2 flex items-center gap-2">
							<span className="text-sm font-medium">Visualização do mapa</span>
							<Badge variant="outline" className="text-xs">
								Somente visualização
							</Badge>
						</div>
						<motion.div
							ref={mapRef}
							className="pointer-events-none relative overflow-hidden rounded-md border-2 border-primary/40"
							style={{ height: 384, width: '100%' }}
							whileHover={{ scale: 1.01 }}
							transition={{ type: 'spring', stiffness: 300, damping: 30 }}
						>
							<Map zoom={13} center={markerPosition} gestureHandling="greedy" disableDefaultUI colorScheme="DARK" styles={mapStyles}>
								<Marker position={markerPosition} draggable onDragEnd={onMarkerDragEnd} />
							</Map>
							<motion.div
								className="absolute right-2 top-2"
								initial={{ opacity: 0, y: -10 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: 0.5 }}
							>
								<Badge variant="secondary" className="bg-black/70 px-2 text-xs">
									Localização selecionada
								</Badge>
							</motion.div>
						</motion.div>
					</motion.div>
				</motion.div>
			</div>
		</motion.div>
	);
}
