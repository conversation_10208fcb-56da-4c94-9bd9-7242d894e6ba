import { Button } from '@/components/shadcnui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/shadcnui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/shadcnui/table';
import { cn } from '@/shared/lib/utils';
import { LucidePlus, MonitorSmartphone } from 'lucide-react';
import { Schedule } from './types';
import { MOCK_DEVICES } from './utils';

interface DevicesTableProps {
	selectedDevice?: string;
	onDeviceSelect: (deviceId: string | undefined) => void;
	schedules: Schedule[];
	onCreateSchedule: (deviceId: string) => void;
}

export const DevicesTable = ({ selectedDevice, onDeviceSelect, schedules, onCreateSchedule }: DevicesTableProps) => {
	return (
		<Card className="h-full w-full bg-muted">
			<CardHeader className="pb-3">
				<CardTitle className="flex items-center gap-2">
					<MonitorSmartphone /> Dispositivos
				</CardTitle>
				<CardDescription>G<PERSON><PERSON><PERSON> as programações por dispositivo</CardDescription>
			</CardHeader>
			<CardContent className="bg-black/15 p-2">
				<Table>
					<TableHeader className="bg-black/20 backdrop:blur-2xl">
						<TableRow>
							<TableHead>Nome</TableHead>
							<TableHead>Tipo</TableHead>
							<TableHead>Status</TableHead>
							<TableHead>Programações Ativas</TableHead>
							<TableHead className="text-right">Ações</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{MOCK_DEVICES.map((device) => {
							const deviceSchedules = schedules.filter((s) => s.deviceId === device.id);
							const activeSchedules = deviceSchedules.filter((s) => s.endTime > new Date() || s.recurrence !== 'none');

							return (
								<TableRow
									key={device.id}
									className={cn('cursor-pointer', selectedDevice === device.id && 'bg-accent/50')}
									onClick={() => onDeviceSelect(selectedDevice === device.id ? undefined : device.id)}
								>
									<TableCell className="font-medium">{device.name}</TableCell>
									<TableCell>{device.type}</TableCell>
									<TableCell>
										<span className={`inline-block h-2 w-2 rounded-full ${device.status ? 'bg-green-500' : 'bg-red-500'}`} />
										<span className="ml-2 text-xs">{device.status ? 'Online' : 'Offline'}</span>
									</TableCell>
									<TableCell>{activeSchedules.length}</TableCell>
									<TableCell className="text-right">
										<Button
											variant="ghost"
											size="sm"
											className="h-8 w-8 p-0"
											onClick={(e) => {
												e.stopPropagation();
												onCreateSchedule(device.id);
											}}
										>
											<LucidePlus className="h-4 w-4" />
											<span className="sr-only">Criar programação</span>
										</Button>
									</TableCell>
								</TableRow>
							);
						})}
					</TableBody>
				</Table>
			</CardContent>
		</Card>
	);
};
