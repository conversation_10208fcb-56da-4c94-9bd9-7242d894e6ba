import { Button } from '@/components/shadcnui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/shadcnui/tooltip';
import { motion } from 'framer-motion';
import { LucideRepeat, LucideTrash } from 'lucide-react';
import { Schedule } from './types';
import { getRecurrenceText, getTimeString } from './utils';

interface ScheduleItemProps {
	schedule: Schedule;
	onDelete: (id: string) => void;
}

export const ScheduleItem = ({ schedule, onDelete }: ScheduleItemProps) => (
	<motion.div
		initial={{ opacity: 0, y: 10 }}
		animate={{ opacity: 1, y: 0 }}
		className="group mb-2 rounded-md border border-border bg-card p-3 shadow-sm transition-all hover:border-primary/20"
	>
		<div className="flex items-center justify-between">
			<div className="flex flex-col">
				<div className="flex items-center gap-2">
					<span className="text-sm font-semibold text-foreground">{schedule.presentationTitle}</span>
					{schedule.recurrence !== 'none' && (
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger>
									<LucideRepeat className="h-4 w-4 text-muted-foreground" />
								</TooltipTrigger>
								<TooltipContent>
									<p>{getRecurrenceText(schedule.recurrence)}</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					)}
				</div>
				<span className="text-xs text-muted-foreground">{schedule.deviceName}</span>
			</div>
			<div className="flex items-center gap-2">
				<span className="text-xs font-medium">
					{getTimeString(schedule.startTime)} - {getTimeString(schedule.endTime)}
				</span>
				<Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100" onClick={() => onDelete(schedule.id)}>
					<LucideTrash className="h-4 w-4 text-destructive" />
				</Button>
			</div>
		</div>
	</motion.div>
);
