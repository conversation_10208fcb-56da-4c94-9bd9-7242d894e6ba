import { Card } from '@/components/shadcnui/card';
import { addDays, format, startOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Schedule } from './types';

interface WeeklyViewProps {
	schedules: Schedule[];
	selectedDate: Date | undefined;
}

export const WeeklyView = ({ schedules, selectedDate }: WeeklyViewProps) => {
	if (!selectedDate) return null;

	const startOfWeekDate = startOfWeek(selectedDate, { locale: ptBR });
	const weekDays = Array.from({ length: 7 }).map((_, i) => addDays(startOfWeekDate, i));

	const getSchedulesForDate = (date: Date) => {
		return schedules.filter((schedule) => {
			const scheduleDate = new Date(schedule.startTime);
			return (
				scheduleDate.getDate() === date.getDate() &&
				scheduleDate.getMonth() === date.getMonth() &&
				scheduleDate.getFullYear() === date.getFullYear()
			);
		});
	};

	return (
		<div className="grid grid-cols-7 gap-4">
			{weekDays.map((day) => {
				const daySchedules = getSchedulesForDate(day);
				return (
					<Card key={day.toString()} className="p-3">
						<div className="text-center">
							<div className="text-sm font-medium">{format(day, 'EEEE', { locale: ptBR })}</div>
							<div className="mt-1 text-2xl font-bold">{format(day, 'd', { locale: ptBR })}</div>
						</div>
						<div className="mt-3 space-y-2">
							{daySchedules.length > 0 ? (
								daySchedules.map((schedule) => (
									<div
										key={schedule.id}
										className="rounded-md bg-primary/10 p-2 text-xs"
										style={{ borderLeft: `3px solid var(--primary)` }}
									>
										<div className="font-medium">{schedule.presentationTitle}</div>
										<div className="text-muted-foreground">{format(new Date(schedule.startTime), 'HH:mm')}</div>
										<div className="mt-1 text-[10px] text-muted-foreground">{schedule.deviceName}</div>
									</div>
								))
							) : (
								<div className="flex h-20 items-center justify-center text-xs text-muted-foreground">Sem programações</div>
							)}
						</div>
					</Card>
				);
			})}
		</div>
	);
};
