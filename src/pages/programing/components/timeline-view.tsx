import { Schedule } from './types';
import { MOCK_DEVICES, getTimeString } from './utils';

const getHourKey = (hour: number) => `hour-${hour}`;
const getCellKey = (deviceId: string, hour: number) => `${deviceId}-cell-${hour}`;

interface TimelineViewProps {
	filteredSchedules: Schedule[];
}

export const TimelineView = ({ filteredSchedules }: TimelineViewProps) => {
	const deviceIds = filteredSchedules.map((s) => s.deviceId).filter((id, index, arr) => arr.indexOf(id) === index);
	const devices = MOCK_DEVICES.filter((d) => deviceIds.includes(d.id));

	if (devices.length === 0) {
		return (
			<div className="flex h-48 flex-col items-center justify-center rounded-md border border-dashed">
				<p className="text-muted-foreground">Nenhuma programação para a data selecionada</p>
			</div>
		);
	}

	return (
		<div className="overflow-x-auto">
			<div className="w-full" style={{ minWidth: 'max(600px, 100%)' }}>
				<div className="ml-[200px] flex border-b">
					{Array.from({ length: 24 }).map((_, i) => (
						<div key={getHourKey(i)} className="bocner-r h-12 flex-1 px-2 pt-1 text-center text-xs font-medium text-muted-foreground">
							{`${i.toString().padStart(2, '0')}:00`}
						</div>
					))}
				</div>
				{devices.map((device) => {
					const deviceSchedules = filteredSchedules.filter((s) => s.deviceId === device.id);
					return (
						<div key={device.id} className="flex">
							<div className="flex h-16 w-[200px] flex-shrink-0 items-center border-b border-r px-4 py-2">
								<div>
									<div className="flex items-center gap-2">
										<span className="text-sm font-semibold">{device.name}</span>
										<span className={`h-2 w-2 rounded-full ${device.status ? 'bg-green-500' : 'bg-red-500'}`} />
									</div>
									<span className="text-xs text-muted-foreground">{device.type}</span>
								</div>
							</div>
							<div className="relative flex h-16 flex-1 border-b">
								{Array.from({ length: 24 }).map((_, i) => (
									<div key={getCellKey(device.id, i)} className="h-16 flex-1 border-r"></div>
								))}
								{deviceSchedules.map((schedule) => {
									const startHour = schedule.startTime.getHours() + schedule.startTime.getMinutes() / 60;
									const endHour = schedule.endTime.getHours() + schedule.endTime.getMinutes() / 60;
									const duration = endHour - startHour;
									const cellWidth = 100 / 24; // Cada célula representa 4.166% da largura total

									return (
										<div
											key={schedule.id}
											className="absolute top-2 z-10 h-12 cursor-pointer rounded bg-primary/80 px-2 py-1 text-xs text-white"
											style={{
												left: `${startHour * cellWidth}%`,
												width: `${duration * cellWidth}%`,
												zIndex: schedule.priority,
											}}
											title={`${schedule.presentationTitle} (${getTimeString(schedule.startTime)}-${getTimeString(schedule.endTime)})`}
										>
											<div className="truncate">{schedule.presentationTitle}</div>
											<div className="text-[10px] opacity-90">
												{getTimeString(schedule.startTime)}-{getTimeString(schedule.endTime)}
											</div>
										</div>
									);
								})}
							</div>
						</div>
					);
				})}
			</div>
		</div>
	);
};
