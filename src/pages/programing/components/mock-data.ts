import { addDays, addHours, startOfToday } from 'date-fns';
import { Schedule } from './types';

const today = startOfToday();

export const MOCK_SCHEDULES: Schedule[] = [
	{
		id: 's1',
		deviceId: 'd1',
		deviceName: 'TV Recepção',
		presentationId: 'p1',
		presentationTitle: 'Bem-vindo à Empresa',
		startTime: addHours(today, 8),
		endTime: addHours(today, 18),
		recurrence: 'daily',
		priority: 1,
	},
	{
		id: 's2',
		deviceId: 'd2',
		deviceName: 'Totem Hall Principal',
		presentationId: 'p2',
		presentationTitle: 'Produtos em Destaque',
		startTime: addHours(today, 9),
		endTime: addHours(today, 17),
		recurrence: 'none',
		priority: 2,
	},
	{
		id: 's3',
		deviceId: 'd4',
		deviceName: 'TV Sala de Reuniões',
		presentationId: 'p3',
		presentationTitle: 'Orientações de Segurança',
		startTime: addHours(today, 10),
		endTime: addHours(addDays(today, 1), 19),
		recurrence: 'weekly',
		priority: 3,
	},
];
