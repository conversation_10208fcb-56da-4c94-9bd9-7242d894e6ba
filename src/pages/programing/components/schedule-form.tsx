import { Button } from '@/components/shadcnui/button';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/shadcnui/select';
import { SheetClose, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle } from '@/components/shadcnui/sheet';
import { format } from 'date-fns';
import { Schedule } from './types';
import { MOCK_DEVICES, MOCK_PRESENTATIONS, getDurationText } from './utils';

interface ScheduleFormProps {
	newSchedule: Partial<Schedule>;
	onScheduleChange: (schedule: Partial<Schedule>) => void;
	onSubmit: () => void;
}

export const ScheduleForm = ({ newSchedule, onScheduleChange, onSubmit }: ScheduleFormProps) => {
	return (
		<SheetContent className="sm:max-w-md">
			<SheetHeader>
				<SheetTitle>Nova Programação</SheetTitle>
				<SheetDescription>Crie uma nova programação para exibir uma apresentação em um dispositivo.</SheetDescription>
			</SheetHeader>

			<div className="grid gap-6 py-6">
				<div className="grid gap-3">
					<Label htmlFor="device">Dispositivo</Label>
					<Select value={newSchedule.deviceId} onValueChange={(value: string) => onScheduleChange({ ...newSchedule, deviceId: value })}>
						<SelectTrigger>
							<SelectValue placeholder="Selecione um dispositivo" />
						</SelectTrigger>
						<SelectContent>
							{MOCK_DEVICES.map((device) => (
								<SelectItem key={device.id} value={device.id}>
									{device.name} {!device.status && '(Offline)'}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="grid gap-3">
					<Label htmlFor="presentation">Apresentação</Label>
					<Select
						value={newSchedule.presentationId}
						onValueChange={(value: string) => onScheduleChange({ ...newSchedule, presentationId: value })}
					>
						<SelectTrigger>
							<SelectValue placeholder="Selecione uma apresentação" />
						</SelectTrigger>
						<SelectContent>
							{MOCK_PRESENTATIONS.map((presentation) => (
								<SelectItem key={presentation.id} value={presentation.id}>
									{presentation.title} ({getDurationText(presentation.duration)})
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<div className="grid gap-3">
						<Label htmlFor="date">Data de Início</Label>
						<Input
							type="date"
							value={newSchedule.startTime ? format(newSchedule.startTime, 'yyyy-MM-dd') : ''}
							onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
								const date = new Date(e.target.value);
								if (isNaN(date.getTime())) return;
								const newDate = new Date(newSchedule.startTime || new Date());
								newDate.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
								onScheduleChange({ ...newSchedule, startTime: newDate });
							}}
						/>
					</div>
					<div className="grid gap-3">
						<Label htmlFor="time">Hora de Início</Label>
						<Input
							type="time"
							value={newSchedule.startTime ? format(newSchedule.startTime, 'HH:mm') : ''}
							onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
								const [hours, minutes] = e.target.value.split(':').map(Number);
								const newDate = new Date(newSchedule.startTime || new Date());
								newDate.setHours(hours, minutes);
								onScheduleChange({ ...newSchedule, startTime: newDate });
							}}
						/>
					</div>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<div className="grid gap-3">
						<Label htmlFor="endDate">Data de Término</Label>
						<Input
							type="date"
							value={newSchedule.endTime ? format(newSchedule.endTime, 'yyyy-MM-dd') : ''}
							onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
								const date = new Date(e.target.value);
								if (isNaN(date.getTime())) return;
								const newDate = new Date(newSchedule.endTime || new Date());
								newDate.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
								onScheduleChange({ ...newSchedule, endTime: newDate });
							}}
						/>
					</div>
					<div className="grid gap-3">
						<Label htmlFor="endTime">Hora de Término</Label>
						<Input
							type="time"
							value={newSchedule.endTime ? format(newSchedule.endTime, 'HH:mm') : ''}
							onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
								const [hours, minutes] = e.target.value.split(':').map(Number);
								const newDate = new Date(newSchedule.endTime || new Date());
								newDate.setHours(hours, minutes);
								onScheduleChange({ ...newSchedule, endTime: newDate });
							}}
						/>
					</div>
				</div>

				<div className="grid gap-3">
					<Label htmlFor="recurrence">Recorrência</Label>
					<Select
						value={newSchedule.recurrence}
						onValueChange={(value: 'none' | 'daily' | 'weekly' | 'monthly') => onScheduleChange({ ...newSchedule, recurrence: value })}
					>
						<SelectTrigger>
							<SelectValue placeholder="Selecione a recorrência" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="none">Sem recorrência</SelectItem>
							<SelectItem value="daily">Diariamente</SelectItem>
							<SelectItem value="weekly">Semanalmente</SelectItem>
							<SelectItem value="monthly">Mensalmente</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="grid gap-3">
					<Label htmlFor="priority">Prioridade</Label>
					<Select
						value={String(newSchedule.priority)}
						onValueChange={(value: string) => onScheduleChange({ ...newSchedule, priority: parseInt(value) })}
					>
						<SelectTrigger>
							<SelectValue placeholder="Selecione a prioridade" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="1">1 (Baixa)</SelectItem>
							<SelectItem value="2">2 (Média)</SelectItem>
							<SelectItem value="3">3 (Alta)</SelectItem>
						</SelectContent>
					</Select>
					<p className="text-xs text-muted-foreground">A prioridade é usada em caso de conflito entre programações.</p>
				</div>
			</div>

			<SheetFooter>
				<SheetClose asChild>
					<Button variant="ghost">Cancelar</Button>
				</SheetClose>
				<Button onClick={onSubmit}>Criar Programação</Button>
			</SheetFooter>
		</SheetContent>
	);
};
