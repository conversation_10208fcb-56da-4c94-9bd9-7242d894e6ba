import { format, isSameDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { MOCK_SCHEDULES } from './mock-data';
import { Schedule } from './types';

export const getDurationText = (minutes: number) => {
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
};

export const getTimeString = (date: Date) => {
    return format(date, 'HH:mm', { locale: ptBR });
};

export const getScheduleByDate = (date: Date, schedules: Schedule[]) => {
    return schedules.filter(
        (schedule) =>
            isSameDay(schedule.startTime, date) ||
            schedule.recurrence === 'daily' ||
            (schedule.recurrence === 'weekly' && schedule.startTime.getDay() === date.getDay()) ||
            (schedule.recurrence === 'monthly' && schedule.startTime.getDate() === date.getDate()),
    );
};

export const getRecurrenceText = (recurrence: string) => {
    switch (recurrence) {
        case 'daily':
            return 'Diariamente';
        case 'weekly':
            return 'Semanalmente';
        case 'monthly':
            return 'Mensalmente';
        default:
            return 'Sem recorrência';
    }
};

export const MOCK_DEVICES = [
    { id: 'd1', name: 'TV Recepção', type: 'TV', status: true },
    { id: 'd2', name: 'Totem Hall Principal', type: 'Totem', status: true },
    { id: 'd3', name: 'Painel Digital Sala de Espera', type: 'Painel', status: false },
    { id: 'd4', name: 'TV Sala de Reuniões', type: 'TV', status: true },
];

export const MOCK_PRESENTATIONS = [
    { id: 'p1', title: 'Bem-vindo à Empresa', thumbnail: '/assets/images/apresentation.png', duration: 60, resolution: '1920x1080' },
    { id: 'p2', title: 'Produtos em Destaque', thumbnail: '/assets/images/frame-tv.png', duration: 120, resolution: '1920x1080' },
    { id: 'p3', title: 'Orientações de Segurança', thumbnail: '/assets/images/apresentation.png', duration: 45, resolution: '1280x720' },
    { id: 'p4', title: 'Conheça Nossa História', thumbnail: '/assets/images/frame-tv.png', duration: 180, resolution: '3840x2160' },
];

export { MOCK_SCHEDULES };
