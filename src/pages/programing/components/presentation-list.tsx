import { Button } from '@/components/shadcnui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/shadcnui/card';
import { LucideClock, Presentation } from 'lucide-react';
import { MOCK_PRESENTATIONS, getDurationText } from './utils';

interface PresentationListProps {
	onSelectPresentation: (presentationId: string) => void;
}

export const PresentationList = ({ onSelectPresentation }: PresentationListProps) => {
	return (
		<Card className="h-full w-full bg-muted">
			<CardHeader className="pb-3">
				<CardTitle className="flex items-center">
					<Presentation className="mr-2" size={16} />
					Apresentações
				</CardTitle>
				<CardDescription>Apresentações disponíveis para programação</CardDescription>
			</CardHeader>
			<CardContent className="bg-black/15 p-2">
				<div className="space-y-2">
					{MOCK_PRESENTATIONS.map((presentation) => (
						<div
							key={presentation.id}
							className="group flex items-center gap-3 rounded-lg border bg-black/20 p-3 transition-colors hover:bg-accent/50"
						>
							<div className="h-12 w-16 overflow-hidden rounded-md bg-muted">
								<img src={presentation.thumbnail} alt={presentation.title} className="h-full w-full object-cover" />
							</div>
							<div className="flex-1">
								<h3 className="font-medium">{presentation.title}</h3>
								<div className="flex gap-3 text-xs text-muted-foreground">
									<span>{getDurationText(presentation.duration)}</span>
									<span>{presentation.resolution}</span>
								</div>
							</div>
							<Button
								variant="ghost"
								size="sm"
								className="h-8 w-8 opacity-0 group-hover:opacity-100"
								onClick={() => onSelectPresentation(presentation.id)}
							>
								<LucideClock className="h-4 w-4" />
							</Button>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
};
