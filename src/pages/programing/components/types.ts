export interface Device {
	id: string;
	name: string;
	type: string;
	status: boolean;
}

export interface Presentation {
	id: string;
	title: string;
	thumbnail: string;
	duration: number;
	resolution: string;
}

export interface Schedule {
	id: string;
	deviceId: string;
	deviceName: string;
	presentationId: string;
	presentationTitle: string;
	startTime: Date;
	endTime: Date;
	recurrence: 'none' | 'daily' | 'weekly' | 'monthly';
	priority: number;
}
