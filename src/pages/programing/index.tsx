import { Button, buttonVariants } from '@/components/shadcnui/button';
import { Calendar } from '@/components/shadcnui/calendar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/shadcnui/card';
import { Sheet, SheetTrigger } from '@/components/shadcnui/sheet';
import { Tabs, TabsList, TabsTrigger } from '@/components/shadcnui/tabs';
import { addDays, format, getWeek, startOfToday, startOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CalendarClock, CalendarIcon, Clock, LucideClock, LucidePlus } from 'lucide-react';
import { useState } from 'react';

import { cn } from '@/shared/lib/utils';
import { DevicesTable } from './components/devices-table';
import { PresentationList } from './components/presentation-list';
import { ScheduleForm } from './components/schedule-form';
import { ScheduleItem } from './components/schedule-item';
import { TimelineView } from './components/timeline-view';
import { Schedule } from './components/types';
import { MOCK_DEVICES, MOCK_PRESENTATIONS, MOCK_SCHEDULES, getScheduleByDate } from './components/utils';
import { WeeklyView } from './components/weekly-view';

const ProgrammingPage = () => {
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(startOfToday());
	const [selectedDevice, setSelectedDevice] = useState<string | undefined>();
	const [selectedView, setSelectedView] = useState<string>('day');
	const [schedules, setSchedules] = useState<Schedule[]>(MOCK_SCHEDULES);
	const [isCreating, setIsCreating] = useState<boolean>(false);
	const [newSchedule, setNewSchedule] = useState<Partial<Schedule>>({
		priority: 1,
		recurrence: 'none',
		startTime: new Date(),
		endTime: addDays(new Date(), 1),
	});

	const handleDateSelect = (date: Date | undefined) => {
		setSelectedDate(date);
	};

	const handleMultipleDatesSelect = (dates: Date[] | undefined) => {
		if (dates && dates.length > 0) {
			setSelectedDate(dates[0]);
		}
	};

	const handleDeviceSelect = (deviceId: string | undefined) => {
		setSelectedDevice(deviceId);
	};

	const handleAddSchedule = () => {
		if (!newSchedule.deviceId || !newSchedule.presentationId || !newSchedule.startTime || !newSchedule.endTime) {
			return;
		}

		const deviceInfo = MOCK_DEVICES.find((d) => d.id === newSchedule.deviceId);
		const presentationInfo = MOCK_PRESENTATIONS.find((p) => p.id === newSchedule.presentationId);

		const scheduleToAdd: Schedule = {
			id: `s${Date.now()}`,
			deviceId: newSchedule.deviceId,
			deviceName: deviceInfo?.name ?? 'Dispositivo',
			presentationId: newSchedule.presentationId,
			presentationTitle: presentationInfo?.title ?? 'Apresentação',
			startTime: newSchedule.startTime,
			endTime: newSchedule.endTime,
			recurrence: newSchedule.recurrence ?? 'none',
			priority: newSchedule.priority ?? 1,
		};

		setSchedules((prev) => [...prev, scheduleToAdd]);
		setIsCreating(false);
		setNewSchedule({
			priority: 1,
			recurrence: 'none',
			startTime: new Date(),
			endTime: addDays(new Date(), 1),
		});
	};

	const handleDeleteSchedule = (id: string) => {
		setSchedules((prev) => prev.filter((schedule) => schedule.id !== id));
	};

	const filteredSchedules = selectedDate ? getScheduleByDate(selectedDate, schedules).filter((s) => !selectedDevice || s.deviceId === selectedDevice) : [];

	const getViewTitle = () => {
		if (!selectedDate) return 'Programação';

		switch (selectedView) {
			case 'week': {
				const weekNumber = getWeek(selectedDate, { locale: ptBR });
				const weekStart = format(startOfWeek(selectedDate, { locale: ptBR }), "d 'de' MMMM", { locale: ptBR });
				const weekEnd = format(addDays(startOfWeek(selectedDate, { locale: ptBR }), 6), "d 'de' MMMM", { locale: ptBR });
				return `Programação da Semana ${weekNumber} (${weekStart} - ${weekEnd})`;
			}
			case 'timeline':
				return `Timeline de ${format(selectedDate, "d 'de' MMMM 'de' yyyy", { locale: ptBR })}`;
			default:
				return `Programação de ${format(selectedDate, "d 'de' MMMM 'de' yyyy", { locale: ptBR })}`;
		}
	};

	return (
		<div className="mx-auto py-8">
			<div className="group mb-6 flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
				<h1 className="flex items-center gap-2 text-2xl font-semibold">
					Programação
					<div className="relative flex items-center">
						<LucideClock className="absolute scale-0 transition-all duration-300 group-hover:scale-100" size={22} />
						<Clock className="absolute scale-0 transition-all duration-300 group-hover:scale-100" size={22} />
					</div>
				</h1>
				<Sheet open={isCreating} onOpenChange={setIsCreating}>
					<SheetTrigger asChild>
						<Button className="gap-2">
							<LucidePlus size={16} />
							<span>Nova Programação</span>
						</Button>
					</SheetTrigger>
					<ScheduleForm newSchedule={newSchedule} onScheduleChange={setNewSchedule} onSubmit={handleAddSchedule} />
				</Sheet>
			</div>
			<div className="grid gap-3 overflow-hidden md:grid-cols-[1fr_350px]">
				<div className="">
					<Tabs defaultValue="day" value={selectedView} onValueChange={setSelectedView} className="mb-6">
						<TabsList>
							<TabsTrigger value="week">Visualização geral</TabsTrigger>
							<TabsTrigger value="day">Visualização Diária</TabsTrigger>
							<TabsTrigger value="timeline">Timeline</TabsTrigger>
						</TabsList>
					</Tabs>
					<div className="flex flex-col gap-2">
						<Card className="w-full bg-muted">
							<CardHeader className="pb-2">
								<CardTitle className="flex items-center">
									<CalendarClock className="mr-2" size={16} />
									<h1 className="text-gray-100">{getViewTitle()}</h1>
								</CardTitle>
								<CardDescription className="">
									{selectedDevice
										? `Mostrando programação para ${MOCK_DEVICES.find((d) => d.id === selectedDevice)?.name}`
										: 'Mostrando programação para todos os dispositivos'}
								</CardDescription>
							</CardHeader>
							<CardContent className="w-full overflow-x-auto bg-black/15 py-4">
								{selectedView === 'week' ? (
									<div className="min-w-[600px]">
										<WeeklyView schedules={filteredSchedules} selectedDate={selectedDate} />
									</div>
								) : selectedView === 'day' ? (
									<div className="space-y-2">
										{filteredSchedules.length > 0 ? (
											filteredSchedules.map((schedule) => (
												<ScheduleItem key={schedule.id} schedule={schedule} onDelete={handleDeleteSchedule} />
											))
										) : (
											<div className="flex h-48 flex-col items-center justify-center rounded-md border border-dashed">
												<p className="text-muted-foreground">Nenhuma programação para a data selecionada</p>
												<Button variant="link" onClick={() => setIsCreating(true)}>
													Criar nova programação
												</Button>
											</div>
										)}
									</div>
								) : (
									<div className="min-w-[600px]">
										<TimelineView filteredSchedules={filteredSchedules} />
									</div>
								)}
							</CardContent>
						</Card>

						<DevicesTable
							selectedDevice={selectedDevice}
							onDeviceSelect={handleDeviceSelect}
							schedules={schedules}
							onCreateSchedule={(deviceId) => {
								setNewSchedule((prev) => ({ ...prev, deviceId }));
								setIsCreating(true);
							}}
						/>
					</div>
				</div>
				<div className="flex flex-col gap-2">
					<Card className="h-full w-full bg-muted">
						<CardHeader className="pb-2">
							<CardTitle className="flex">
								<CalendarIcon className="mr-2" size={16} />
								Calendário
							</CardTitle>
							<CardDescription>Selecione uma data para ver as programações</CardDescription>
						</CardHeader>
						<CardContent className="w-full bg-black/15 p-1 py-2">
							{selectedView === 'week' ? (
								<Calendar
									mode="multiple"
									selected={Array.from({ length: 7 }).map((_, i) =>
										addDays(startOfWeek(selectedDate || new Date(), { locale: ptBR }), i),
									)}
									onSelect={handleMultipleDatesSelect}
									className="h-full w-full overflow-hidden rounded-[15px] border bg-muted"
									locale={ptBR}
									modifiers={{
										hasSchedule: schedules.map((s) => new Date(s.startTime)),
									}}
									classNames={{
										months: 'flex w-full h-full flex-col sm:flex-row bg-black/40 backdrop-blur-3xl space-y-4 sm:space-x-4 sm:space-y-0',
										month: 'space-y-4 w-full h-full flex flex-col',
										caption: 'flex justify-center pt-1 relative items-center',
										caption_label: 'text-sm font-medium',
										nav: 'space-x-1 flex items-center',
										nav_button: cn(
											buttonVariants({ variant: 'outline' }),
											'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100',
										),
										nav_button_previous: 'absolute left-1',
										nav_button_next: 'absolute right-1',
										table: 'w-full h-full border-collapse space-y-1',
										head_row: 'flex justify-between w-full',
										head_cell: 'text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem]',
										row: 'flex w-full justify-between mt-2',
										cell: cn(
											'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md',
										),
										day: cn(
											buttonVariants({ variant: 'ghost' }),
											'h-full w-full min-h-8 min-w-8 p-0 font-normal aria-selected:opacity-100 flex-1',
										),
										day_range_start: 'day-range-start',
										day_range_end: 'day-range-end',
										day_selected:
											'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
										day_today: 'bg-accent text-accent-foreground',
										day_outside:
											'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',
										day_disabled: 'text-muted-foreground opacity-50',
										day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',
										day_hidden: 'invisible',
									}}
									modifiersStyles={{
										hasSchedule: {
											fontWeight: 'bold',
											textDecoration: 'underline',
											color: 'var(--primary)',
										},
									}}
								/>
							) : (
								<Calendar
									mode="single"
									selected={selectedDate}
									className="h-full w-full overflow-hidden rounded-[15px] border bg-muted"
									onSelect={handleDateSelect}
									locale={ptBR}
									classNames={{
										months: 'flex w-full h-full flex-col sm:flex-row bg-black/40 backdrop-blur-3xl space-y-4 sm:space-x-4 sm:space-y-0',
										month: 'space-y-4 w-full h-full flex flex-col',
										caption: 'flex justify-center pt-1 relative items-center',
										caption_label: 'text-sm font-medium',
										nav: 'space-x-1 flex items-center',
										nav_button: cn(
											buttonVariants({ variant: 'outline' }),
											'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100',
										),
										nav_button_previous: 'absolute left-1',
										nav_button_next: 'absolute right-1',
										table: 'w-full h-full border-collapse space-y-1',
										head_row: 'flex justify-between w-full',
										head_cell: 'text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem]',
										row: 'flex w-full justify-between mt-2',
										cell: cn(
											'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md',
										),
										day: cn(
											buttonVariants({ variant: 'ghost' }),
											'h-full w-full min-h-8 min-w-8 p-0 font-normal aria-selected:opacity-100 flex-1',
										),
										day_range_start: 'day-range-start',
										day_range_end: 'day-range-end',
										day_selected:
											'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
										day_today: 'bg-accent text-accent-foreground',
										day_outside:
											'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',
										day_disabled: 'text-muted-foreground opacity-50',
										day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',
										day_hidden: 'invisible',
									}}
									modifiersStyles={{
										hasSchedule: {
											fontWeight: 'bold',
											textDecoration: 'underline',
											color: 'var(--primary)',
										},
									}}
								/>
							)}
						</CardContent>
					</Card>
					<PresentationList
						onSelectPresentation={(presentationId) => {
							setNewSchedule((prev) => ({ ...prev, presentationId }));
							setIsCreating(true);
						}}
					/>
				</div>
			</div>
		</div>
	);
};

export default ProgrammingPage;
