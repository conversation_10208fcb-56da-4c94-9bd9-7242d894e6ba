import { mapElementsToItems } from '@/pages/edit-apresentation/lib/presentation';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { useQuery } from '@tanstack/react-query';
import { useAtom } from 'jotai';
import { useEffect } from 'react';
import { findDeviceRequest } from '../services/requests/find-device';

export const useFindDeviceWithPresentation = (token: string) => {
	const [items, setItems] = useAtom(itemsAtom);

	const { data, isLoading } = useQuery({
		queryKey: ['find-device', token],
		queryFn: () => findDeviceRequest(token),
		enabled: !!token,
		retry: false,
	});

	useEffect(() => {
		if (isLoading || !data) return;
		if (!data.success || !data.data?.programings[0].presentation.elements) return;
		const organizeItems = mapElementsToItems(data.data.programings[0].presentation.elements);
		setItems(organizeItems);
	}, [data, isLoading, setItems]);

	return {
		isLoading,
		success: data?.success ?? false,
		status: data?.status ?? 0,
		data: data,
		items,
	};
};
