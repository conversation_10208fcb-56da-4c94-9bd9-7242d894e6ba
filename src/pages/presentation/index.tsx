import { HTMLOverlay } from '@/components/presentation-elements/html-overlay/html-overlay';
import { useParams } from 'react-router-dom';
import { useFindDeviceWithPresentation } from './hook/find-device.hook';

const PresentationPage = () => {
	const { token } = useParams();
	const { data, isLoading, success, status } = useFindDeviceWithPresentation(token ?? '');

	if (isLoading) {
		return <LoadingMessage />;
	}

	if (!success) {
		return <ErrorMessage status={status} />;
	}

	return (
		<HTMLOverlay
			scale={1}
			containerSize={{
				width: data?.success ? data.data.width : 0,
				height: data?.success ? data.data.height : 0,
			}}
		/>
	);
};

const LoadingMessage = () => (
	<MessageContainer>
		<h1 className="text-2xl font-bold">Aguarde...</h1>
		<p className="mt-4 text-gray-600">Estamos buscando o dispositivo...</p>
	</MessageContainer>
);

const ErrorMessage = ({ status }: { status: number }) => (
	<MessageContainer>
		<h1 className="text-2xl font-bold">Erro</h1>
		<p className="mt-4 text-gray-600">Não foi possível encontrar o dispositivo.</p>
		<p className="mt-2 text-gray-500">Status: {status}</p>
	</MessageContainer>
);

const MessageContainer = ({ children }: { children: React.ReactNode }) => (
	<div className="flex h-screen w-full items-center justify-center">
		<div className="text-center">{children}</div>
	</div>
);

export default PresentationPage;
