import { IPresentationInfo } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';

export interface GetPresentationDto {
	id: string;
	createdAt: string;
	updatedAt: string;
	name: string;
	status: boolean;
	type: string;
	width: number;
	height: number;
	resolution: string;
	brand: string;
	model: string;
	token: string;
	token_generated_at: string;
	location: {
		x: number;
		y: number;
	};
	token_expires_at: string;
	programings: IPrograming[];
}

export interface IPrograming {
	id: string;
	createdAt: string;
	id_device: string;
	id_presentation: string;
	updatedAt: string;
	presentation: IPresentationInfo;
}
