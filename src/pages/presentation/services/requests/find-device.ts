import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { GetPresentationDto } from '../../dtos/get-presentation.dto';
import { PRESENTATION_ENDPOINTS } from '../endpoints';

export const findDeviceRequest = async (token: string): Promise<ApiResponse<GetPresentationDto>> => {
	try {
		const { data, status } = await api.get<GetPresentationDto>(PRESENTATION_ENDPOINTS.FIND_ONE(token));
		return { success: true, data, status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
