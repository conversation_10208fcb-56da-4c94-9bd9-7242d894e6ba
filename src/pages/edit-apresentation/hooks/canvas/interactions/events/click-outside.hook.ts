import { selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';
import { useCallback, useEffect } from 'react';

interface UseClickOutsideProps {
	propertiesPanelRef: React.RefObject<HTMLDivElement>;
	containerRef: React.RefObject<HTMLDivElement>;
}

export function useClickOutside({ propertiesPanelRef, containerRef }: UseClickOutsideProps) {
	const [, setSelectedIds] = useAtom(selectedItemsIdsAtom);

	const handleClickOutside = useCallback(
		(event: MouseEvent) => {
			const target = event.target as HTMLElement;
			const isInsidePropertiesPanel = propertiesPanelRef.current?.contains(target);
			const isInsideCanvas = containerRef.current?.contains(target);
			const isInsideSelect = Boolean(target.closest('[role="option"], [role="listbox"], .select-content'));
			const isInsideLayerItem = Boolean(target.closest('.elements-layer-item, [data-selection-enabled]'));
			const isInsideNavAction = Boolean(target.closest('[data-nav-action="delete"], [data-nav-action="reset"]'));

			if (!isInsideCanvas && !isInsidePropertiesPanel && !isInsideSelect && !isInsideLayerItem && !isInsideNavAction) {
				setSelectedIds([]);
			}
		},
		[propertiesPanelRef, setSelectedIds, containerRef],
	);

	useEffect(() => {
		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [handleClickOutside]);

	return { containerRef };
}
