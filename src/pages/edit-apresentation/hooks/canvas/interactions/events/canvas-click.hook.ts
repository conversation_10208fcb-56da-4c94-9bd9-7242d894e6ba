import { IItem, IItemShapeType, ITEM_STATUS } from '@/pages/edit-apresentation/types/item.type';
import { IElementToAdd } from '@/shared/states/items/next-items.state';

import Konva from 'konva';
import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';

const DEFAULT_ITEM_WIDTH = 100;
const DEFAULT_ITEM_HEIGHT = 100;
const clamp = (value: number, min: number, max: number) => Math.min(Math.max(value, min), max);

interface UseCanvasClickProps {
	nextElement: { type: IItemShapeType; contentDefault?: object } | null;
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	items: IItem[];
	setItems: (items: IItem[]) => void;
	setNextElement: React.Dispatch<React.SetStateAction<IElementToAdd | null>>;
	setSelectedsIds: (ids: string[]) => void;
	padding: number;
	editableAreaWidth: number;
	editableAreaHeight: number;
}

const isClickInsideEditableArea = (
	pointerPos: { x: number; y: number },
	padding: number,
	editableAreaWidth: number,
	editableAreaHeight: number,
	scale: number,
): boolean => {
	const adjustedX = pointerPos.x - padding;
	const adjustedY = pointerPos.y - padding;
	const scaledWidth = editableAreaWidth * scale;
	const scaledHeight = editableAreaHeight * scale;
	return adjustedX >= 0 && adjustedX <= scaledWidth && adjustedY >= 0 && adjustedY <= scaledHeight;
};

export function useCanvasClick(props: UseCanvasClickProps) {
	const { nextElement, scale, canvasWidth, canvasHeight, items, setItems, setNextElement, setSelectedsIds, padding, editableAreaWidth, editableAreaHeight } =
		props;

	const handleCanvasClick = useCallback(
		(e: Konva.KonvaEventObject<MouseEvent>) => {
			const stage = e.target.getStage();
			if (!stage) return;
			const pointerPos = stage.getPointerPosition();
			if (!pointerPos) return;

			if (nextElement) {
				const realX = pointerPos.x / scale;
				const realY = pointerPos.y / scale;
				const centeredX = realX - DEFAULT_ITEM_WIDTH / 2;
				const centeredY = realY - DEFAULT_ITEM_HEIGHT / 2;
				const clampedX = clamp(centeredX, 0, canvasWidth - DEFAULT_ITEM_WIDTH);
				const clampedY = clamp(centeredY, 0, canvasHeight - DEFAULT_ITEM_HEIGHT);
				const newItem = {
					tempId: uuidv4(),
					name: `item ${items.length + 1}`,
					status: ITEM_STATUS.NEW,
					type: nextElement.type,
					position: { x: clampedX, y: clampedY },
					size: { width: DEFAULT_ITEM_WIDTH, height: DEFAULT_ITEM_HEIGHT },
					layer: items.length + 1,
					content: nextElement.contentDefault,
				};
				setItems([...items, newItem]);
				setSelectedsIds([newItem.tempId]);
				setNextElement(null);
			} else if (e.target === stage) {
				const isInsideEditableArea = isClickInsideEditableArea(pointerPos, padding, editableAreaWidth, editableAreaHeight, scale);
				if (!isInsideEditableArea) {
					setSelectedsIds([]);
				} else {
					setSelectedsIds([]);
				}
			}
		},
		[nextElement, scale, canvasWidth, canvasHeight, items, setItems, setNextElement, setSelectedsIds, padding, editableAreaWidth, editableAreaHeight],
	);

	return { handleCanvasClick };
}
