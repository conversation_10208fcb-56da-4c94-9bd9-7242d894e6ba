import { itemsAtom } from '@/shared/states/items/object-item.state';
import { useAtomValue } from 'jotai';
import { useCallback, useEffect, useState } from 'react';

export type ShapeInfo = {
	name: string;
	width: number;
	height: number;
	tempId: string;
};

export const useShapeHover = () => {
	const items = useAtomValue(itemsAtom);
	const [hoveredShape, setHoveredShape] = useState<{
		item: ShapeInfo | null;
		x: number;
		y: number;
	}>({ item: null, x: 0, y: 0 });

	const handleShapeMouseEnter = (item: ShapeInfo, position: { x: number; y: number }) => setHoveredShape({ item, x: position.x + 10, y: position.y + 10 });
	const handleShapeMouseLeave = () => setHoveredShape({ item: null, x: 0, y: 0 });
	const clearHoveredShape = useCallback(() => setHoveredShape({ item: null, x: 0, y: 0 }), []);

	useEffect(() => {
		if (hoveredShape.item) {
			const stillExists = items.some((item) => item.tempId === hoveredShape.item?.tempId);
			if (!stillExists) {
				clearHoveredShape();
			}
		}
	}, [items, hoveredShape.item, clearHoveredShape]);

	return {
		hoveredShape,
		handleShapeMouseEnter,
		handleShapeMouseLeave,
		clearHoveredShape,
	};
};
