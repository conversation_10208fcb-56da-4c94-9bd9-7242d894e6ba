import Konva from 'konva';

import { Dispatch, SetStateAction, useCallback } from 'react';

interface UseShapeSelectionProps {
	setSelectedsIds: Dispatch<SetStateAction<string[]>>;
}

export function useShapeSelection({ setSelectedsIds }: UseShapeSelectionProps) {
	const handleSelectShape = useCallback(
		({ idElement, event }: { idElement: string; event: Konva.KonvaEventObject<MouseEvent> }) => {
			if (event.evt.ctrlKey || event.evt.shiftKey || event.evt.metaKey) {
				setSelectedsIds((prevSelecteds: string[]) =>
					prevSelecteds.includes(idElement) ? prevSelecteds.filter((id) => id !== idElement) : [...prevSelecteds, idElement],
				);
			} else {
				setSelectedsIds([idElement]);
			}
		},
		[setSelectedsIds],
	);

	return { handleSelectShape };
}
