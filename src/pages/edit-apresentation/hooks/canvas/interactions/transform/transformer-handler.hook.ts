import { IItem } from '@/pages/edit-apresentation/types/item.type';
import Konva from 'konva';
import { useCallback } from 'react';

interface UseTransformHandlerProps {
	items: IItem[];
	setItems: (items: IItem[]) => void;
	selectedsIds: string[];
	scale: number;
	transformerRef: React.MutableRefObject<Konva.Transformer | null>;
	clearSnapLines: () => void;
	checkSnapLines: (currentItem: IItem) => void;
	editableAreaWidth: number;
	editableAreaHeight: number;
}

export function useTransformHandler({
	items,
	setItems,
	selectedsIds,
	scale,
	transformerRef,
	clearSnapLines,
	checkSnapLines,
	editableAreaWidth,
	editableAreaHeight,
}: UseTransformHandlerProps) {
	const markItemsAsDragging = useCallback(() => {
		if (selectedsIds.length === 0) return;

		setItems(items.map((item) => (selectedsIds.includes(item.tempId) ? { ...item, isDragging: true } : item)));
	}, [items, selectedsIds, setItems]);

	const updateTransformedItem = useCallback(
		(item: IItem, node: Konva.Node): IItem => {
			const newX = node.x() / scale;
			const newY = node.y() / scale;
			const newWidth = (node.width() * node.scaleX()) / scale;
			const newHeight = (node.height() * node.scaleY()) / scale;

			node.scaleX(1);
			node.scaleY(1);

			const realNewWidth = Math.max(Math.round(newWidth), 1);
			const realNewHeight = Math.max(Math.round(newHeight), 1);
			const newPosition = {
				x: Math.max(0, Math.round(newX)),
				y: Math.max(0, Math.round(newY)),
			};

			const maxWidth = editableAreaWidth + 1;
			const maxHeight = editableAreaHeight + 1;
			const adjustedWidth = Math.min(realNewWidth, maxWidth);
			const adjustedHeight = Math.min(realNewHeight, maxHeight);
			return {
				...item,
				isDragging: false,
				position: newPosition,
				size: {
					width: adjustedWidth,
					height: adjustedHeight,
				},
			};
		},
		[scale, editableAreaWidth, editableAreaHeight],
	);

	const handleTransformStart = useCallback(() => {
		markItemsAsDragging();
	}, [markItemsAsDragging]);

	const handleTransform = useCallback(() => {
		if (!transformerRef.current || selectedsIds.length === 0) return;

		const transformer = transformerRef.current;
		const nodes = transformer.getNodes();

		if (selectedsIds.length === 1) {
			const selectedId = selectedsIds[0];
			const item = items.find((i) => i.tempId === selectedId);

			if (item) {
				const node = nodes[0];

				const newX = node.x() / scale;
				const newY = node.y() / scale;
				const newWidth = (node.width() * node.scaleX()) / scale;
				const newHeight = (node.height() * node.scaleY()) / scale;

				const realNewWidth = Math.max(Math.round(newWidth), 1);
				const realNewHeight = Math.max(Math.round(newHeight), 1);

				const newPosition = {
					x: Math.max(0, Math.round(newX)),
					y: Math.max(0, Math.round(newY)),
				};

				const maxWidth = editableAreaWidth + 1;
				const maxHeight = editableAreaHeight + 1;
				const adjustedWidth = Math.min(realNewWidth, maxWidth);
				const adjustedHeight = Math.min(realNewHeight, maxHeight);

				const tempItem: IItem = {
					...item,
					position: newPosition,
					size: {
						width: adjustedWidth,
						height: adjustedHeight,
					},
				};

				checkSnapLines(tempItem);
			}
		}
	}, [transformerRef, selectedsIds, items, scale, checkSnapLines, editableAreaWidth, editableAreaHeight]);

	const handleTransformEnd = useCallback(() => {
		if (!transformerRef.current || selectedsIds.length === 0) return;

		const transformer = transformerRef.current;
		const nodes = transformer.getNodes();

		setItems(
			items.map((item) => {
				if (!selectedsIds.includes(item.tempId)) return item;
				const node = nodes.find((n) => n.id() === item.tempId);
				if (!node) return { ...item, isDragging: false };
				return updateTransformedItem(item, node);
			}),
		);

		clearSnapLines();
	}, [items, transformerRef, selectedsIds, setItems, updateTransformedItem, clearSnapLines]);

	return { handleTransformStart, handleTransformEnd, handleTransform };
}
