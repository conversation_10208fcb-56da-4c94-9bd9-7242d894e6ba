interface ElementDimensions {
	x: number;
	y: number;
	width: number;
	height: number;
}

interface CanvasDimensions {
	width: number;
	height: number;
}

interface AdjustElementDimensionsParams extends ElementDimensions {
	canvas: CanvasDimensions;
	snapTolerance?: number;
}

const DEFAULT_POSITION = 0;
const DEFAULT_SNAP_TOLERANCE = 0;

export const useElementDimensions = () => {
	const adjustElementDimensions = ({
		x,
		y,
		width,
		height,
		canvas,
		snapTolerance = DEFAULT_SNAP_TOLERANCE,
	}: AdjustElementDimensionsParams): ElementDimensions => {
		let dims: ElementDimensions = { x, y, width, height };
		dims = adjustLeftBoundary(dims);
		dims = adjustTopBoundary(dims);
		dims = adjustRightBoundary(dims, canvas.width);
		dims = adjustBottomBoundary(dims, canvas.height);
		dims = applyRightSnap(dims, canvas.width, snapTolerance);
		dims = applyBottomSnap(dims, canvas.height, snapTolerance);

		return dims;
	};

	const adjustLeftBoundary = (dims: ElementDimensions): ElementDimensions =>
		dims.x < DEFAULT_POSITION ? { ...dims, x: DEFAULT_POSITION, width: dims.width + dims.x } : dims;

	const adjustTopBoundary = (dims: ElementDimensions): ElementDimensions =>
		dims.y < DEFAULT_POSITION ? { ...dims, y: DEFAULT_POSITION, height: dims.height + dims.y } : dims;

	const adjustRightBoundary = (dims: ElementDimensions, canvasWidth: number): ElementDimensions =>
		dims.x + dims.width > canvasWidth ? { ...dims, width: canvasWidth - dims.x } : dims;

	const adjustBottomBoundary = (dims: ElementDimensions, canvasHeight: number): ElementDimensions =>
		dims.y + dims.height > canvasHeight ? { ...dims, height: canvasHeight - dims.y } : dims;

	const applyRightSnap = (dims: ElementDimensions, canvasWidth: number, tolerance: number): ElementDimensions => {
		const distanceToRightEdge = canvasWidth - (dims.x + dims.width);
		return distanceToRightEdge <= tolerance ? { ...dims, width: canvasWidth - dims.x } : dims;
	};

	const applyBottomSnap = (dims: ElementDimensions, canvasHeight: number, tolerance: number): ElementDimensions => {
		const distanceToBottomEdge = canvasHeight - (dims.y + dims.height);
		return distanceToBottomEdge <= tolerance ? { ...dims, height: canvasHeight - dims.y } : dims;
	};

	return {
		adjustElementDimensions,
	};
};
