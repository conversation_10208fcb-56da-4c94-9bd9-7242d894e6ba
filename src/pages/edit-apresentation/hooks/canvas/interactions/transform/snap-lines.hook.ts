import { snapLinesAtom } from '@/pages/edit-apresentation/states/canvas/snap-lines.state';
import { IItem } from '@/pages/edit-apresentation/types/item.type';
import { useAtom } from 'jotai';

const SNAP_TOLERANCE = 3;

const computeCandidate = (reference: number, candidates: number[], tolerance: number): number | null => {
	const validCandidates = candidates.filter((candidate) => Math.abs(reference - candidate) < tolerance);
	if (validCandidates.length === 0) return null;
	return validCandidates.reduce((prev, curr) => (Math.abs(reference - curr) < Math.abs(reference - prev) ? curr : prev));
};

export const useSnapLines = (items: IItem[], scale: number, canvasWidth: number, canvasHeight: number) => {
	const [snapLines, setSnapLines] = useAtom(snapLinesAtom);

	const checkSnapLines = (currentItem: IItem) => {
		const currentPoints = {
			left: currentItem.position.x,
			centerX: currentItem.position.x + currentItem.size.width / 2,
			right: currentItem.position.x + currentItem.size.width,
			top: currentItem.position.y,
			centerY: currentItem.position.y + currentItem.size.height / 2,
			bottom: currentItem.position.y + currentItem.size.height,
		};

		const canvasCandidatesX = [0, canvasWidth / 2, canvasWidth];
		const canvasCandidatesY = [0, canvasHeight / 2, canvasHeight];

		const itemsCandidatesX = items
			.filter((item) => item.tempId !== currentItem.tempId)
			.flatMap((item) => [item.position.x, item.position.x + item.size.width / 2, item.position.x + item.size.width]);

		const itemsCandidatesY = items
			.filter((item) => item.tempId !== currentItem.tempId)
			.flatMap((item) => [item.position.y, item.position.y + item.size.height / 2, item.position.y + item.size.height]);

		const candidateXPool = [...itemsCandidatesX, ...canvasCandidatesX];
		const candidateYPool = [...itemsCandidatesY, ...canvasCandidatesY];

		const candidateLeft = computeCandidate(currentPoints.left, candidateXPool, SNAP_TOLERANCE);
		const candidateCenter = computeCandidate(currentPoints.centerX, candidateXPool, SNAP_TOLERANCE);
		const candidateRight = computeCandidate(currentPoints.right, candidateXPool, SNAP_TOLERANCE);

		const candidateTop = computeCandidate(currentPoints.top, candidateYPool, SNAP_TOLERANCE);
		const candidateMiddle = computeCandidate(currentPoints.centerY, candidateYPool, SNAP_TOLERANCE);
		const candidateBottom = computeCandidate(currentPoints.bottom, candidateYPool, SNAP_TOLERANCE);

		const verticalLines: number[] = [];
		if (candidateLeft !== null) verticalLines.push(candidateLeft * scale);
		if (candidateCenter !== null) verticalLines.push(candidateCenter * scale);
		if (candidateRight !== null) verticalLines.push(candidateRight * scale);

		const horizontalLines: number[] = [];
		if (candidateTop !== null) horizontalLines.push(candidateTop * scale);
		if (candidateMiddle !== null) horizontalLines.push(candidateMiddle * scale);
		if (candidateBottom !== null) horizontalLines.push(candidateBottom * scale);

		setSnapLines({ vertical: verticalLines, horizontal: horizontalLines });
	};

	const clearSnapLines = () => setSnapLines({ vertical: [], horizontal: [] });

	return { checkSnapLines, clearSnapLines, snapLines };
};
