import { undoAtom } from '@/pages/edit-apresentation/states/history/history.state';
import { useSetAtom } from 'jotai';

import { useKeyboardShortcuts } from '../../keyboards/keyboard-shotcuts.hook';

import { useCanvasAlignment } from './canvas-alignment.hook';
import { useCanvasClipboard } from './canvas-clipboard.hook';
import { useCanvasLayers } from './canvas-layers.hook';
import { useCanvasMovement } from './canvas-movement.hook';
import { useCanvasSelection } from './canvas-selection.hook';

export const useCanvasKeyboard = () => {
	const undo = useSetAtom(undoAtom);
	const { handleCopy, handlePaste, handleDelete } = useCanvasClipboard();
	const { handleMoveUp, handleMoveDown, handleMoveLeft, handleMoveRight, handleMoveUpFast, handleMoveDownFast, handleMoveLeftFast, handleMoveRightFast } =
		useCanvasMovement();
	const {
		handleAlignLeft,
		handleAlignCenter,
		handleAlignRight,
		handleAlignTop,
		handleAlignMiddle,
		handleAlignBottom,
		handleDistributeHorizontal,
		handleDistributeVertical,
	} = useCanvasAlignment();
	const { handleSelectAll, handleDeselectAll, handleSelectNext, handleSelectPrevious } = useCanvasSelection();
	const { handleBringToFront, handleSendToBack, handleBringForward, handleSendBackward, handleDuplicate } = useCanvasLayers();

	useKeyboardShortcuts({
		handlers: {
			copy: handleCopy,
			paste: handlePaste,
			delete: handleDelete,
			undo: undo,
			selectAll: handleSelectAll,
			escape: handleDeselectAll,
			moveUp: handleMoveUp,
			moveDown: handleMoveDown,
			moveLeft: handleMoveLeft,
			moveRight: handleMoveRight,
			moveUpFast: handleMoveUpFast,
			moveDownFast: handleMoveDownFast,
			moveLeftFast: handleMoveLeftFast,
			moveRightFast: handleMoveRightFast,
			alignLeft: handleAlignLeft,
			alignCenter: handleAlignCenter,
			alignRight: handleAlignRight,
			alignTop: handleAlignTop,
			alignMiddle: handleAlignMiddle,
			alignBottom: handleAlignBottom,
			distributeHorizontal: handleDistributeHorizontal,
			distributeVertical: handleDistributeVertical,
			selectNext: handleSelectNext,
			selectPrevious: handleSelectPrevious,
			bringToFront: handleBringToFront,
			sendToBack: handleSendToBack,
			bringForward: handleBringForward,
			sendBackward: handleSendBackward,
			duplicate: handleDuplicate,
		},
	});

	return {
		handleCopy,
		handlePaste,
		handleDelete,
		undo,
		handleMoveUp,
		handleMoveDown,
		handleMoveLeft,
		handleMoveRight,
		handleSelectAll,
		handleDeselectAll,
	};
};
