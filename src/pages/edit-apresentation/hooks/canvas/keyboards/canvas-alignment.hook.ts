import { useAtom } from 'jotai';
import { useCallback } from 'react';

import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { 
	alignElements, 
	distributeElements, 
	AlignmentType, 
	DistributionType 
} from '../../../components/properties-panel/shared/alignment/align-elements';

export const useCanvasAlignment = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedItemsIds] = useAtom(selectedItemsIdsAtom);

	const getSelectedItems = useCallback(() => {
		return items.filter((item) => selectedItemsIds.includes(item.tempId));
	}, [items, selectedItemsIds]);

	const applyAlignment = useCallback(
		(alignmentType: AlignmentType) => {
			const selectedItems = getSelectedItems();
			if (selectedItems.length < 2) return;

			const alignedItems = alignElements(alignmentType, selectedItems);
			
			setItems(
				items.map((item) => {
					const alignedItem = alignedItems.find((aligned) => aligned.tempId === item.tempId);
					return alignedItem || item;
				}),
			);
		},
		[items, setItems, getSelectedItems],
	);

	const applyDistribution = useCallback(
		(distributionType: DistributionType) => {
			const selectedItems = getSelectedItems();
			if (selectedItems.length < 3) return;

			const distributedItems = distributeElements(distributionType, selectedItems);
			
			setItems(
				items.map((item) => {
					const distributedItem = distributedItems.find((distributed) => distributed.tempId === item.tempId);
					return distributedItem || item;
				}),
			);
		},
		[items, setItems, getSelectedItems],
	);

	const handleAlignLeft = useCallback(() => {
		applyAlignment(AlignmentType.LEFT);
	}, [applyAlignment]);

	const handleAlignCenter = useCallback(() => {
		applyAlignment(AlignmentType.CENTER);
	}, [applyAlignment]);

	const handleAlignRight = useCallback(() => {
		applyAlignment(AlignmentType.RIGHT);
	}, [applyAlignment]);

	const handleAlignTop = useCallback(() => {
		applyAlignment(AlignmentType.TOP);
	}, [applyAlignment]);

	const handleAlignMiddle = useCallback(() => {
		applyAlignment(AlignmentType.MIDDLE);
	}, [applyAlignment]);

	const handleAlignBottom = useCallback(() => {
		applyAlignment(AlignmentType.BOTTOM);
	}, [applyAlignment]);

	const handleDistributeHorizontal = useCallback(() => {
		applyDistribution(DistributionType.HORIZONTAL_CENTERS);
	}, [applyDistribution]);

	const handleDistributeVertical = useCallback(() => {
		applyDistribution(DistributionType.VERTICAL_CENTERS);
	}, [applyDistribution]);

	return {
		handleAlignLeft,
		handleAlignCenter,
		handleAlignRight,
		handleAlignTop,
		handleAlignMiddle,
		handleAlignBottom,
		handleDistributeHorizontal,
		handleDistributeVertical,
	};
};
