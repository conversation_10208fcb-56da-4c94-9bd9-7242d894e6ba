import { useAtom, useAtomValue } from 'jotai';
import { useCallback } from 'react';

import { presentationInfoAtom } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useCanvasConfig } from '../config/core/canvas-config.hook';
import { useContainerWidth } from '../config/layout/container-width.hook';

const MOVEMENT_STEP = 1;
const FAST_MOVEMENT_STEP = 10;

export const useCanvasMovement = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedItemsIds] = useAtom(selectedItemsIdsAtom);
	const presentation = useAtomValue(presentationInfoAtom);
	const { width: containerWidth, height: containerHeight } = useContainerWidth();
	const { editableAreaWidth, editableAreaHeight } = useCanvasConfig(presentation, containerWidth, containerHeight);

	const moveSelectedItems = useCallback(
		(deltaX: number, deltaY: number) => {
			if (selectedItemsIds.length === 0) return;

			setItems(
				items.map((item) => {
					if (!selectedItemsIds.includes(item.tempId)) return item;

					const newX = Math.max(0, Math.min(item.position.x + deltaX, editableAreaWidth - item.size.width));
					const newY = Math.max(0, Math.min(item.position.y + deltaY, editableAreaHeight - item.size.height));

					return {
						...item,
						position: {
							x: newX,
							y: newY,
						},
					};
				}),
			);
		},
		[items, selectedItemsIds, setItems, editableAreaWidth, editableAreaHeight],
	);

	const handleMoveUp = useCallback(() => {
		moveSelectedItems(0, -MOVEMENT_STEP);
	}, [moveSelectedItems]);

	const handleMoveDown = useCallback(() => {
		moveSelectedItems(0, MOVEMENT_STEP);
	}, [moveSelectedItems]);

	const handleMoveLeft = useCallback(() => {
		moveSelectedItems(-MOVEMENT_STEP, 0);
	}, [moveSelectedItems]);

	const handleMoveRight = useCallback(() => {
		moveSelectedItems(MOVEMENT_STEP, 0);
	}, [moveSelectedItems]);

	const handleMoveUpFast = useCallback(() => {
		moveSelectedItems(0, -FAST_MOVEMENT_STEP);
	}, [moveSelectedItems]);

	const handleMoveDownFast = useCallback(() => {
		moveSelectedItems(0, FAST_MOVEMENT_STEP);
	}, [moveSelectedItems]);

	const handleMoveLeftFast = useCallback(() => {
		moveSelectedItems(-FAST_MOVEMENT_STEP, 0);
	}, [moveSelectedItems]);

	const handleMoveRightFast = useCallback(() => {
		moveSelectedItems(FAST_MOVEMENT_STEP, 0);
	}, [moveSelectedItems]);

	return {
		handleMoveUp,
		handleMoveDown,
		handleMoveLeft,
		handleMoveRight,
		handleMoveUpFast,
		handleMoveDownFast,
		handleMoveLeftFast,
		handleMoveRightFast,
	};
};
