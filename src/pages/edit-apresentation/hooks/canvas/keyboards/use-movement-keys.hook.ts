import { useEffect, useRef } from 'react';

interface MovementKeyState {
	up: boolean;
	down: boolean;
	left: boolean;
	right: boolean;
	upFast: boolean;
	downFast: boolean;
	leftFast: boolean;
	rightFast: boolean;
}

interface UseMovementKeysProps {
	onKeyRelease: () => void;
}

/**
 * Hook para gerenciar o estado das teclas de movimento pressionadas
 * Responsabilidade única: rastrear quais teclas de movimento estão ativas
 */
export const useMovementKeys = ({ onKeyRelease }: UseMovementKeysProps) => {
	const isMovementKeyPressed = useRef<MovementKeyState>({
		up: false,
		down: false,
		left: false,
		right: false,
		upFast: false,
		downFast: false,
		leftFast: false,
		rightFast: false,
	});

	// Efeito para adicionar listener para quando a tecla é liberada
	useEffect(() => {
		const handleKeyUp = (e: KeyboardEvent) => {
			const keyMap: Record<string, keyof MovementKeyState> = {
				ArrowUp: 'up',
				ArrowDown: 'down',
				ArrowLeft: 'left',
				ArrowRight: 'right',
				// Podemos adicionar mais combinações aqui, como Shift+Arrow para movimentos rápidos
			};

			if (e.key in keyMap) {
				const direction = keyMap[e.key];
				isMovementKeyPressed.current[direction] = false;

				// Verifica se ainda há alguma tecla de movimento pressionada
				const anyKeyStillPressed = Object.values(isMovementKeyPressed.current).some((val) => val);

				if (!anyKeyStillPressed) {
					onKeyRelease();
				}
			}
		};

		window.addEventListener('keyup', handleKeyUp);
		return () => {
			window.removeEventListener('keyup', handleKeyUp);
		};
	}, [onKeyRelease]);

	const setKeyPressed = (key: keyof MovementKeyState, pressed: boolean = true) => {
		isMovementKeyPressed.current[key] = pressed;
	};

	return {
		isMovementKeyPressed,
		setKeyPressed,
	};
};
