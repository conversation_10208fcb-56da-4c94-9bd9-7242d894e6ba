import { useAtom } from 'jotai';
import { useCallback } from 'react';

import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { ITEM_STATUS } from '../../../types/item.type';

export const useCanvasSelection = () => {
	const [items] = useAtom(itemsAtom);
	const [selectedItemsIds, setSelectedItemsIds] = useAtom(selectedItemsIdsAtom);

	const getVisibleItems = useCallback(() => {
		return items.filter((item) => item.status !== ITEM_STATUS.DELETED);
	}, [items]);

	const handleSelectAll = useCallback(() => {
		const visibleItems = getVisibleItems();
		const allIds = visibleItems.map((item) => item.tempId);
		setSelectedItemsIds(allIds);
	}, [getVisibleItems, setSelectedItemsIds]);

	const handleDeselectAll = useCallback(() => {
		setSelectedItemsIds([]);
	}, [setSelectedItemsIds]);

	const handleSelectNext = useCallback(() => {
		const visibleItems = getVisibleItems();
		if (visibleItems.length === 0) return;

		if (selectedItemsIds.length === 0) {
			setSelectedItemsIds([visibleItems[0].tempId]);
			return;
		}

		if (selectedItemsIds.length > 1) {
			const currentIndex = visibleItems.findIndex((item) => selectedItemsIds.includes(item.tempId));
			const nextIndex = (currentIndex + 1) % visibleItems.length;
			setSelectedItemsIds([visibleItems[nextIndex].tempId]);
			return;
		}
		const currentIndex = visibleItems.findIndex((item) => item.tempId === selectedItemsIds[0]);
		const nextIndex = (currentIndex + 1) % visibleItems.length;
		setSelectedItemsIds([visibleItems[nextIndex].tempId]);
	}, [selectedItemsIds, setSelectedItemsIds, getVisibleItems]);

	const handleSelectPrevious = useCallback(() => {
		const visibleItems = getVisibleItems();
		if (visibleItems.length === 0) return;

		if (selectedItemsIds.length === 0) {
			setSelectedItemsIds([visibleItems[visibleItems.length - 1].tempId]);
			return;
		}

		if (selectedItemsIds.length > 1) {
			const currentIndex = visibleItems.findIndex((item) => selectedItemsIds.includes(item.tempId));
			const prevIndex = currentIndex === 0 ? visibleItems.length - 1 : currentIndex - 1;
			setSelectedItemsIds([visibleItems[prevIndex].tempId]);
			return;
		}

		const currentIndex = visibleItems.findIndex((item) => item.tempId === selectedItemsIds[0]);
		const prevIndex = currentIndex === 0 ? visibleItems.length - 1 : currentIndex - 1;
		setSelectedItemsIds([visibleItems[prevIndex].tempId]);
	}, [selectedItemsIds, setSelectedItemsIds, getVisibleItems]);

	return {
		handleSelectAll,
		handleDeselectAll,
		handleSelectNext,
		handleSelectPrevious,
	};
};
