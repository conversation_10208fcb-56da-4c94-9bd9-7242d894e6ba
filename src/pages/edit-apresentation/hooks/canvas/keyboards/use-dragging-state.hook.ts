import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';
import { useCallback, useRef } from 'react';

export const useDraggingState = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedItemsIds] = useAtom(selectedItemsIdsAtom);
	const isDraggingRef = useRef(false);

	const setDraggingState = useCallback(
		(dragging: boolean) => {
			if (selectedItemsIds.length === 0) return;
			isDraggingRef.current = dragging;

			setItems(
				items.map((item) => {
					if (!selectedItemsIds.includes(item.tempId)) return item;

					return {
						...item,
						isDragging: dragging,
					};
				}),
			);
		},
		[items, selectedItemsIds, setItems],
	);

	return {
		isDraggingRef,
		setDraggingState,
	};
};
