import { ISocketConnection } from '@/pages/edit-apresentation/types/socket/socket-connection.type';
import { socketStatusAtom } from '@/shared/states/socket/socket-status.state';
import { useSetAtom } from 'jotai';
import { useEffect, useRef } from 'react';

export type SocketStatusEvent = 'connect' | 'disconnect' | 'connect_error' | 'reconnect_attempt';

export const useSocketStatus = (socketService: ISocketConnection): void => {
	const setSocketStatus = useSetAtom(socketStatusAtom);
	const isInitializedRef = useRef<boolean>(false);
	const reconnectAttemptsRef = useRef<number>(0);
	const reconnectTimerRef = useRef<number | undefined>(undefined);
	const MAX_RECONNECT_ATTEMPTS = 5;
	const RECONNECT_DELAY_MS = 2000;
	const STATUS_CHECK_INTERVAL_MS = 3000;

	useEffect(() => {
		if (isInitializedRef.current) return;
		isInitializedRef.current = true;

		const updateStatus = (): void => {
			const currentStatus = socketService.getStatus();
			setSocketStatus(currentStatus);

			if (currentStatus === 'disconnected' && reconnectAttemptsRef.current < MAX_RECONNECT_ATTEMPTS && reconnectTimerRef.current === undefined) {
				reconnectAttemptsRef.current += 1;
				console.info(`Tentativa de reconexão ${reconnectAttemptsRef.current}/${MAX_RECONNECT_ATTEMPTS}`);

				const backoffDelay = RECONNECT_DELAY_MS * Math.min(reconnectAttemptsRef.current, 5);
				reconnectTimerRef.current = window.setTimeout(() => {
					socketService.connect();
					reconnectTimerRef.current = undefined;
				}, backoffDelay);
			}
		};

		updateStatus();

		const handleConnect = (): void => {
			setSocketStatus('connected');
			reconnectAttemptsRef.current = 0;
			console.info('Socket conectado com sucesso');
			if (reconnectTimerRef.current) {
				clearTimeout(reconnectTimerRef.current);
				reconnectTimerRef.current = undefined;
			}
		};

		const handleDisconnect = (): void => {
			setSocketStatus('disconnected');
			console.warn('Socket desconectado');
			updateStatus();
		};

		const handleConnectError = (): void => {
			setSocketStatus('connecting');
			console.error('Erro ao conectar socket');
		};

		const handleReconnectAttempt = (): void => {
			setSocketStatus('reconnecting');
			console.info('Tentando reconectar socket');
		};

		socketService.on('connect', handleConnect);
		socketService.on('disconnect', handleDisconnect);
		socketService.on('connect_error', handleConnectError);
		socketService.on('reconnect_attempt', handleReconnectAttempt);

		const statusInterval = window.setInterval(updateStatus, STATUS_CHECK_INTERVAL_MS);

		return () => {
			window.clearInterval(statusInterval);

			if (reconnectTimerRef.current) {
				window.clearTimeout(reconnectTimerRef.current);
			}

			socketService.off('connect', handleConnect);
			socketService.off('disconnect', handleDisconnect);
			socketService.off('connect_error', handleConnectError);
			socketService.off('reconnect_attempt', handleReconnectAttempt);
		};
	}, [socketService, setSocketStatus]);
};
