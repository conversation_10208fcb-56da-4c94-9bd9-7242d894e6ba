import { useEffect, useRef } from 'react';

import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtomValue } from 'jotai';
import Konva from 'konva';

export function useTransformer() {
	const items = useAtomValue(itemsAtom);
	const selectdsId = useAtomValue(selectedItemsIdsAtom);
	const transformerRef = useRef<Konva.Transformer>(null);
	const stageRef = useRef<Konva.Stage>(null);

	useEffect(() => {
		const transformer = transformerRef.current;
		if (!transformer) return;
		const stage = stageRef.current;
		if (!stage) return;

		const selectedNodes = selectdsId
			.map((id) => stage.findOne((node: Konva.Node) => node.attrs.id === id))
			.filter((node): node is Konva.Node => node !== undefined);
		if (selectedNodes.length > 0) {
			transformer.nodes(selectedNodes);
			transformer.getLayer()?.batchDraw();
		} else {
			transformer.nodes([]);
		}
	}, [selectdsId, items]);

	return { transformerRef, stageRef };
}
