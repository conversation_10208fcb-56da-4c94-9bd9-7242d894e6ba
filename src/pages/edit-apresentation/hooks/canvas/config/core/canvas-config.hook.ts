import { IPresentationInfo } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import { presentationScaleAtom } from '@/pages/edit-apresentation/states/presentation/scale.state';
import { useSetAtom } from 'jotai';
import { useEffect } from 'react';

interface CanvasConfig {
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	editableAreaWidth: number;
	editableAreaHeight: number;
	padding: number;
}

export const useCanvasConfig = (presentation: IPresentationInfo | null, containerWidth: number, containerHeight: number): CanvasConfig => {
	const setPresentationScale = useSetAtom(presentationScaleAtom);
	const padding = 10;
	const baseWidth = presentation?.width ?? containerWidth;
	const baseHeight = presentation?.height ?? containerHeight;
	const availableWidth = containerWidth - padding * 2;
	const availableHeight = containerHeight - padding * 2;
	const scaleByWidth = availableWidth / baseWidth;
	const scaleByHeight = availableHeight / baseHeight;
	const scale = Math.min(scaleByWidth, scaleByHeight);
	const editableAreaWidth = baseWidth;
	const editableAreaHeight = baseHeight;
	const scaledWidth = baseWidth * scale;
	const scaledHeight = baseHeight * scale;
	const canvasWidth = scaledWidth + padding * 2;
	const canvasHeight = scaledHeight + padding * 2;

	useEffect(() => {
		setPresentationScale(scale);
	}, [scale, setPresentationScale]);

	return {
		scale,
		canvasWidth,
		canvasHeight,
		editableAreaWidth,
		editableAreaHeight,
		padding,
	};
};
