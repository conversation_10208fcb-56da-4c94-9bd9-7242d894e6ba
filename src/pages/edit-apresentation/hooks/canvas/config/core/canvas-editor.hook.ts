import { presentationInfoAtom } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import { useAtom, useAtomValue } from 'jotai';

import { nextElementAtom } from '@/shared/states/items/next-items.state';

import { useDebouncedUpsertElement } from '../../../element/manage-element.hook';
import { useCanvasInteractions } from '../../interactions';
import { useCanvasKeyboard } from '../../keyboards/keyboards.hook';
import { useCanvasConfig, useContainerWidth, useTransformer } from '..';
import { CANVAS_EDITOR_CONFIG } from '@/pages/edit-apresentation/data/constants/canvas-editor.constants';
import { ICanvasEditorConfig, ICanvasEditorInteractions } from '@/pages/edit-apresentation/types/canvas-editor.types';

interface IUseCanvasEditorHook extends ICanvasEditorConfig, ICanvasEditorInteractions {
	containerWidth: number;
	containerHeight: number;
}

export const useCanvasEditor = (): IUseCanvasEditorHook => {
	const { containerRef, width: containerWidth, height: containerHeight } = useContainerWidth();
	const presentation = useAtomValue(presentationInfoAtom);
	const { scale, canvasWidth, canvasHeight, editableAreaWidth, editableAreaHeight, padding } = useCanvasConfig(
		presentation,
		containerWidth,
		containerHeight,
	);
	const [nextElement, setNextElement] = useAtom(nextElementAtom);
	const { transformerRef, stageRef } = useTransformer();

	const canvasDimensions = {
		width: editableAreaWidth,
		height: editableAreaHeight,
	};

	const interactions = useCanvasInteractions({
		nextElement,
		setNextElement,
		scale,
		transformerRef,
		canvasWidth: canvasDimensions.width,
		canvasHeight: canvasDimensions.height,
		padding,
	});

	useDebouncedUpsertElement(CANVAS_EDITOR_CONFIG.DEBOUNCE_DELAY);
	useCanvasKeyboard();

	return {
		containerRef,
		stageRef,
		containerWidth,
		containerHeight,
		canvasHeight,
		canvasWidth,
		editableAreaWidth,
		editableAreaHeight,
		padding,
		scale,
		...interactions,
	};
};
