import { useEffect, useRef, useState } from 'react';

export function useContainerWidth() {
	const containerRef = useRef<HTMLDivElement>(null);
	const [width, setWidth] = useState(0);
	const [height, setHeight] = useState(0);

	useEffect(() => {
		if (containerRef.current) {
			const newWidth = containerRef.current.offsetWidth;
			const newHeight = containerRef.current.offsetHeight;
			setWidth(newWidth);
			setHeight(newHeight);
		}
		const resizeObserver = new ResizeObserver((entries) => {
			for (const entry of entries) {
				setWidth(entry.contentRect.width);
				setHeight(entry.contentRect.height);
			}
		});

		if (containerRef.current) {
			resizeObserver.observe(containerRef.current);
		}

		return () => {
			if (containerRef.current) {
				resizeObserver.disconnect();
			}
		};
	}, [containerRef]);

	return { containerRef, width, height };
}
