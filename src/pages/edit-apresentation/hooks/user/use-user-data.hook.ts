interface IUserData {
	name: string;
	email: string;
	avatar: string;
}

interface IUseUserDataHook {
	user: IUserData;
	isLoading: boolean;
	error: string | null;
}

export const useUserData = (): IUseUserDataHook => {
	const mockUser: IUserData = {
		name: '<PERSON>',
		email: '<EMAIL>',
		avatar: 'https://i.pravatar.cc/150',
	};

	return {
		user: mockUser,
		isLoading: false,
		error: null,
	};
};
