import { useSet<PERSON>tom } from 'jotai';
import { useCallback } from 'react';
import { itemsHistoryAtom } from '../../states/history/history.state';
import { IItem } from '../../types/item.type';

interface HistoryState {
	past: IItem[][];
	present: IItem[];
	future: IItem[][];
}

const areSnapshotsEqual = (a: IItem[], b: IItem[]): boolean => JSON.stringify(a) === JSON.stringify(b);
const updateHistoryState = (prevHistory: HistoryState, newItems: IItem[]): HistoryState => {
	if (areSnapshotsEqual(prevHistory.present, newItems)) return prevHistory;
	const lastSnapshot = prevHistory.present.length ? prevHistory.present : newItems;
	return {
		past: [...prevHistory.past, lastSnapshot],
		present: newItems,
		future: [],
	};
};

export const useSaveHistory = () => {
	const setHistory = useSetAtom(itemsHistoryAtom);

	const saveHistory = useCallback(
		({ items }: { items: IItem[] }) => {
			setHistory((prevHistory) => updateHistoryState(prevHistory, items));
		},
		[setHistory],
	);

	return saveHistory;
};
