import { useCallback, useEffect, useState } from 'react';

export const useHelpModal = () => {
	const [isOpen, setIsOpen] = useState(false);

	const openModal = useCallback(() => {
		setIsOpen(true);
	}, []);

	const closeModal = useCallback(() => {
		setIsOpen(false);
	}, []);

	const toggleModal = useCallback(() => {
		setIsOpen((prev) => !prev);
	}, []);

	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === 'F1') {
				event.preventDefault();
				openModal();
				return;
			}
			if (
				event.key === '?' &&
				!event.ctrlKey &&
				!event.altKey &&
				!event.metaKey &&
				!(
					event.target instanceof HTMLElement &&
					(event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.isContentEditable)
				)
			) {
				event.preventDefault();
				openModal();
				return;
			}
			if (event.key === 'Escape' && isOpen) {
				event.preventDefault();
				closeModal();
				return;
			}
		};

		document.addEventListener('keydown', handleKeyDown);
		return () => document.removeEventListener('keydown', handleKeyDown);
	}, [isOpen, openModal, closeModal]);

	useEffect(() => {
		if (isOpen) {
			const originalOverflow = document.body.style.overflow;
			document.body.style.overflow = 'hidden';
			return () => {
				document.body.style.overflow = originalOverflow;
			};
		}
	}, [isOpen]);

	return {
		isOpen,
		openModal,
		closeModal,
		toggleModal,
	};
};
