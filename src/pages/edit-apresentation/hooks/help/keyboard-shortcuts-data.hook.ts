import { useMemo } from 'react';

export interface ShortcutInfo {
	key: string;
	description: string;
	category: string;
	shortcut: {
		key: string;
		ctrl?: boolean;
		shift?: boolean;
		alt?: boolean;
	};
}

export const useKeyboardShortcutsData = () => {
	const shortcuts = useMemo<ShortcutInfo[]>(
		() => [
			{
				key: 'copy',
				description: 'Copiar elementos selecionados',
				category: 'Ações Básicas',
				shortcut: { key: 'C', ctrl: true },
			},
			{
				key: 'paste',
				description: 'Colar elementos copiados',
				category: 'Ações Básicas',
				shortcut: { key: 'V', ctrl: true },
			},
			{
				key: 'delete',
				description: 'Excluir elementos selecionados',
				category: 'Ações Básicas',
				shortcut: { key: 'Delete' },
			},
			{
				key: 'undo',
				description: 'Desfazer última ação',
				category: 'Ações Básicas',
				shortcut: { key: 'Z', ctrl: true },
			},
			{
				key: 'duplicate',
				description: 'Duplicar elementos selecionados',
				category: 'Ações Básicas',
				shortcut: { key: 'D', ctrl: true },
			},
			{
				key: 'selectAll',
				description: 'Selecionar todos os elementos',
				category: 'Seleção',
				shortcut: { key: 'A', ctrl: true },
			},
			{
				key: 'escape',
				description: 'Desselecionar todos os elementos',
				category: 'Seleção',
				shortcut: { key: 'Escape' },
			},
			{
				key: 'selectNext',
				description: 'Selecionar próximo elemento',
				category: 'Seleção',
				shortcut: { key: 'Tab' },
			},
			{
				key: 'selectPrevious',
				description: 'Selecionar elemento anterior',
				category: 'Seleção',
				shortcut: { key: 'Tab', shift: true },
			},
			{
				key: 'moveUp',
				description: 'Mover elemento para cima (1px)',
				category: 'Movimentação',
				shortcut: { key: '↑' },
			},
			{
				key: 'moveDown',
				description: 'Mover elemento para baixo (1px)',
				category: 'Movimentação',
				shortcut: { key: '↓' },
			},
			{
				key: 'moveLeft',
				description: 'Mover elemento para esquerda (1px)',
				category: 'Movimentação',
				shortcut: { key: '←' },
			},
			{
				key: 'moveRight',
				description: 'Mover elemento para direita (1px)',
				category: 'Movimentação',
				shortcut: { key: '→' },
			},
			{
				key: 'moveUpFast',
				description: 'Mover elemento para cima (10px)',
				category: 'Movimentação',
				shortcut: { key: '↑', shift: true },
			},
			{
				key: 'moveDownFast',
				description: 'Mover elemento para baixo (10px)',
				category: 'Movimentação',
				shortcut: { key: '↓', shift: true },
			},
			{
				key: 'moveLeftFast',
				description: 'Mover elemento para esquerda (10px)',
				category: 'Movimentação',
				shortcut: { key: '←', shift: true },
			},
			{
				key: 'moveRightFast',
				description: 'Mover elemento para direita (10px)',
				category: 'Movimentação',
				shortcut: { key: '→', shift: true },
			},
			{
				key: 'alignLeft',
				description: 'Alinhar elementos à esquerda',
				category: 'Alinhamento',
				shortcut: { key: 'L', ctrl: true, shift: true },
			},
			{
				key: 'alignCenter',
				description: 'Alinhar elementos ao centro horizontal',
				category: 'Alinhamento',
				shortcut: { key: 'C', ctrl: true, shift: true },
			},
			{
				key: 'alignRight',
				description: 'Alinhar elementos à direita',
				category: 'Alinhamento',
				shortcut: { key: 'R', ctrl: true, shift: true },
			},
			{
				key: 'alignTop',
				description: 'Alinhar elementos ao topo',
				category: 'Alinhamento',
				shortcut: { key: 'T', ctrl: true, shift: true },
			},
			{
				key: 'alignMiddle',
				description: 'Alinhar elementos ao centro vertical',
				category: 'Alinhamento',
				shortcut: { key: 'M', ctrl: true, shift: true },
			},
			{
				key: 'alignBottom',
				description: 'Alinhar elementos à base',
				category: 'Alinhamento',
				shortcut: { key: 'B', ctrl: true, shift: true },
			},
			{
				key: 'distributeHorizontal',
				description: 'Distribuir elementos horizontalmente',
				category: 'Distribuição',
				shortcut: { key: 'H', ctrl: true, shift: true },
			},
			{
				key: 'distributeVertical',
				description: 'Distribuir elementos verticalmente',
				category: 'Distribuição',
				shortcut: { key: 'V', ctrl: true, shift: true },
			},
			{
				key: 'bringToFront',
				description: 'Trazer para frente',
				category: 'Camadas',
				shortcut: { key: ']', ctrl: true, shift: true },
			},
			{
				key: 'sendToBack',
				description: 'Enviar para trás',
				category: 'Camadas',
				shortcut: { key: '[', ctrl: true, shift: true },
			},
			{
				key: 'bringForward',
				description: 'Avançar uma camada',
				category: 'Camadas',
				shortcut: { key: ']', ctrl: true },
			},
			{
				key: 'sendBackward',
				description: 'Recuar uma camada',
				category: 'Camadas',
				shortcut: { key: '[', ctrl: true },
			},
		],
		[],
	);

	const categorizedShortcuts = useMemo(() => {
		const categories: Record<string, ShortcutInfo[]> = {};

		shortcuts.forEach((shortcut) => {
			if (!categories[shortcut.category]) {
				categories[shortcut.category] = [];
			}
			categories[shortcut.category].push(shortcut);
		});

		return categories;
	}, [shortcuts]);

	const formatShortcut = (shortcut: ShortcutInfo['shortcut']): string => {
		const parts: string[] = [];

		if (shortcut.ctrl) parts.push('Ctrl');
		if (shortcut.shift) parts.push('Shift');
		if (shortcut.alt) parts.push('Alt');

		parts.push(shortcut.key);

		return parts.join(' + ');
	};

	return {
		shortcuts,
		categorizedShortcuts,
		formatShortcut,
	};
};
