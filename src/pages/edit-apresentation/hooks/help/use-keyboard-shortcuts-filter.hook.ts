import { useMemo, useState } from 'react';
import { ShortcutInfo } from './keyboard-shortcuts-data.hook';

interface UseKeyboardShortcutsFilterProps {
	categorizedShortcuts: Record<string, ShortcutInfo[]>;
	formatShortcut: (shortcut: ShortcutInfo['shortcut']) => string;
}

export function useKeyboardShortcutsFilter({ categorizedShortcuts, formatShortcut }: UseKeyboardShortcutsFilterProps) {
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

	const filteredShortcuts = useMemo(() => {
		const filtered: Record<string, ShortcutInfo[]> = {};

		Object.entries(categorizedShortcuts).forEach(([category, shortcuts]) => {
			if (selectedCategory && category !== selectedCategory) return;

			const filteredCategoryShortcuts = shortcuts.filter(
				(shortcut) =>
					shortcut.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
					formatShortcut(shortcut.shortcut).toLowerCase().includes(searchTerm.toLowerCase()),
			);

			if (filteredCategoryShortcuts.length > 0) {
				filtered[category] = filteredCategoryShortcuts;
			}
		});

		return filtered;
	}, [categorizedShortcuts, searchTerm, selectedCategory, formatShortcut]);

	const categories = Object.keys(categorizedShortcuts);

	const totalShortcuts = Object.values(filteredShortcuts).reduce((acc, shortcuts) => acc + shortcuts.length, 0);

	return {
		searchTerm,
		setSearchTerm,
		selectedCategory,
		setSelectedCategory,
		filteredShortcuts,
		categories,
		totalShortcuts,
	};
}
