import { useSet<PERSON>tom } from 'jotai';
import { contextMenuAtom, IContextMenuItem } from '../../states/context-menu/context-menu.state';

export function useContextMenu() {
	const setContextMenu = useSetAtom(contextMenuAtom);

	function openContextMenu(x: number, y: number, items: IContextMenuItem[]) {
		setContextMenu({
			visible: true,
			x,
			y,
			items,
		});
	}

	function closeContextMenu() {
		setContextMenu((prev) => ({ ...prev, visible: false }));
	}

	return { openContextMenu, closeContextMenu };
}
