import { useEffect } from 'react';
import { interactionMapping } from '../../data/cursor/cursor-type';
import { CursorType, InteractionType } from '../../types/cursor/interaction.type';

interface DynamicCursorProps {
	interaction: InteractionType;
}

const getCursorType = (interaction: InteractionType): CursorType => {
	return interaction ? interactionMapping[interaction] : CursorType.DEFAULT;
};

const applyCursorStyle = (cursor: CursorType) => {
	document.body.style.cursor = cursor;
};

export const useDynamicCursor = ({ interaction }: DynamicCursorProps) => {
	useEffect(() => {
		const cursor = getCursorType(interaction);
		applyCursorStyle(cursor);
		return () => applyCursorStyle(CursorType.DEFAULT);
	}, [interaction]);
};
