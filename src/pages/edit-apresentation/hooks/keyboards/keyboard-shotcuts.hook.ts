import { useEffect } from 'react';

interface KeyboardHandlers {
	copy: () => void;
	paste: () => void;
	delete: () => void;
	save?: () => void;
	undo?: () => void;
	redo?: () => void;
	selectAll?: () => void;
	escape?: () => void;
	moveUp?: () => void;
	moveDown?: () => void;
	moveLeft?: () => void;
	moveRight?: () => void;
	moveUpFast?: () => void;
	moveDownFast?: () => void;
	moveLeftFast?: () => void;
	moveRightFast?: () => void;
	alignLeft?: () => void;
	alignCenter?: () => void;
	alignRight?: () => void;
	alignTop?: () => void;
	alignMiddle?: () => void;
	alignBottom?: () => void;
	distributeHorizontal?: () => void;
	distributeVertical?: () => void;
	selectNext?: () => void;
	selectPrevious?: () => void;
	bringToFront?: () => void;
	sendToBack?: () => void;
	bringForward?: () => void;
	sendBackward?: () => void;
	duplicate?: () => void;
}

type KeyMapping = {
	test: (event: KeyboardEvent) => boolean;
	handler: () => void;
};

const defaultShortcuts: Record<string, { key: string; ctrl?: boolean; shift?: boolean }> = {
	copy: { key: 'c', ctrl: true },
	paste: { key: 'v', ctrl: true },
	save: { key: 's', ctrl: true },
	undo: { key: 'z', ctrl: true },
	redo: { key: 'y', ctrl: true },
	selectAll: { key: 'a', ctrl: true },
	delete: { key: 'Delete' },
	escape: { key: 'Escape' },
	moveUp: { key: 'ArrowUp' },
	moveDown: { key: 'ArrowDown' },
	moveLeft: { key: 'ArrowLeft' },
	moveRight: { key: 'ArrowRight' },
	moveUpFast: { key: 'ArrowUp', shift: true },
	moveDownFast: { key: 'ArrowDown', shift: true },
	moveLeftFast: { key: 'ArrowLeft', shift: true },
	moveRightFast: { key: 'ArrowRight', shift: true },
	alignLeft: { key: 'l', ctrl: true, shift: true },
	alignCenter: { key: 'c', ctrl: true, shift: true },
	alignRight: { key: 'r', ctrl: true, shift: true },
	alignTop: { key: 't', ctrl: true, shift: true },
	alignMiddle: { key: 'm', ctrl: true, shift: true },
	alignBottom: { key: 'b', ctrl: true, shift: true },
	distributeHorizontal: { key: 'h', ctrl: true, shift: true },
	distributeVertical: { key: 'v', ctrl: true, shift: true },
	selectNext: { key: 'Tab' },
	selectPrevious: { key: 'Tab', shift: true },
	bringToFront: { key: ']', ctrl: true, shift: true },
	sendToBack: { key: '[', ctrl: true, shift: true },
	bringForward: { key: ']', ctrl: true },
	sendBackward: { key: '[', ctrl: true },
	duplicate: { key: 'd', ctrl: true },
};

const createKeyTest =
	(key: string, ctrl = false, shift = false) =>
	(event: KeyboardEvent) => {
		const keyMatch = event.key === key;
		const ctrlMatch = ctrl ? event.ctrlKey : !event.ctrlKey;
		const shiftMatch = shift ? event.shiftKey : !event.shiftKey;

		if (!ctrl && !shift) {
			return keyMatch && !event.ctrlKey && !event.shiftKey && !event.altKey && !event.metaKey;
		}

		return keyMatch && ctrlMatch && shiftMatch;
	};

const buildKeyMappings = (handlers: KeyboardHandlers): KeyMapping[] => {
	const mappings: KeyMapping[] = [];
	const addMapping = (name: keyof KeyboardHandlers) => {
		const handler = handlers[name];
		if (handler) {
			const shortcut = defaultShortcuts[name];
			if (shortcut) {
				const { key, ctrl = false, shift = false } = shortcut;
				mappings.push({ test: createKeyTest(key, ctrl, shift), handler });
			}
		}
	};

	(['copy', 'paste', 'delete'] as (keyof KeyboardHandlers)[]).forEach(addMapping);
	(['save', 'undo', 'redo', 'selectAll', 'escape'] as (keyof KeyboardHandlers)[]).forEach(addMapping);
	(['moveUp', 'moveDown', 'moveLeft', 'moveRight'] as (keyof KeyboardHandlers)[]).forEach(addMapping);
	(['moveUpFast', 'moveDownFast', 'moveLeftFast', 'moveRightFast'] as (keyof KeyboardHandlers)[]).forEach(addMapping);
	(['alignLeft', 'alignCenter', 'alignRight', 'alignTop', 'alignMiddle', 'alignBottom'] as (keyof KeyboardHandlers)[]).forEach(addMapping);
	(['distributeHorizontal', 'distributeVertical'] as (keyof KeyboardHandlers)[]).forEach(addMapping);
	(['selectNext', 'selectPrevious'] as (keyof KeyboardHandlers)[]).forEach(addMapping);
	(['bringToFront', 'sendToBack', 'bringForward', 'sendBackward', 'duplicate'] as (keyof KeyboardHandlers)[]).forEach(addMapping);

	return mappings;
};

export const useKeyboardShortcuts = ({ handlers }: { handlers: KeyboardHandlers }): void => {
	useEffect(() => {
		const mappings: KeyMapping[] = buildKeyMappings(handlers);
		const handleKeyDown = (event: KeyboardEvent) => {
			if (
				event.target instanceof HTMLElement &&
				(event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.isContentEditable)
			) {
				return;
			}

			if (event.target instanceof HTMLElement) {
				const isSelectionElement =
					event.target.closest('[data-selection-enabled]') ||
					event.target.closest('[role="listbox"]') ||
					event.target.closest('[role="option"]') ||
					event.target.closest('.elements-layer-item') ||
					event.target.getAttribute('aria-selected') !== null;

				if (isSelectionElement && (event.ctrlKey || event.shiftKey || event.metaKey)) {
					const allowedKeysWithModifiers = ['c', 'v', 'z', 'y', 'a', 's', 'd'];
					if (!allowedKeysWithModifiers.includes(event.key.toLowerCase())) {
						return;
					}
				}
			}

			const mapping = mappings.find(({ test }) => test(event));
			if (mapping) {
				event.preventDefault();
				mapping.handler();
			}
		};

		document.addEventListener('keydown', handleKeyDown);
		return () => document.removeEventListener('keydown', handleKeyDown);
	}, [handlers]);
};
