import { useMutation } from '@tanstack/react-query';
import { useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { ICreateElementDto } from '../../dtos/elements/create-element.dto';
import { diffItems } from '../../lib/item/get-alterations';
import { mapElementsToItems } from '../../lib/presentation';
import { updateElementRequest } from '../../services/requests/elements/update';
import { IPresentationInfo } from '../../states/presentation/presentation-info.state';
import { IItem, ITEM_STATUS } from '../../types/item.type';
import { useSaveHistory } from '../history/history.hook';

interface IUpdatedItems {
	debouncedData: IItem[];
	updateItem: (updatedItem: IItem) => void;
	buildPayload: (item: IItem) => ICreateElementDto;
	saveHistory: ReturnType<typeof useSaveHistory>;
	presentation: IPresentationInfo | null;
}

export const useProcessUpdatedItems = ({ debouncedData, updateItem, buildPayload, saveHistory, presentation }: IUpdatedItems) => {
	const oldDataRef = useRef<IItem[]>(mapElementsToItems(presentation?.elements ?? []));

	const processItensMutation = useMutation({
		mutationKey: ['processItens'],
		mutationFn: async () => {
			const restItems = debouncedData.filter((item) => item.status !== ITEM_STATUS.NEW && item.status !== ITEM_STATUS.DELETED);
			const changedItems = diffItems(restItems, oldDataRef.current);
			if (changedItems.length === 0) return;
			for (const item of changedItems) {
				updateItem({ ...item, status: ITEM_STATUS.UPDATING } as IItem);
				const res = await updateElementRequest({
					id: item.id!,
					items: buildPayload(item),
				});
				if (res.success) {
					updateItem({ ...item, status: ITEM_STATUS.SAVED } as IItem);
				} else {
					toast.dismiss();
					toast.error(`Erro ao atualizar o elemento ${item.name}`);
				}
			}
			if (changedItems.length > 0) saveHistory({ items: debouncedData });
			oldDataRef.current = restItems;
		},
	});

	return useCallback(async () => {
		await processItensMutation.mutateAsync();
	}, [processItensMutation]);
};
