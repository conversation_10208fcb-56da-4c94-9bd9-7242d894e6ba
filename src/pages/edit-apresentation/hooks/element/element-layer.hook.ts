// import { useAtom } from 'jotai';
// import { useState } from 'react';
// import { itemsAtom, selectedItemsIdsAtom, updateLayers } from '../../../../shared/states/items/object-item.state';
// import { IItem } from '../../types/item.type';

// export const useElementLayer = () => {
// 	const [items, setItems] = useAtom(itemsAtom);
// 	const [, setSelectedItemsIds] = useAtom(selectedItemsIdsAtom);
// 	const [editingId, setEditingId] = useState<string | null>(null);
// 	const [editingName, setEditingName] = useState<string>('');

// 	const layeredItems = items.sort((a, b) => b.layer - a.layer);

// 	const handleReorder = (newOrder: IItem[]) => {
// 		const newLayers = updateLayers(newOrder);
// 		setItems([...newLayers]);
// 	};

// 	const handleSelect = ({
// 		itemId,
// 		modifiers,
// 	}: {
// 		itemId: string;
// 		modifiers: {
// 			shiftKey: boolean;
// 			ctrlKey: boolean;
// 		};
// 	}) => {
// 		setSelectedItemsIds((prevSelectedIds) => {
// 			if (!modifiers?.ctrlKey && !modifiers?.shiftKey) return [itemId];
// 			if (modifiers?.ctrlKey) {
// 				if (prevSelectedIds.includes(itemId)) return prevSelectedIds.filter((selectedId) => selectedId !== itemId);
// 				return [...prevSelectedIds, itemId];
// 			}
// 			if (modifiers?.shiftKey && prevSelectedIds.length > 0) {
// 				const lastSelectedId = prevSelectedIds[prevSelectedIds.length - 1];
// 				const lastSelectedIndex = layeredItems.findIndex((item) => item.tempId === lastSelectedId);
// 				const currentIndex = layeredItems.findIndex((item) => item.tempId === itemId);
// 				if (lastSelectedIndex === -1 || currentIndex === -1) return [...prevSelectedIds, itemId];
// 				const startIndex = Math.min(lastSelectedIndex, currentIndex);
// 				const endIndex = Math.max(lastSelectedIndex, currentIndex);
// 				const itemsInRange = layeredItems.slice(startIndex, endIndex + 1).map((item) => item.tempId);
// 				return [...new Set([...prevSelectedIds, ...itemsInRange])];
// 			}
// 			return [...prevSelectedIds, itemId];
// 		});
// 	};

// 	const handleStartEdit = (id: string, name: string) => {
// 		setEditingId(id);
// 		setEditingName(name);
// 	};

// 	const handleCommitEdit = (id: string, newName: string) => {
// 		setItems((prevItems) => prevItems.map((item) => (item.tempId === id ? { ...item, name: newName } : item)));
// 		setEditingId(null);
// 	};

// 	const handleCancelEdit = () => {
// 		setEditingId(null);
// 	};

// 	return {
// 		editingName,
// 		editingId,
// 		layeredItems,
// 		handleReorder,
// 		handleSelect,
// 		handleStartEdit,
// 		handleCommitEdit,
// 		setEditingName,
// 		handleCancelEdit,
// 	};
// };
