import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { deleteElementRequest } from '../../services/requests/elements/delete';
import { IItem, ITEM_STATUS } from '../../types/item.type';
import { useSaveHistory } from '../history/history.hook';

interface IProcessDeletedItems {
	debouncedData: IItem[];
	updateItem: (updatedItem: IItem) => void;
	deleteItem: (itemId: string) => void;
	saveHistory: ReturnType<typeof useSaveHistory>;
}

export const useProcessDeletedItems = ({ debouncedData, deleteItem, saveHistory, updateItem }: IProcessDeletedItems) => {
	const deletedItemsMutation = useMutation({
		mutationKey: ['processDeletedItems'],
		mutationFn: async () => {
			const deletedItems = debouncedData.filter((item) => item.status === ITEM_STATUS.DELETED);
			if (deletedItems.length === 0) return;

			for (const item of deletedItems) {
				if (item.id) {
					const res = await deleteElementRequest({
						id: item.id,
					});
					if (res.success) {
						deleteItem(item.tempId);
					} else {
						updateItem({ ...item, status: ITEM_STATUS.SAVED } as IItem);
						toast.dismiss();
						toast.error(`Erro ao deletar o elemento ${item.name}`);
					}
					saveHistory({ items: debouncedData });
				}
			}
		},
	});

	return useCallback(async () => {
		await deletedItemsMutation.mutateAsync();
	}, [deletedItemsMutation]);
};
