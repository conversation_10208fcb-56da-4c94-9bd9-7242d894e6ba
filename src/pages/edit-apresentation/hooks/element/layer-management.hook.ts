import { itemsAtom, updateLayers } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';
import { useCallback, useMemo } from 'react';
import { IItem } from '../../types/item.type';

export const useLayerManagement = () => {
	const [items, setItems] = useAtom(itemsAtom);

	const layeredItems = useMemo(() => items.sort((a, b) => b.layer - a.layer), [items]);

	const handleReorder = useCallback(
		(newOrder: IItem[]) => {
			const newLayers = updateLayers(newOrder);
			setItems([...newLayers]);
		},
		[setItems],
	);

	return {
		layeredItems,
		handleReorder,
	};
};
