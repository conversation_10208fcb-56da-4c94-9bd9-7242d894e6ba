import { selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useSet<PERSON>tom } from 'jotai';
import { useCallback } from 'react';
import { IItem } from '../../types/item.type';

interface SelectionModifiers {
	shiftKey: boolean;
	ctrlKey: boolean;
}

interface SelectParams {
	itemId: string;
	modifiers: SelectionModifiers;
}

type ItemId = string;

const getCtrlSelection = (itemId: ItemId, prevSelectedIds: ItemId[]): ItemId[] =>
	prevSelectedIds.includes(itemId) ? prevSelectedIds.filter((id) => id !== itemId) : [...prevSelectedIds, itemId];

const getShiftSelection = (itemId: ItemId, prevSelectedIds: ItemId[], layeredItem: IItem[]): ItemId[] => {
	if (prevSelectedIds.length === 0) return [itemId];
	const lastId = prevSelectedIds[prevSelectedIds.length - 1];
	const lastIdx = layeredItem.findIndex((item) => item.tempId === lastId);
	const currIdx = layeredItem.findIndex((item) => item.tempId === itemId);
	if (lastIdx === -1 || currIdx === -1) return [...prevSelectedIds, itemId];
	const [start, end] = [Math.min(lastIdx, currIdx), Math.max(lastIdx, currIdx)];
	const rangeIds = layeredItem.slice(start, end + 1).map((item) => item.tempId);
	return Array.from(new Set([...prevSelectedIds, ...rangeIds]));
};

export const useItemSelection = (layeredItem: IItem[]) => {
	const setSelectedItemsIds = useSetAtom(selectedItemsIdsAtom);
	const handleSelect = useCallback(
		({ itemId, modifiers }: SelectParams) => {
			setSelectedItemsIds((prevSelectedIds) => {
				if (!modifiers.ctrlKey && !modifiers.shiftKey) return [itemId];
				if (modifiers.ctrlKey) return getCtrlSelection(itemId, prevSelectedIds);
				if (modifiers.shiftKey && prevSelectedIds.length > 0) return getShiftSelection(itemId, prevSelectedIds, layeredItem);
				return [...prevSelectedIds, itemId];
			});
		},
		[setSelectedItemsIds, layeredItem],
	);

	return { handleSelect };
};
