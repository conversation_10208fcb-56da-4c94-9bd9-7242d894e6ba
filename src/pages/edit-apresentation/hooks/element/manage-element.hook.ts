import { ICreateElementDto } from '@/pages/edit-apresentation/dtos/elements/create-element.dto';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect, useRef } from 'react';
import { itemsAtom, removeItemAtom, updateItemAtom } from '../../../../shared/states/items/object-item.state';
import { presentationInfoAtom } from '../../states/presentation/presentation-info.state';
import { useSaveHistory } from '../history/history.hook';

import useDebounce from '@/hooks/use-debounce';
import { toast } from 'sonner';
import { useProcessDeletedItems } from './deleted-item.hook';
import { useProcessNewItems } from './new-item.hook';
import { useProcessUpdatedItems } from './updated-item.hook';

export const useDebouncedUpsertElement = (debounceDelay: number): void => {
	const itemsData = useAtomValue(itemsAtom);
	const presentation = useAtomValue(presentationInfoAtom);
	const updateItem = useSetAtom(updateItemAtom);
	const deleteItem = useSetAtom(removeItemAtom);
	const debouncedElementData = useDebounce(itemsData, debounceDelay);
	const saveHistory = useSaveHistory();
	const pendingChangesRef = useRef<Set<string>>(new Set());

	const createElementPayload = useCallback(
		(item: (typeof itemsData)[number]): ICreateElementDto => ({
			elementType: item.type,
			title: item.name,
			content: item.content ?? {},
			positionX: item.position.x,
			positionY: item.position.y,
			width: item.size.width,
			height: item.size.height,
			presentationId: presentation!.id,
			layer: item.layer,
		}),
		[presentation],
	);

	const processDeletedItems = useProcessDeletedItems({
		debouncedData: debouncedElementData,
		deleteItem,
		saveHistory,
		updateItem,
	});
	const processNewItems = useProcessNewItems({
		debouncedData: debouncedElementData,
		updateItem,
		buildPayload: createElementPayload,
		saveHistory,
	});
	const processUpdatedItems = useProcessUpdatedItems({
		debouncedData: debouncedElementData,
		updateItem,
		buildPayload: createElementPayload,
		saveHistory,
		presentation,
	});
	const prevDebouncedDataRef = useRef(debouncedElementData);

	useEffect(() => {
		if (prevDebouncedDataRef.current === debouncedElementData) return;
		if (!presentation?.id) return;
		prevDebouncedDataRef.current = debouncedElementData;
		const processItems = async (): Promise<void> => {
			try {
				await processDeletedItems();
				await processNewItems();
				await processUpdatedItems();
			} catch (error) {
				toast.dismiss();
				toast.error(`Erro ao processar itens: ${error}`);
				debouncedElementData.forEach((item) => item.id && pendingChangesRef.current.add(item.id));
			}
		};

		processItems();
	}, [debouncedElementData, presentation?.id, processDeletedItems, processNewItems, processUpdatedItems]);
};
