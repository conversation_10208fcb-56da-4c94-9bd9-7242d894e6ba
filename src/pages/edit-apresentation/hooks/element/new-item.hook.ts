import { ICreateElementDto } from '@/pages/edit-apresentation/dtos/elements/create-element.dto';
import { IItem, ITEM_STATUS } from '@/pages/edit-apresentation/types/item.type';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { createElementRequest } from '../../services/requests/elements/create';
import { useSaveHistory } from '../history/history.hook';

interface IProcessNewItems {
	debouncedData: IItem[];
	updateItem: (updatedItem: IItem) => void;
	buildPayload: (item: IItem) => ICreateElementDto;
	saveHistory: ReturnType<typeof useSaveHistory>;
}

export const useProcessNewItems = ({ debouncedData, updateItem, buildPayload, saveHistory }: IProcessNewItems) => {
	const newItemsMutation = useMutation({
		mutationKey: ['processNewItems'],
		mutationFn: async () => {
			const newItems = debouncedData.filter((item) => item.status === ITEM_STATUS.NEW);
			if (newItems.length === 0) return false;
			for (const item of newItems) {
				try {
					updateItem({ ...item, status: ITEM_STATUS.UPDATING });
					const controller = new AbortController();
					const timeoutId = setTimeout(() => controller.abort(), 10000);
					const response = await createElementRequest({
						items: buildPayload(item),
					});
					clearTimeout(timeoutId);
					if (!response.success) {
						console.error('Erro ao criar elemento:', response.data);
						toast.dismiss();
						toast.error(response.data.message || 'Erro ao criar elemento', {
							id: 'socket-error',
						});
						updateItem({ ...item, status: ITEM_STATUS.UPDATING });
						continue;
					}
					updateItem({ ...item, status: ITEM_STATUS.SAVED, id: String(response.data.id) });
				} catch (error) {
					toast.dismiss();
					toast.error('Erro ao processar item. Tentando novamente mais tarde.');
				}
			}
			if (newItems.length > 0) saveHistory({ items: debouncedData });
		},
	});

	return useCallback(async () => {
		await newItemsMutation.mutateAsync();
	}, [newItemsMutation]);
};
