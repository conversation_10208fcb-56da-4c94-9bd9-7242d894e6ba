import { useSet<PERSON>tom } from 'jotai';
import { useEffect } from 'react';
import { itemsAtom } from '../../../../shared/states/items/object-item.state';
import { usePresentationGetById } from '../../../presentations/hooks/crud/get-by-id.hook';
import { mapElementsToItems } from '../../lib/presentation';
import { presentationInfoAtom } from '../../states/presentation/presentation-info.state';

const getErrorMessage = (status: number): string =>
	({
		404: 'Apresentação não encontrada',
		403: 'Você não tem permissão para acessar esta apresentação',
		500: 'Erro interno do servidor',
	})[status] ?? `Ocorreu um erro inesperado (${status})`;

type UseSetupPresentationReturn = {
	isLoading: boolean;
	success: boolean;
	status: number;
	errorMessage: string;
};

export function useSetupPresentation(id: string): UseSetupPresentationReturn {
	const { data, isLoading } = usePresentationGetById(id);
	const setPresentationInfo = useSetAtom(presentationInfoAtom);
	const setItems = useSetAtom(itemsAtom);

	useEffect(() => {
		if (!isLoading && data?.success && data.data?.elements) {
			setPresentationInfo(data.data);
			setItems(mapElementsToItems(data.data.elements));
		}
	}, [data, isLoading, setPresentationInfo, setItems]);

	const status = data?.status ?? 0;
	const success = data?.success ?? false;

	return {
		isLoading,
		success,
		status,
		errorMessage: success ? '' : getErrorMessage(status),
	};
}
