import { useNavigate } from 'react-router-dom';

export interface NavActionsHookReturn {
	handleNavigateToPresentation: () => void;
	handleNavigateToHome: () => void;
}

export const useNavActions = (): NavActionsHookReturn => {
	const navigate = useNavigate();

	const handleNavigateToPresentation = () => {
		navigate('/presentations');
	};

	const handleNavigateToHome = () => {
		navigate('/');
	};

	return {
		handleNavigateToPresentation,
		handleNavigateToHome,
	};
};
