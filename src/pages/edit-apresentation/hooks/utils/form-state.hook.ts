import { useCallback, useEffect, useState } from 'react';

const convertValue = <T>(value: string | number | undefined): T | undefined => {
	if (value === '' || value === undefined) {
		return undefined;
	}
	const num = Number(value);
	return isNaN(num) ? undefined : (num as T);
};

export function useFormHandler<T>(defaultValue: T, content: T | undefined, onChange: (data: T) => void) {
	const [formData, setFormData] = useState<T>(() => content ?? defaultValue);

	useEffect(() => {
		if (content === undefined) {
			setFormData(defaultValue);
			return;
		}
		setFormData(content);
	}, [content, defaultValue]);

	const updateFormData = useCallback(
		(newData: T) => {
			setFormData(newData);
			onChange(newData);
		},
		[onChange],
	);

	const updateField = useCallback(
		<K extends keyof T>(field: K, value: string | T[K]) => {
			const convertedValue = typeof defaultValue[field] === 'number' && typeof value === 'string' ? convertValue<number>(value) : value;

			const newData = { ...formData, [field]: convertedValue };
			updateFormData(newData as T);
		},
		[formData, updateFormData, defaultValue],
	);

	return {
		formData,
		setFormData: updateFormData,
		updateField,
	};
}
