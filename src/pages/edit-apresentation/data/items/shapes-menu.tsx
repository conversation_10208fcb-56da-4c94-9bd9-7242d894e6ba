import { Calendar, Clock, CloudSunRain, Grid, Image, RectangleHorizontal } from 'lucide-react';
import { IItemShapeType, ITEM_SHAPE_TYPE } from '../../types/item.type';

export interface IItemMenu {
	readonly value: IItemShapeType;
	readonly icon: React.ReactElement<{ className?: string }>;
	readonly name: string;
	readonly actionLabel?: string;
}

export const SHAPES_MENU: ReadonlyArray<IItemMenu> = [
	{
		value: ITEM_SHAPE_TYPE.RECTANGLE,
		icon: <RectangleHorizontal className="h-5 w-5" />,
		name: 'Quadrado',
		actionLabel: 'Insere um novo quadrado',
	},
	// {
	// 	icon: <LucideType size={22} />,
	// 	name: 'Texto',
	// 	value: ITEM_SHAPE_TYPE.TEXT,
	// 	actionLabel: 'Insere um novo texto',
	// },
	{
		value: ITEM_SHAPE_TYPE.CLOCK,
		icon: <Clock className="h-5 w-5" />,
		name: '<PERSON><PERSON><PERSON><PERSON>',
		actionLabel: 'Insere um novo relógio',
	},
	{
		value: ITEM_SHAPE_TYPE.CALENDAR,
		icon: <Calendar className="h-5 w-5" />,
		name: 'Calendário',
		actionLabel: 'Insere um novo calendário',
	},
	{
		value: ITEM_SHAPE_TYPE.CAROUSEL,
		icon: <Grid className="h-5 w-5" />,
		name: 'Carrossel',
		actionLabel: 'Insere um novo carrossel',
	},
	{
		value: ITEM_SHAPE_TYPE.IMAGE,
		icon: <Image className="h-5 w-5" />,
		name: 'Imagem',
		actionLabel: 'Insere uma nova imagem',
	},
	{
		value: ITEM_SHAPE_TYPE.WEATHER,
		icon: <CloudSunRain className="h-5 w-5" />,
		name: 'Tempo',
		actionLabel: 'Insere uma nova previsão do tempo',
	},
];
