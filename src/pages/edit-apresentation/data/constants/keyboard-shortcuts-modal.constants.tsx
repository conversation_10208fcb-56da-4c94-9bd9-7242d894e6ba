import { Keyboard, Search, Zap } from 'lucide-react';

export const CATEGORY_ICONS: Record<string, React.ReactNode> = {
	'Ações Básicas': <Zap className="h-4 w-4" />,
	Seleção: <Search className="h-4 w-4" />,
	Movimentação: <Keyboard className="h-4 w-4" />,
	Alinhamento: <div className="h-4 w-4 border border-current" />,
	Distribuição: (
		<div className="grid h-4 w-4 grid-cols-2 gap-0.5">
			<div className="rounded-sm bg-current"></div>
			<div className="rounded-sm bg-current"></div>
			<div className="rounded-sm bg-current"></div>
			<div className="rounded-sm bg-current"></div>
		</div>
	),
	Camadas: (
		<div className="relative h-4 w-4">
			<div className="absolute inset-0 border border-current"></div>
			<div className="bg-current/20 absolute inset-1 border border-current"></div>
		</div>
	),
};

export const CATEGORY_COLORS: Record<string, string> = {
	'Ações Básicas': 'bg-blue-500/10 text-blue-400 border-blue-500/20',
	Seleção: 'bg-green-500/10 text-green-400 border-green-500/20',
	Movimentação: 'bg-purple-500/10 text-purple-400 border-purple-500/20',
	Alinhamento: 'bg-orange-500/10 text-orange-400 border-orange-500/20',
	Distribuição: 'bg-pink-500/10 text-pink-400 border-pink-500/20',
	Camadas: 'bg-cyan-500/10 text-cyan-400 border-cyan-500/20',
};
