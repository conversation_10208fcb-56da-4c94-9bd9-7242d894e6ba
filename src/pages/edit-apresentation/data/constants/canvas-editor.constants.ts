export const CANVAS_EDITOR_CONFIG = {
  DEBOUNCE_DELAY: 300,
  DEFAULT_PADDING: 20,
  MIN_CANVAS_WIDTH: 800,
  MIN_CANVAS_HEIGHT: 600,
} as const;

export const ANIMATION_CONFIG = {
  MOTION: {
    LEFT: {
      initial: { opacity: 0, x: -100 },
      animate: { opacity: 1, x: 0 },
      transition: { duration: 0.5, delay: 0.2 },
    },
    CENTER: {
      initial: { opacity: 0, y: -100 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.5 },
    },
    RIGHT: {
      initial: { opacity: 0, x: 100 },
      animate: { opacity: 1, x: 0 },
      transition: { duration: 0.5 },
    },
  },
  BUTTON_TAP: { scale: 0.95 },
  HOVER_SCALE: 1.05,
} as const;

export const TOOLTIP_MESSAGES = {
  BACK_TO_PRESENTATIONS: 'Voltar para apresentações',
  STREAM_HUB_HOME: 'Stream Hub - Tela inicial',
  KEYBOARD_SHORTCUTS: 'Atalhos de teclado (F1 ou ?)',
  OPEN_USER_MENU: 'Abrir menu do usuário',
  OPEN_HELP: 'Abrir ajuda de atalhos',
} as const;
