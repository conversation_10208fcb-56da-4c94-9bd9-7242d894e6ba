import { IItemShapeType } from '../../types/item.type';
import { DEFAULT_CALENDAR_OBJECT } from './calendar';
import { DEFAULT_CAROUSEL_OBJECT } from './caroulsel';
import { DEFAULT_IMAGE_STYLE } from './image';
import { DEFAULT_SQUARE_STYLE } from './square';
import { DEFAULT_WEATHER_SETTINGS } from './weather';

export const getContextNextElement = (type: IItemShapeType): object | undefined => {
	switch (type) {
		case 'text':
			return DEFAULT_SQUARE_STYLE;
		case 'weather':
			return DEFAULT_WEATHER_SETTINGS;
		case 'rectangle':
			return DEFAULT_SQUARE_STYLE;
		case 'image':
			return DEFAULT_IMAGE_STYLE;
		case 'carousel':
			return DEFAULT_CAROUSEL_OBJECT;
		case 'calendar':
			return DEFAULT_CALENDAR_OBJECT;
		default:
			return undefined;
	}
};
