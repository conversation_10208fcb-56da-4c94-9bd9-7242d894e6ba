import { ICON_TYPES, I<PERSON><PERSON><PERSON>, WEATHER_DISPLAY_THEME } from '../../components/properties-panel/item-editors/weather/weather-editor.type';

export const DEFAULT_WEATHER_SETTINGS: IWeather = {
	labelTwo: 'Clima',
	iconType: ICON_TYPES.CLIMACONS,
	forecastMode: 'Both',
	forecastDays: 3,
	themeOptions: {
		theme: WEATHER_DISPLAY_THEME.CUSTOM,
		customTheme: {
			background: '#252525',
			accent: '#659c51',
			text: '#ffffff',
			highTemp: '#659c51',
			lowTemp: '#659c51',
			shadow: 'rgba(0,0,0,0.3)',
			sunAndThunder: '#659c51',
			moon: '#ffffff',
			cloud: '#ffffff',
			cloudFill: '#ffffff',
			rain: '#659c51',
			snow: '#ffffff',
		},
	},
};
