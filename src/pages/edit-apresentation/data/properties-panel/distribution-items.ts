import { AlignHorizontalDistributeCenter, AlignVerticalDistributeCenter } from 'lucide-react';
import { DistributionType } from '../../components/properties-panel/shared/alignment/align-elements';

export interface IDistributionButton {
	distributionType: DistributionType;
	icon: React.ElementType;
	tooltip: string;
	minItems: number;
}

class DistributionButtonFactory {
	public static create(distributionType: DistributionType, icon: React.ElementType, tooltip: string, minItems: number = 3): IDistributionButton {
		return { distributionType, icon, tooltip, minItems };
	}
}

export const distributionButtonsConfig: IDistributionButton[] = [
	DistributionButtonFactory.create(DistributionType.HORIZONTAL_CENTERS, AlignHorizontalDistributeCenter, 'Distribuir Centros Horizontalmente', 3),
	DistributionButtonFactory.create(DistributionType.VERTICAL_CENTERS, AlignVerticalDistributeCenter, 'Distribuir Centros Verticalmente', 3),
];
