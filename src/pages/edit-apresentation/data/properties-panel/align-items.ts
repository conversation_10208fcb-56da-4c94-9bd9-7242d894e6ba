import { AlignCenterHorizontal, AlignCenterVertical, AlignEndHorizontal, AlignEndVertical, AlignStartHorizontal, AlignStartVertical } from 'lucide-react';
import { AlignmentType } from '../../components/properties-panel/shared/alignment/align-elements';

export interface IAlignmentButton {
	alignmentType: AlignmentType;
	icon: React.ElementType;
	tooltip: string;
}

class AlignmentButtonFactory {
	public static create(alignmentType: AlignmentType, icon: React.ElementType, tooltip: string): IAlignmentButton {
		return { alignmentType, icon, tooltip };
	}
}

export const alignmentButtonsConfig: IAlignmentButton[] = [
	AlignmentButtonFactory.create(AlignmentType.LEFT, AlignStartVertical, 'Alinhar à Esquerda'),
	AlignmentButtonFactory.create(AlignmentType.CENTER, AlignCenterVertical, 'Centralizar Horizontalmente'),
	AlignmentButtonFactory.create(AlignmentType.RIGHT, AlignEndVertical, 'Alinhar à Direita'),
	AlignmentButtonFactory.create(AlignmentType.TOP, AlignStartHorizontal, 'Alinhar ao Topo'),
	AlignmentButtonFactory.create(AlignmentType.MIDDLE, AlignCenterHorizontal, 'Centralizar Verticalmente'),
	AlignmentButtonFactory.create(AlignmentType.BOTTOM, AlignEndHorizontal, 'Alinhar à Base'),
];
