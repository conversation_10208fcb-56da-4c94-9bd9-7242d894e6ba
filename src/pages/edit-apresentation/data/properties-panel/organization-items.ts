import { 
	BringT<PERSON><PERSON><PERSON>t, 
	SendToBack, 
	ChevronUp, 
	ChevronDown, 
	Copy, 
	Trash2 
} from 'lucide-react';
import { OrganizationType } from '../../components/properties-panel/shared/alignment/align-elements';

export interface IOrganizationButton {
	organizationType: OrganizationType;
	icon: React.ElementType;
	tooltip: string;
	minItems: number;
	variant?: 'default' | 'destructive';
}

class OrganizationButtonFactory {
	public static create(
		organizationType: OrganizationType, 
		icon: React.ElementType, 
		tooltip: string,
		minItems: number = 1,
		variant: 'default' | 'destructive' = 'default'
	): IOrganizationButton {
		return { organizationType, icon, tooltip, minItems, variant };
	}
}

export const organizationButtonsConfig: IOrganizationButton[] = [
	OrganizationButtonFactory.create(
		OrganizationType.BRING_TO_FRONT, 
		BringToFront, 
		'Trazer para Frente',
		1
	),
	OrganizationButtonFactory.create(
		OrganizationType.SEND_TO_BACK, 
		SendToBack, 
		'Enviar para Trás',
		1
	),
	OrganizationButtonFactory.create(
		OrganizationType.BRING_FORWARD, 
		ChevronUp, 
		'Avançar uma Camada',
		1
	),
	OrganizationButtonFactory.create(
		OrganizationType.SEND_BACKWARD, 
		ChevronDown, 
		'Recuar uma Camada',
		1
	),
	OrganizationButtonFactory.create(
		OrganizationType.DUPLICATE, 
		Copy, 
		'Duplicar Seleção',
		1
	),
	OrganizationButtonFactory.create(
		OrganizationType.DELETE, 
		Trash2, 
		'Excluir Seleção',
		1,
		'destructive'
	),
];
