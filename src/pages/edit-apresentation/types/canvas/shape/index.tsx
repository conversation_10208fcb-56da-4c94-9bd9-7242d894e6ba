import { SelectionBox } from '@/pages/edit-apresentation/hooks/canvas/interactions';
import Konva from 'konva';

export interface ICanvasDimensions {
	canvasWidth: number;
	canvasHeight: number;
}

export interface IShapeDimensions {
	x: number;
	y: number;
	width: number;
	height: number;
}

export interface IShapeEvents {
	onClick: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onDragStart: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onDragEnd: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onTransformEnd: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onDragMove: ({ id, event }: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => void;
}

export interface IShapeMouseEvents {
	onMouseMove: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onMouseLeave: () => void;
}

export interface IShapeConfiguration extends ICanvasDimensions, IShapeDimensions, IShapeEvents, IShapeMouseEvents {
	id: string;
	isSelected: boolean;
	currentlyHoveredItem: { name: string; width: number; height: number; tempId: string } | null;
}

export interface IShapeRenderOptions extends ICanvasDimensions, IShapeEvents, IShapeMouseEvents {
	isSelected: boolean;
	scale: number;
	currentlyHoveredItem: { name: string; width: number; height: number; tempId: string } | null;
}

export interface IShapesLayerProps extends ICanvasDimensions {
	onSelectShape: (params: { idElement: string; event: Konva.KonvaEventObject<MouseEvent> }) => void;
	onDragEnd: (params: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => void;
	onTransformEnd: () => void;
	onDragStart: (params: { id: string }) => void;
	onDragMove: ({ id, event }: { id: string; event: Konva.KonvaEventObject<MouseEvent> }) => void;
	onTransformStart: (e: Konva.KonvaEventObject<MouseEvent>, id: string) => void;
	transformerRef: React.MutableRefObject<Konva.Transformer | null>;
	scale: number;
	hoverPosition: { x: number; y: number } | null;
	onShapeMouseEnter: (item: { name: string; width: number; height: number; tempId: string }, position: { x: number; y: number }) => void;
	onShapeMouseLeave: () => void;
	selectionBox: SelectionBox | null;
	// layerRef: React.MutableRefObject<Konva.Layer | null>;
	clearHoveredShape: () => void;
	currentlyHoveredItem: { name: string; width: number; height: number; tempId: string } | null;
	handleTransform: () => void;
}
