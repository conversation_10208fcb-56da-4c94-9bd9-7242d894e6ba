import { IShapeConfiguration } from './shape';

export interface ITextShapeConfiguration extends IShapeConfiguration {
	isEditing?: boolean;
}

export interface ITextShapeRenderOptions {
	isSelected: boolean;
	isEditing?: boolean;
	onClick: (e: import('konva/lib/Node').KonvaEventObject<MouseEvent>) => void;
	onDragEnd: (e: import('konva/lib/Node').KonvaEventObject<MouseEvent>) => void;
	onTransformEnd: (e: import('konva/lib/Node').KonvaEventObject<MouseEvent>) => void;
	onDragStart: () => void;
	onDragMove: (args: { id: string; event: import('konva/lib/Node').KonvaEventObject<MouseEvent> }) => void;
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	onMouseMove: (e: import('konva/lib/Node').KonvaEventObject<MouseEvent>) => void;
	onMouseLeave: () => void;
	currentlyHoveredItem: { name: string; width: number; height: number; tempId: string } | null;
}
