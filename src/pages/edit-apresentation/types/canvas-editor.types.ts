import React from 'react';
import Konva from 'konva';
import { KonvaEventObject } from 'konva/lib/Node';

export interface ICanvasEditorProps {
	propertiesPanelRef: React.RefObject<HTMLDivElement>;
}

export interface ICanvasEditorConfig {
	containerRef: React.RefObject<HTMLDivElement>;
	stageRef: React.RefObject<Konva.Stage>;
	transformerRef: React.RefObject<Konva.Transformer>;
	scale: number;
	canvasWidth: number;
	canvasHeight: number;
	editableAreaWidth: number;
	editableAreaHeight: number;
	padding: number;
}

export interface ICanvasEditorInteractions {
	handleCanvasClick: (e: KonvaEventObject<MouseEvent>) => void;
	handleMouseMove: (e: KonvaEventObject<MouseEvent>) => void;
	handleSelectShape: (params: { idElement: string; event: KonvaEventObject<MouseEvent> }) => void;
	handleDragEnd: (params: { id: string; event: KonvaEventObject<MouseEvent> }) => void;
	handleTransformEnd: (e: KonvaEventObject<Event>) => void;
	handleDragStart: (params: { id: string }) => void;
	handleDragMove: (e: KonvaEventObject<DragEvent>) => void;
	handleMouseLeave: () => void;
	handleTransformStart: (e: KonvaEventObject<Event>) => void;
	handleTransform: (e: KonvaEventObject<Event>) => void;
	hoverPosition: { x: number; y: number } | null;
}

export interface ISelectionBox {
	x: number;
	y: number;
	width: number;
	height: number;
}

export interface ISelectionBoxConfig {
	selectionBox: ISelectionBox | null;
	handleMouseDown: (e: KonvaEventObject<MouseEvent>) => void;
	handleMouseMoveSelectionBox: (e: KonvaEventObject<MouseEvent>) => void;
	handleMouseUp: (e: KonvaEventObject<MouseEvent>) => void;
}

export interface IShapeInfo {
	name: string;
	width: number;
	height: number;
	tempId: string;
}

export interface IShapeHoverConfig {
	hoveredShape: {
		item: IShapeInfo | null;
		x: number;
		y: number;
	};
	handleShapeMouseEnter: (item: IShapeInfo, position: { x: number; y: number }) => void;
	handleShapeMouseLeave: () => void;
	clearHoveredShape: () => void;
}
