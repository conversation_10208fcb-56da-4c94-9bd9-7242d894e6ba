import { IItem } from '../item.type';

export interface ElementsLayerItemProps {
	item: IItem;
	isSelected: boolean;
	// editingId: string | null;
	// editingName: string;
	onSelect: ({
		itemId,
		modifiers,
	}: {
		itemId: string;
		modifiers: {
			shiftKey: boolean;
			ctrlKey: boolean;
		};
	}) => void;
	// onStartEdit: (id: string, name: string) => void;
	// onCommitEdit: (id: string, newName: string) => void;
	// onCancelEdit: () => void;
	// onChangeEdit: (value: string) => void;
}
