import { atom } from 'jotai';
import { itemsAtom } from '../../../../shared/states/items/object-item.state';
import { normalizeUndoSnapshot } from '../../lib/history/normalized-snapshot';
import { IItem } from '../../types/item.type';

export interface IHistoryItems {
	past: IItem[][];
	present: IItem[];
	future: IItem[][];
}

export const itemsHistoryAtom = atom<IHistoryItems>({
	past: [],
	present: [],
	future: [],
});

export const undoAtom = atom(null, (get, set) => {
	const history = get(itemsHistoryAtom);

	if (history.past.length === 0) return;
	const previousSnapshot = history.past[history.past.length - 1];
	const currentSnapshot = history.present;
	const nextSnapshot = history.future[0];

	const adjustedSnapshot = normalizeUndoSnapshot({
		previousSnapshot,
		currentSnapshot,
		nextSnapshot,
	});

	const newPast = history.past.slice(0, -1);
	const newFuture = [...history.future, currentSnapshot];

	set(itemsHistoryAtom, {
		past: newPast,
		present: adjustedSnapshot,
		future: newFuture,
	});
	set(itemsAtom, adjustedSnapshot);
});
