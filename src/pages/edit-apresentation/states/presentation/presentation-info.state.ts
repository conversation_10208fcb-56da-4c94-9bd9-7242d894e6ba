import { atom } from 'jotai';

export interface IPresentationInfo {
	id: string;
	title: string;
	createAt: string;
	updatedAt: string;
	description: string;
	width: number;
	height: number;
	elements: IElement[];
}

export interface IElement {
	id: string;
	createdAt: string;
	deteledAt: string;
	updatedAt: string;
	media: IMediaElement[];
	elementType: string;
	content: object;
	title: string;
	positionX: number;
	positionY: number;
	width: number;
	height: number;
	layer: number;
}

export interface IMediaElement {
	id: string;
	createdAt: string;
	updatedAt: string;
	pathConfigurationId: string;
	filename: string;
	originalFilename: string;
	size: number;
	mimetype: string;
	url: string;
	mediaTempToken: string;
	mediaTempTokenExpireAt: number;
}

export const presentationInfoAtom = atom<IPresentationInfo | null>(null);
