export interface IWeatherEditorProps {
	content?: IWeather;
	onChange: (content: IWeather) => void;
}

export interface IWeather {
	labelTwo: string;
	iconType: string;
	forecastMode: string;
	forecastDays: number;
	themeOptions: {
		theme: string;
		customTheme: {
			background: string;
			accent: string;
			text: string;
			highTemp: string;
			lowTemp: string;
			shadow: string;
			sunAndThunder: string;
			moon: string;
			cloud: string;
			cloudFill: string;
			rain: string;
			snow: string;
		};
	};
}

export const FLAT_THEME = {
	ORIGINAL: 'original',
	CLARO: 'light',
	LARANJA: 'orange',
	CINZA: 'cinza',
	PRETO: 'black',
	DESERTO: 'desert',
	ROSA: 'candy',
	BEGE: 'beige',
	SALMÃO: 'salmon',
} as const;

export const GRADIENT_THEME = {
	CLEAR: 'clear',
	CÉU: 'sky',
	METÁLICO: 'metallic',
	RUBY: 'ruby',
	KIWI: 'kiwi',
} as const;

export const PATTERN_THEME = {
	HEXAGONOS: 'hexellence',
	'MADERIA AZULEJADA': 'tile_wood',
	'COURO BRANCO': 'white_leather',
	'MESA DE BILHAR': 'pool_table',
	'CINZA ALEATÓRIO': 'random_grey',
} as const;

export const IMAGE_THEME = {
	'CÉU RETRO': 'retro-sky',
	MARINHO: 'marine',
	MONTANHAS: 'mountains',
	'MONTANHAS AZUIS': 'blue-mountains',
	TRAVESSEIROS: 'pillows',
	GRAMA: 'grass',
	'FOLHAS DE OUTONO': 'fall-leaves',
};

export const WEATHER_CONDITIONS_THEME = {
	'CLIMA ATUAL': 'weather_one',
};

export const WEATHER_DISPLAY_THEME = {
	CUSTOM: 'custom',
	...FLAT_THEME,
	...GRADIENT_THEME,
	...PATTERN_THEME,
	...IMAGE_THEME,
	...WEATHER_CONDITIONS_THEME,
} as const;

export const ICON_TYPES = {
	CLIMACONS: 'Climacons',
	CLIMACONS_ANIMATED: 'Climacons Animated',
	ICON_VAULT: 'IconVault',
} as const;
