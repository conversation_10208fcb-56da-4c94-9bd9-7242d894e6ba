import { Label } from '@/components/shadcnui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/shadcnui/select';

import { DEFAULT_WEATHER_SETTINGS } from '@/pages/edit-apresentation/data/base-format-elements/weather';

import { useForm<PERSON>andler } from '@/pages/edit-apresentation/hooks/utils/form-state.hook';
import { SelectItemProperties } from '../../shared/common';
import TextInput from '../../shared/common/ui/input-item-with-icon';
import {
	FLAT_THEME,
	GRADIENT_THEME,
	IMAGE_THEME,
	IWeather,
	IWeatherEditorProps,
	PATTERN_THEME,
	WEATHER_CONDITIONS_THEME,
	WEATHER_DISPLAY_THEME,
} from './weather-editor.type';

const DEFAULT_CUSTOM_THEME: NonNullable<IWeather['themeOptions']['customTheme']> = {
	background: '',
	accent: '',
	text: '',
	highTemp: '',
	lowTemp: '',
	shadow: '',
	sunAndThunder: '',
	moon: '',
	cloud: '',
	rain: '',
	snow: '',
	cloudFill: '',
};

interface CustomThemeEditorProps {
	customTheme: NonNullable<IWeather['themeOptions']['customTheme']> | null;
	onChange: (field: keyof NonNullable<IWeather['themeOptions']['customTheme']>, value: string) => void;
}

const CustomThemeEditor = ({ customTheme, onChange }: CustomThemeEditorProps) => {
	if (!customTheme) {
		return (
			<button
				className="rounded-md bg-blue-500 px-3 py-1 text-white"
				onClick={() => {
					(Object.keys(DEFAULT_CUSTOM_THEME) as (keyof typeof DEFAULT_CUSTOM_THEME)[]).forEach((key) => onChange(key, ''));
				}}
			>
				Customizar tema
			</button>
		);
	}

	const renderColorInput = (labelText: string, field: keyof typeof DEFAULT_CUSTOM_THEME) => (
		<div className="flex flex-col gap-1">
			<Label className="text-[11px] uppercase text-gray-50/40">{labelText}:</Label>
			<TextInput value={customTheme[field]} type="color" onChange={(e) => onChange(field, e.target.value)} />
		</div>
	);

	return (
		<div className="flex flex-col gap-2">
			<div className="flex flex-row gap-2">
				{renderColorInput('Background', 'background')}
				{renderColorInput('Cor destaque', 'accent')}
			</div>
			<div className="flex flex-row gap-2">
				{renderColorInput('Cor do texto', 'text')}
				{renderColorInput('Alta temp.', 'highTemp')}
			</div>
			<div className="flex flex-row gap-2">
				{renderColorInput('Baixa temp.', 'lowTemp')}
				{renderColorInput('Sombra', 'shadow')}
			</div>
			<div className="flex flex-row gap-2">
				{renderColorInput('Sol e trovão', 'sunAndThunder')}
				{renderColorInput('Lua', 'moon')}
			</div>
			<div className="flex flex-row gap-2">
				{renderColorInput('Nuvem', 'cloud')}
				{renderColorInput('Precipitação', 'rain')}
			</div>
			<div className="flex flex-row gap-2">
				{renderColorInput('Neve', 'snow')}
				{renderColorInput('Nuvem inteira', 'cloudFill')}
			</div>
		</div>
	);
};

const themeGroups = [
	{ label: 'Padrão', items: Object.entries(FLAT_THEME) },
	{ label: 'Gradiente', items: Object.entries(GRADIENT_THEME) },
	{ label: 'Pattern', items: Object.entries(PATTERN_THEME) },
	{ label: 'IMAGEM', items: Object.entries(IMAGE_THEME) },
	{ label: 'Condições Climáticas', items: Object.entries(WEATHER_CONDITIONS_THEME) },
];

export const WeatherEditor = ({ content, onChange }: IWeatherEditorProps) => {
	const { formData, updateField } = useFormHandler<IWeather>(DEFAULT_WEATHER_SETTINGS, content, onChange);

	const updateCustomTheme = (field: keyof NonNullable<IWeather['themeOptions']['customTheme']>, value: string) => {
		updateField('themeOptions', {
			...formData.themeOptions,
			customTheme: {
				...(formData.themeOptions.customTheme || DEFAULT_CUSTOM_THEME),
				[field]: value,
			},
		});
	};

	return (
		<div className="flex flex-col gap-2">
			<h2 className="text-xs uppercase">Tempo:</h2>

			<div className="flex flex-col">
				<Label className="text-[11px] uppercase text-gray-50/40">Titulo secundário:</Label>
				<TextInput value={formData.labelTwo} sizeUnit="text" onChange={(e) => updateField('labelTwo', e.target.value)} />
			</div>

			<div className="flex flex-col gap-2">
				<SelectItemProperties
					labelText="Tipo de ícone"
					value={formData.iconType}
					onValueChange={(value) => updateField('iconType', value as 'IconVault' | 'Climacons' | 'Climacons Animated')}
					items={[
						{ value: 'IconVault', label: 'IconVault' },
						{ value: 'Climacons', label: 'Climacons' },
						{ value: 'Climacons Animated', label: 'Climacons Animated' },
					]}
				/>
			</div>

			<div className="flex flex-row gap-2">
				<SelectItemProperties
					labelText="Modo:"
					value={formData.forecastMode}
					onValueChange={(value) => updateField('forecastMode', value as 'Current' | 'Forecast' | 'Both')}
					items={[
						{ value: 'Current', label: 'Atual' },
						{ value: 'Forecast', label: 'Previsão' },
						{ value: 'Both', label: 'Ambos' },
					]}
				/>
				<SelectItemProperties
					labelText="Dias de previsão"
					value={String(formData.forecastDays)}
					onValueChange={(value) => updateField('forecastDays', Number(value) as 7 | 5 | 3)}
					items={[
						{ value: '3', label: '3 dias' },
						{ value: '5', label: '5 dias' },
						{ value: '7', label: '7 dias' },
					]}
				/>
			</div>

			<div className="flex flex-col gap-2">
				<Label className="text-[11px] uppercase text-gray-50/40">Tema:</Label>
				<Select
					value={formData.themeOptions.theme}
					onValueChange={(value) => {
						updateField('themeOptions', {
							...formData.themeOptions,
							theme: value as keyof typeof WEATHER_DISPLAY_THEME,
						});
					}}
				>
					<SelectTrigger className="flex h-[24px] w-full rounded-md bg-gray-100/10">
						<SelectValue placeholder="selecione o tema" />
					</SelectTrigger>
					<SelectContent>
						<SelectGroup>
							<SelectItem value="custom">Personalizado</SelectItem>
						</SelectGroup>
						{themeGroups.map(({ label, items }) => (
							<SelectGroup key={label}>
								<SelectLabel className="text-[11px] uppercase text-gray-50/40">{label}</SelectLabel>
								{items.map(([key, value]) => (
									<SelectItem key={key} value={value}>
										{key}
									</SelectItem>
								))}
							</SelectGroup>
						))}
					</SelectContent>
				</Select>
			</div>
			{formData.themeOptions.theme === 'custom' && <CustomThemeEditor customTheme={formData.themeOptions.customTheme} onChange={updateCustomTheme} />}
		</div>
	);
};
