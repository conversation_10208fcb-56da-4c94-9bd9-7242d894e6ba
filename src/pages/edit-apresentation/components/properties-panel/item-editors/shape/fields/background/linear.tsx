import { useEffect, useState } from 'react';
import { Range, getTrackBackground } from 'react-range';
import { IGradientStop, ILinearGradientEditorProps } from '../../square-editor.types';

function AngleSlider({ angle, onAngleChange }: { angle: number; onAngleChange: (val: number) => void }) {
	return (
		<Range
			step={1}
			min={0}
			max={360}
			values={[angle]}
			onChange={(values) => onAngleChange(values[0])}
			renderTrack={({ props, children }) => (
				<div
					{...props}
					style={{
						height: '6px',
						borderRadius: '3px',
						background: getTrackBackground({
							values: [angle],
							colors: ['hsl(var(--primary))', 'rgb(249 250 251 / 0.2)'],
							min: 0,
							max: 360,
						}),
						alignSelf: 'center',
						cursor: 'pointer',
					}}
				>
					{children}
				</div>
			)}
			renderThumb={({ props }) => (
				<div
					{...props}
					key={props.key + '-thumb'}
					style={{
						height: '16px',
						width: '16px',
						borderRadius: '50%',
						backgroundColor: 'rgb(249 250 251 / 0.9)',
						border: '2px solid hsl(var(--primary))',
						cursor: 'grab',
					}}
				/>
			)}
		/>
	);
}

function OffsetSlider({ offsets, onOffsetChange }: { offsets: number[]; onOffsetChange: (vals: number[]) => void }) {
	return (
		<div className="flex w-full">
			<Range
				step={1}
				min={0}
				max={100}
				allowOverlap={false}
				values={offsets.slice().sort((a, b) => a - b)}
				onChange={onOffsetChange}
				renderTrack={({ props, children }) => (
					<div
						{...props}
						style={{
							height: '6px',
							borderRadius: '3px',
							background: getTrackBackground({
								values: offsets,
								colors: ['rgb(249 250 251 / 0.2)', 'hsl(var(--primary))', 'rgb(249 250 251 / 0.2)'],
								min: 0,
								max: 100,
							}),
							alignSelf: 'center',

							cursor: 'pointer',
							flex: 1,
						}}
					>
						{children}
					</div>
				)}
				renderThumb={({ props }) => (
					<div
						{...props}
						key={props.key}
						style={{
							...props.style,
							height: '16px',
							width: '16px',
							borderRadius: '50%',
							backgroundColor: 'rgb(249 250 251 / 0.9)',
							border: '2px solid hsl(var(--primary))',
							cursor: 'grab',
						}}
					/>
				)}
			/>
		</div>
	);
}

function ColorPicker({ label, color, onColorChange }: { readonly label: string; readonly color: string; readonly onColorChange: (color: string) => void }) {
	const [localColor, setLocalColor] = useState(color);

	useEffect(() => {
		const timeoutId = setTimeout(() => {
			if (localColor !== color) {
				onColorChange(localColor);
			}
		}, 100);

		return () => clearTimeout(timeoutId);
	}, [localColor, color, onColorChange]);

	return (
		<div className="max-w-sm rounded-xl bg-background/10">
			<span className="mb-1 text-xs text-[#6c6c6c]">{label}</span>
			<div className="relative mt-1 overflow-hidden rounded-md border border-gray-800/30 shadow-sm transition-all hover:ring-1 hover:ring-primary/50">
				<input
					id={`${label}-color`}
					type="color"
					value={localColor}
					onChange={(e) => setLocalColor(e.target.value)}
					className="absolute inset-0 h-10 w-10 cursor-pointer opacity-0"
				/>
				<div className="flex h-10 w-10 items-center justify-center rounded" style={{ backgroundColor: localColor }}>
					<button
						type="button"
						className="font-mono text-[10px] text-white opacity-0 mix-blend-difference transition-opacity hover:opacity-80"
						onClick={() => {
							const colorInput = document.getElementById(`${label}-color`) as HTMLInputElement;
							colorInput.click();
						}}
						style={{ background: 'none', border: 'none', cursor: 'pointer' }}
					>
						{localColor.substring(1, 7)}
					</button>
				</div>
			</div>
		</div>
	);
}

export function LinearGradientEditor({ gradientOptions, onChange }: ILinearGradientEditorProps) {
	const { angle, stops } = gradientOptions;
	const [stop1, stop2] = stops;
	const offsets = [stop1.offset, stop2.offset];
	const gradientCSS = `linear-gradient(${angle}deg, ${stop1.color} ${stop1.offset}%, ${stop2.color} ${stop2.offset}%)`;

	const handleAngleChange = (newAngle: number) => {
		onChange({ ...gradientOptions, angle: newAngle });
	};

	const handleColorChange = (index: number, newColor: string) => {
		const newStops = [...stops] as [IGradientStop, IGradientStop];
		newStops[index] = { ...newStops[index], color: newColor };
		onChange({ ...gradientOptions, stops: newStops });
	};

	const handleRangeChange = (values: number[]) => {
		const newStops = [...stops] as [IGradientStop, IGradientStop];
		newStops[0] = { ...newStops[0], offset: values[0] };
		newStops[1] = { ...newStops[1], offset: values[1] };
		onChange({ ...gradientOptions, stops: newStops });
	};

	return (
		<div className="max-w-sm rounded-xl border bg-background/30 p-3">
			<div className="mb-3 h-24 w-full rounded-lg" style={{ background: gradientCSS }} />
			<div className="mb-4">
				<AngleSlider angle={angle} onAngleChange={handleAngleChange} />
			</div>
			<div className="flex items-center gap-4">
				<ColorPicker label="Cor 1:" color={stop1.color} onColorChange={(val) => handleColorChange(0, val)} />
				<div className="bg-b mt-4 flex-1">
					<OffsetSlider offsets={offsets} onOffsetChange={handleRangeChange} />
				</div>
				<ColorPicker label="Cor 2:" color={stop2.color} onColorChange={(val) => handleColorChange(1, val)} />
			</div>
		</div>
	);
}
