import { useEffect, useState } from 'react';
import { Range, getTrackBackground } from 'react-range';
import { IGradientStop, IRadialGradientEditorProps, IRadialGradientOptions, ShapeSelectorProps } from '../../square-editor.types';

function ShapeSelector({ shape, onChange }: ShapeSelectorProps) {
	const options = [
		{ label: 'Círculo', value: 'circle' },
		{ label: 'Elipse', value: 'ellipse' },
	];

	return (
		<div className="mb-4 flex items-center gap-4">
			<span className="mb-1 text-xs text-[#6c6c6c]">Forma: </span>
			<div className="flex gap-2">
				{options.map((option) => (
					<button
						key={option.value}
						onClick={() => onChange(option.value as 'circle' | 'ellipse')}
						className={`rounded px-2 py-1 text-xs transition-all ${
							shape === option.value ? 'bg-primary text-primary-foreground' : 'bg-muted text-white hover:bg-primary/20'
						}`}
					>
						{option.label}
					</button>
				))}
			</div>
		</div>
	);
}

interface SliderControlProps {
	value: number;
	onChange: (value: number) => void;
	min?: number;
	max?: number;
	step?: number;
}

function SliderControl({ value, onChange, min = 0, max = 100, step = 1 }: SliderControlProps) {
	return (
		<Range
			step={step}
			values={[value]}
			onChange={(values) => onChange(values[0])}
			renderTrack={({ props, children }) => (
				<div
					{...props}
					style={{
						...props.style,
						height: '6px',
						width: '100%',
						background: getTrackBackground({
							values: [value],
							colors: ['hsl(var(--primary))', 'rgb(249 250 251 / 0.2)'],
							min,
							max,
						}),
						borderRadius: '4px',
					}}
				>
					{children}
				</div>
			)}
			renderThumb={({ props }) => {
				const { key, ...restProps } = props;
				return (
					<div
						key={key}
						{...restProps}
						style={{
							...restProps.style,
							height: '16px',
							width: '16px',
							borderRadius: '50%',
							backgroundColor: 'rgb(249 250 251 / 0.9)',
							border: '2px solid hsl(var(--primary))',
							cursor: 'grab',
						}}
					/>
				);
			}}
		/>
	);
}

interface StopControlProps {
	stop: IGradientStop;
	label: string;
	onColorChange: (newColor: string) => void;
	onOffsetChange: (newOffset: number) => void;
}

function StopControl({ stop, label, onColorChange, onOffsetChange }: StopControlProps) {
	const [localColor, setLocalColor] = useState(stop.color);

	useEffect(() => {
		const timeoutId = setTimeout(() => {
			if (localColor !== stop.color) {
				onColorChange(localColor);
			}
		}, 100);

		return () => clearTimeout(timeoutId);
	}, [localColor, stop.color, onColorChange]);

	return (
		<div className="flex w-1/2 flex-col items-center gap-2 px-2">
			<span className="mb-1 text-xs uppercase text-[#6c6c6c]">{label}:</span>
			<div className="relative mt-1 overflow-hidden rounded-md border border-gray-800/30 shadow-sm transition-all hover:ring-1 hover:ring-primary/50">
				<input
					type="color"
					id={`${label}-color-radial`}
					value={localColor}
					onChange={(e) => setLocalColor(e.target.value)}
					className="absolute inset-0 h-10 w-10 cursor-pointer opacity-0"
				/>
				<div className="flex h-10 w-10 cursor-pointer items-center justify-center rounded" style={{ backgroundColor: localColor }}>
					<span
						className="font-mono text-[10px] text-white opacity-0 mix-blend-difference transition-opacity hover:opacity-80"
						onClick={() => {
							const colorInput = document.getElementById(`${label}-color-radial`) as HTMLInputElement;
							colorInput.click();
						}}
					>
						{localColor.substring(1, 7)}
					</span>
				</div>
			</div>
			<SliderControl value={stop.offset} onChange={onOffsetChange} />
			<span className="text-[10px] text-gray-50/60">{stop.offset}%</span>
		</div>
	);
}

export function RadialGradientEditor({ gradientOptions, onChange }: IRadialGradientEditorProps) {
	const defaultOptions: IRadialGradientOptions = {
		shape: 'circle',
		position: { x: 50, y: 50 },
		stops: [
			{ color: '#000000', offset: 0 },
			{ color: '#ffffff', offset: 100 },
		],
	};

	const mergedOptions: IRadialGradientOptions = {
		...defaultOptions,
		...gradientOptions,
		position: { ...defaultOptions.position, ...gradientOptions.position },
		stops: gradientOptions.stops ?? defaultOptions.stops,
	};

	const { shape, position, stops } = mergedOptions;

	const gradientCSS = `radial-gradient(${shape} at ${position.x}% ${position.y}%, ${stops[0].color} ${stops[0].offset}%, ${stops[1].color} ${stops[1].offset}%)`;

	const updateOptions = (newValues: Partial<IRadialGradientOptions>) => {
		onChange({ ...mergedOptions, ...newValues });
	};

	const handleShapeChange = (newShape: 'circle' | 'ellipse') => {
		updateOptions({ shape: newShape });
	};

	const handlePositionChange = (axis: 'x' | 'y', value: number) => {
		updateOptions({ position: { ...position, [axis]: value } });
	};

	const handleStopColorChange = (index: 0 | 1, newColor: string) => {
		const newStops = [...stops] as [IGradientStop, IGradientStop];
		newStops[index] = { ...newStops[index], color: newColor };
		updateOptions({ stops: newStops });
	};

	const handleStopOffsetChange = (index: 0 | 1, newOffset: number) => {
		const newStops = [...stops] as [IGradientStop, IGradientStop];
		newStops[index] = { ...newStops[index], offset: newOffset };
		updateOptions({ stops: newStops });
	};

	return (
		<div className="max-w-sm rounded-xl bg-background/10">
			<div className="mb-3 h-24 w-full rounded-lg" style={{ background: gradientCSS }} />

			<ShapeSelector shape={shape} onChange={handleShapeChange} />

			<div className="mb-4">
				<label className="mb-1 block text-[11px] font-medium uppercase tracking-wide text-gray-50/60">Posição Horizontal: {position.x}%</label>
				<SliderControl value={position.x} onChange={(value) => handlePositionChange('x', value)} />
			</div>

			<div className="mb-4">
				<label className="mb-1 block text-[11px] font-medium uppercase tracking-wide text-gray-50/60">Posição Vertical: {position.y}%</label>
				<SliderControl value={position.y} onChange={(value) => handlePositionChange('y', value)} />
			</div>

			<div className="flex w-full items-center justify-between gap-4">
				<StopControl
					stop={stops[0]}
					label="cor 1"
					onColorChange={(color) => handleStopColorChange(0, color)}
					onOffsetChange={(offset) => handleStopOffsetChange(0, offset)}
				/>
				<StopControl
					stop={stops[1]}
					label="cor 2"
					onColorChange={(color) => handleStopColorChange(1, color)}
					onOffsetChange={(offset) => handleStopOffsetChange(1, offset)}
				/>
			</div>
		</div>
	);
}
