import { useCallback } from 'react';
import TextInput from '../../../../shared/common/ui/input-item-with-icon';

interface IOpaqueBackgroundEditorProps {
	color: string | undefined;
	onChange: (color: string) => void;
}

export function OpaqueBackgroundEditor({ color, onChange }: IOpaqueBackgroundEditorProps) {
	const handleColorChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			onChange(event.target.value);
		},
		[onChange],
	);

	return (
		<div>
			<span className="mb-1 text-xs text-[#6c6c6c]">Cor:</span>
			<TextInput type="color" className="text-[11px] uppercase text-gray-50/40" value={color ?? ''} onChange={handleColorChange} />
		</div>
	);
}
