import { DEFAULT_SQUARE_STYLE } from '@/pages/edit-apresentation/data/base-format-elements/square';

import { useFormHandler } from '@/pages/edit-apresentation/hooks/utils/form-state.hook';
import { Separator } from '@radix-ui/react-select';
import { EditorCategoryTitle } from '../../core/components/categorory-title';
import { InputGroup } from '../../core/components/input-group';
import { InputWithLabel } from '../../shared/common';
import { BorderEditor } from '../../shared/common/property-editors/border';
import { SquareBackgroundOptions } from './fields/background/background';
import { IRectangleEditorProps, IShapeSquareStyle } from './square-editor.types';

export const RectangleEditor = ({ content, onChange, id }: IRectangleEditorProps) => {
	const { formData, updateField } = useFormHandler<IShapeSquareStyle>(DEFAULT_SQUARE_STYLE, content, onChange);

	return (
		<section key={id} aria-labelledby="rectangle-editor-title" className="flex flex-col gap-2">
			<EditorCategoryTitle title="Quadrado" className="text-xs uppercase" />
			<form className="w-full flex-col">
				<InputGroup ariaLabel="Propriedades do Quadrado">
					<InputWithLabel
						label="Opacidade"
						type="number"
						sizeUnit="%"
						value={formData.opacity}
						onChange={(e) => updateField('opacity', Number(e.target.value))}
					/>
				</InputGroup>
				<InputGroup ariaLabel="Configurações da borda">
					<BorderEditor
						border={formData.border}
						onChange={(field, value) => {
							updateField('border', { ...formData.border, [field]: value });
						}}
					/>
				</InputGroup>
				<Separator className="my-2 h-[0.1px] bg-gray-50/10" />
				<InputGroup ariaLabel="Cor de fundo">
					<SquareBackgroundOptions formData={formData} setField={updateField} />
				</InputGroup>
			</form>
		</section>
	);
};
