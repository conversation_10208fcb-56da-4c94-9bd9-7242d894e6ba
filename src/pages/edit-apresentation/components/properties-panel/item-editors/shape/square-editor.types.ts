import { IBorder } from '@/pages/edit-apresentation/types/elements/border-type';

export type TBackgroundStyleType = 'opaque' | 'linearGradient' | 'radialGradient';
export type TGradientShape = 'circle' | 'ellipse';

export interface IGradientStop {
	color: string;
	offset: number;
}

// export interface IBorderStyle {
// 	borderRadius: number;
// 	borderStyle: string;
// 	borderColor: string;
// 	borderWidth: number;
// }

export interface IShadowStyle {
	boxShadow: string;
}

export interface IGradientBase {
	stops: IGradientStop[];
}

export interface ILinearGradientOptions extends IGradientBase {
	angle: number;
}

export interface IRadialGradientOptions extends IGradientBase {
	shape: TGradientShape;
	position: { x: number; y: number };
}

export type GradientOptions = { type: 'linearGradient'; options: ILinearGradientOptions } | { type: 'radialGradient'; options: IRadialGradientOptions };

export interface IOpaqueBackgroundStyle {
	backgroundStyle: 'opaque';
	backgroundColor: string;
	opacity: number;
}

export interface IGradientBackgroundStyle {
	backgroundStyle: 'linearGradient' | 'radialGradient';
	gradientOptions: ILinearGradientOptions | IRadialGradientOptions;
	opacity: number;
}

export type BackgroundStyle = IOpaqueBackgroundStyle | IGradientBackgroundStyle;

export interface IShapeSquareStyle extends IShadowStyle {
	backgroundStyle: TBackgroundStyleType;
	opacity: number;
	border: IBorder;
	gradientOptions?: {
		angle?: number;
		stops?: { color: string; offset: number }[];
		position?: { x: number; y: number };
		shape?: string;
	};
	backgroundColor?: string;
}

export interface IRectangleEditorProps {
	content?: IShapeSquareStyle;
	onChange: (json: IShapeSquareStyle) => void;
	id: string;
}

export interface ILinearGradientEditorProps {
	gradientOptions: ILinearGradientOptions;
	onChange: (gradientOptions: ILinearGradientOptions) => void;
}

export interface IRadialGradientEditorProps {
	gradientOptions: IRadialGradientOptions;
	onChange: (newOptions: IRadialGradientOptions) => void;
}

export interface ShapeSelectorProps {
	shape: TGradientShape;
	onChange: (newShape: TGradientShape) => void;
}
