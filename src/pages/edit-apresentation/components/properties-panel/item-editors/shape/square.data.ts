import { IShapeSquareStyle } from './square-editor.types';

export const DEFAULT_RECTANGLE_VALUE: IShapeSquareStyle = {
	backgroundColor: '#ffffff',
	backgroundStyle: 'opaque',
	opacity: 100,

	border: {
		radius: 0,
		style: 'none',
		color: '#000000',
		width: 0,
	},
	boxShadow: '',
};

export const BORDER_STYLES = [
	{ value: 'none', label: 'Sem Borda' },
	{ value: 'solid', label: '━━━━━━━' },
	{ value: 'dashed', label: '− − − − −' },
	{ value: 'dotted', label: '• • • • •' },
	{ value: 'double', label: '═══════' },
];

export const BACKGROUND_STYLES = [
	{ value: 'opaque', label: 'Opaco' },
	{ value: 'linearGradient', label: 'Gradiente' },
	{ value: 'radialGradient', label: 'Radial' },
];
