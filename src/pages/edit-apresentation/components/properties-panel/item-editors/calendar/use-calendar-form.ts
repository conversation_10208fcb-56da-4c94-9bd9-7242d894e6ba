import { DEFAULT_CALENDAR_OBJECT } from '@/pages/edit-apresentation/data/base-format-elements/calendar';
import { useState } from 'react';
import { ICalendarObject, MultipleCalendarProps, RangeCalendarProps, SingleCalendarProps } from './calendar.types';

export function useCalendarForm(content?: ICalendarObject, onChange?: (content: ICalendarObject) => void) {
	const [formData, setFormData] = useState<ICalendarObject>(() => {
		if (content) return content;
		return {
			...DEFAULT_CALENDAR_OBJECT,
			calendarMode: 'single',
		} as SingleCalendarProps;
	});

	const updateField = <K extends keyof ICalendarObject>(field: K, value: ICalendarObject[K] | Date | Date[] | { from: Date; to: Date }) => {
		let updatedData: ICalendarObject;

		if (field === 'calendarMode') {
			const baseProps = {
				opacity: formData.opacity,
				showOutsideDays: formData.showOutsideDays,
				backgroundColor: formData.backgroundColor,
				mainColor: formData.mainColor,
				secondaryColor: formData.secondaryColor,
				textColor: formData.textColor,
			};

			switch (value) {
				case 'single':
					updatedData = {
						...baseProps,
						calendarMode: 'single',
						selectedDate: undefined,
						useToday: false,
					} as SingleCalendarProps;
					break;
				case 'multiple':
					updatedData = {
						...baseProps,
						calendarMode: 'multiple',
						selectedDates: [],
						maxSelection: undefined,
						padding: formData.padding ?? 0,
						border: formData.border ?? 'none',
						cellSpacing: formData.cellSpacing ?? 0,
					} as MultipleCalendarProps;
					break;
				case 'range':
					updatedData = {
						...baseProps,
						calendarMode: 'range',
						dateRange: { from: new Date(), to: new Date() },
					} as RangeCalendarProps;
					break;
				default:
					updatedData = { ...formData, [field]: value } as ICalendarObject;
			}
		} else {
			updatedData = { ...formData, [field]: value } as ICalendarObject;
		}

		setFormData(updatedData);
		onChange?.(updatedData);
	};

	return {
		formData,
		updateField,
	};
}
