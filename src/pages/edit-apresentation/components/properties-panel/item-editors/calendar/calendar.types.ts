export interface IBaseCalendarProps {
	opacity: number;
	showOutsideDays: boolean;
	backgroundColor: string;
	mainColor: string;
	secondaryColor: string;
	textColor: string;
	numberOfMonths?: number;
	padding: {
		top: number;
		right: number;
		bottom: number;
		left: number;
	};
	border: {
		width: number;
		style: string;
		color: string;
		radius: number;
	};
	cellSpacing: number;
}

export interface SingleCalendarProps extends IBaseCalendarProps {
	calendarMode: 'single';
	selectedDate?: Date;
	useToday?: boolean;
}

export interface RangeCalendarProps extends IBaseCalendarProps {
	calendarMode: 'range';
	dateRange?: {
		from: Date;
		to: Date;
	};
}

export interface MultipleCalendarProps extends IBaseCalendarProps {
	calendarMode: 'multiple';
	selectedDates?: Date[];
	maxSelection?: number;
}

export type ICalendarObject = SingleCalendarProps | RangeCalendarProps | MultipleCalendarProps;

export type CalendarMode = ICalendarObject['calendarMode'];
export type CalendarField<T extends ICalendarObject> = T extends { [K in keyof T]: T[K] } ? T[keyof T] : never;
