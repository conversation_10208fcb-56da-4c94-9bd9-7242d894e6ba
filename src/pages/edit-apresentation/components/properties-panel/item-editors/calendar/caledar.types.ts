export interface IBaseCalendarProps {
	opacity: number;
	showOutsideDays: boolean;
	backgroundColor: string;
	mainColor: string;
	secondaryColor: string;
	textColor: string;
}

interface SingleCalendarProps extends IBaseCalendarProps {
	calendarMode: 'single';
	selectedDate?: Date;
	useToday?: boolean;
}

interface RangeCalendarProps extends IBaseCalendarProps {
	calendarMode: 'range';
	dateRange?: {
		from: Date;
		to: Date;
	};
}

interface MultipleCalendarProps extends IBaseCalendarProps {
	calendarMode: 'multiple';
	selectedDates?: Date[];
	maxSelection?: number;
}

export type ICalendarObject = SingleCalendarProps | RangeCalendarProps | MultipleCalendarProps;
