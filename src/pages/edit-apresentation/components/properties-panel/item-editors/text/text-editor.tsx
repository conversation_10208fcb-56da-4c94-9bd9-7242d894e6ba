import { Label } from '@/components/shadcnui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/shadcnui/select';
import { useCallback, useEffect, useState } from 'react';
import { InputWithLabel, PropertiesCardContainer } from '../../shared/common';
export interface ITextElement {
	fontSize: number;
	fontFamily: string;
	fontWeight: string;
	fontStyle: string;
	textDecoration: string;
	textAlign: string;
	color: string;
	backgroundColor: string;
	opacity: number;
	text: string;
}

export interface TextEditorProps {
	content?: object;
	onChange: (content: object) => void;
}

export const TextEditorProperties = ({ content, onChange }: TextEditorProps) => {
	const [textElement, setTextElement] = useState<ITextElement>({
		fontSize: 16,
		fontFamily: 'Arial',
		fontWeight: 'normal',
		fontStyle: 'normal',
		textDecoration: 'none',
		textAlign: 'left',
		color: '#000000',
		backgroundColor: 'transparent',
		opacity: 100,
		text: '',
	});

	useEffect(() => {
		if (!content) return;

		if (typeof content === 'string') {
			try {
				const parsed = JSON.parse(content) as Partial<ITextElement>;
				setTextElement((prev) => ({ ...prev, ...parsed }));
			} catch {
				setTextElement((prev) => ({ ...prev, text: content }));
			}
		} else {
			const contentObj = content as Partial<ITextElement>;
			setTextElement((prev) => ({
				...prev,
				...contentObj,
				text: contentObj.text ?? prev.text,
			}));
		}
	}, [content]);

	const handleChange = useCallback(
		<K extends keyof ITextElement>(field: K, value: ITextElement[K]) =>
			setTextElement((prev) => {
				const updated = { ...prev, [field]: value };
				onChange(updated);
				return updated;
			}),
		[onChange],
	);

	const fontFamilies = [
		{ label: 'Arial', value: 'Arial' },
		{ label: 'Times New Roman', value: 'Times New Roman' },
		{ label: 'Courier New', value: 'Courier New' },
		{ label: 'Georgia', value: 'Georgia' },
		{ label: 'Verdana', value: 'Verdana' },
	];

	return (
		<PropertiesCardContainer title="Texto">
			<div className="mb-4">
				<Label className="text-[11px] uppercase text-gray-50/40">Conteúdo</Label>
				<textarea
					value={textElement.text}
					className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
					onChange={(e) => handleChange('text', e.target.value)}
				/>
			</div>

			<div className="mb-4">
				<Label className="text-[11px] uppercase text-gray-50/40">Família da Fonte</Label>
				<Select value={textElement.fontFamily} onValueChange={(value) => handleChange('fontFamily', value)}>
					<SelectTrigger className="h-[24px] bg-gray-100/10">
						<SelectValue placeholder="Selecione uma fonte" />
					</SelectTrigger>
					<SelectContent>
						{fontFamilies.map((font) => (
							<SelectItem key={font.value} value={font.value}>
								{font.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			<div className="mb-4 flex gap-2">
				<div className="w-1/2">
					<InputWithLabel
						label="Tamanho"
						type="number"
						value={textElement.fontSize}
						sizeUnit="px"
						onChange={(e) => handleChange('fontSize', Number(e.target.value))}
					/>
				</div>
				<div className="w-1/2">
					<InputWithLabel
						label="Opacidade"
						type="number"
						value={textElement.opacity}
						sizeUnit="%"
						onChange={(e) => handleChange('opacity', Number(e.target.value))}
					/>
				</div>
			</div>

			<div className="mb-4 flex gap-2">
				<div className="w-1/2">
					<InputWithLabel label="Cor do Texto" type="color" value={textElement.color} onChange={(e) => handleChange('color', e.target.value)} />
				</div>
				<div className="w-1/2">
					<InputWithLabel
						label="Cor de Fundo"
						type="color"
						value={textElement.backgroundColor}
						onChange={(e) => handleChange('backgroundColor', e.target.value)}
					/>
				</div>
			</div>
		</PropertiesCardContainer>
	);
};
