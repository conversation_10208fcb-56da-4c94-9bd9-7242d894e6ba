import { Label } from '@/components/shadcnui/label';
import { useFormHandler } from '@/pages/edit-apresentation/hooks/utils/form-state.hook';

import { Checkbox } from '@/components/shadcnui/checkbox';
import { DEFAULT_IMAGE_STYLE } from '@/pages/edit-apresentation/data/base-format-elements/image';
import { EditorCategoryTitle } from '../../core/components/categorory-title';
import { InputGroup } from '../../core/components/input-group';
import { InputWithLabel, MediaUploader } from '../../shared/common';
import { BorderEditor } from '../../shared/common/property-editors/border';
import { IImageEditorProps, IImagePreview } from './image-editor.types';

export const ImageEditor = ({ content, onChange, idElement }: IImageEditorProps) => {
	const { formData, updateField } = useFormHandler<IImagePreview>(DEFAULT_IMAGE_STYLE, content, onChange);

	return (
		<section aria-labelledby="image-editor-title" className="flex flex-col">
			<EditorCategoryTitle title="Imagem" className="text-xs uppercase" />
			<InputGroup columns={2} ariaLabel="Propriedades da Imagem">
				<InputWithLabel
					label="Opacidade"
					type="number"
					sizeUnit="%"
					value={formData.opacity}
					onChange={(e) => updateField('opacity', Number(e.target.value))}
				/>
				<InputWithLabel
					label="Background"
					type="color"
					value={formData.backgroundColor}
					onChange={(e) => {
						const newValue = e.target.value === '' ? '' : e.target.value;
						updateField('backgroundColor', newValue);
					}}
				/>
			</InputGroup>
			<InputGroup ariaLabel="Configurações da borda">
				<BorderEditor
					border={formData.border}
					onChange={(field, value) => {
						updateField('border', { ...formData.border, [field]: value });
					}}
				/>
			</InputGroup>
			<InputGroup ariaLabel="Aspect Ratio">
				<div className="flex w-full items-center gap-2">
					<Checkbox
						id="maintain-aspect-ratio"
						className="text-xs"
						checked={formData.maintainAspectRatio}
						onCheckedChange={(checked) => updateField('maintainAspectRatio', checked)}
						color="primary"
						aria-label="Manter Aspect Ratio"
					/>
					<Label htmlFor="maintain-aspect-ratio" className="text-[11px] text-gray-50/40">
						Manter proporção
					</Label>
				</div>
			</InputGroup>
			<MediaUploader onlyOne id={idElement} />
		</section>
	);
};
