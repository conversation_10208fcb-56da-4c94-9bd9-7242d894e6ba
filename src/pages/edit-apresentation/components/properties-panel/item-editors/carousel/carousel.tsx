import React from 'react';

import { EditorCategoryTitle } from '../../core/components/categorory-title';
import { InputGroup } from '../../core/components/input-group';
import { InputWithLabel, MediaUploader, SelectItemProperties } from '../../shared/common';
import { BorderEditor } from '../../shared/common/property-editors/border';
import { useCarouselForm } from './carousel-form.hook';
import { ICarouselObjectFormProps } from './corousel.types';

export const CarouselObjectForm: React.FC<ICarouselObjectFormProps> = ({ content, onChange, idElement }) => {
	const { carouselState, updateCarouselField } = useCarouselForm(content, onChange);

	return (
		<section aria-labelledby="carousel-editor" className="flex flex-col gap-2">
			<EditorCategoryTitle title="Carrossel" className="text-xs uppercase" />

			<form
				className="w-full flex-col"
				onSubmit={(e) => {
					e.preventDefault();
				}}
			>
				<InputGroup ariaLabel="Tipo de carrousel">
					<SelectItemProperties
						value={carouselState.animationEasing ?? 'ease-in-out'}
						onValueChange={(value) => updateCarouselField('animationEasing', value)}
						items={[
							{ label: 'Início e fim suaves (ease-in-out)', value: 'ease-in-out' },
							{ label: 'Linear (sem aceleração)', value: 'linear' },
							{ label: 'Aceleração no início (ease-in)', value: 'ease-in' },
							{ label: 'Desaceleração no final (ease-out)', value: 'ease-out' },
						]}
						labelText="Animação:"
					/>
				</InputGroup>

				<InputGroup columns={2} ariaLabel="Propriedades do Carrossel">
					<InputWithLabel
						label="Tempo/slide"
						type="number"
						sizeUnit="s"
						value={carouselState.timePerSlide}
						onChange={(e) => updateCarouselField('timePerSlide', Number(e.target.value))}
					/>
					<SelectItemProperties
						value={carouselState.scrollDirection ?? 'normal'}
						onValueChange={(value) => updateCarouselField('scrollDirection', value as 'normal' | 'reverse' | 'up' | 'down')}
						items={[
							{ label: 'Normal ←', value: 'normal' },
							{ label: 'Reverso →', value: 'reverse' },
							{ label: 'Para cima ↑', value: 'up' },
							{ label: 'Para baixo ↓', value: 'down' },
						]}
						labelText="Rolagem:"
					/>
				</InputGroup>

				<InputGroup columns={2} ariaLabel="Propriedades do Carrossel 2">
					<InputWithLabel
						label="Duração/ani..."
						type="number"
						sizeUnit="ms"
						value={carouselState.animationDuration ?? 0}
						onChange={(e) => updateCarouselField('animationDuration', Number(e.target.value))}
					/>
					<InputWithLabel
						label="Opacidade"
						type="number"
						sizeUnit="%"
						value={carouselState.opacity}
						onChange={(e) => updateCarouselField('opacity', Number(e.target.value))}
					/>
				</InputGroup>

				<InputGroup ariaLabel="Propriedades do Carrossel 3">
					<BorderEditor
						border={carouselState.border}
						onChange={(field, value) =>
							updateCarouselField('border', {
								...carouselState.border,
								[field]: value,
							})
						}
					/>
				</InputGroup>
			</form>

			<MediaUploader id={idElement} />
		</section>
	);
};

export default CarouselObjectForm;
