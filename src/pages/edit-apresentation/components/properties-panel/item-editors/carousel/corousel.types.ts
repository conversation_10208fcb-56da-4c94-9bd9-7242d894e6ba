import { IAddMediaResponse } from '@/pages/edit-apresentation/services/requests/elements/add-media';
import { IBorder } from '@/pages/edit-apresentation/types/elements/border-type';

export interface MediaUploaderProps {
	label?: string;
	id: string;
}

export interface MediaFile {
	file?: File;
	preview?: string | null;
}

export interface IMedia extends MediaFile {
	id: string;
	filename: string;
	mimetype: string;
	size: number;
	url: string;
}

export interface IAddMediaResponseWithMinitype extends IAddMediaResponse {
	minitype?: string;
}

export interface ICarouselObjectFormProps {
	idElement: string;
	content?: ICarouselObject;
	onChange: (json: ICarouselObject) => void;
}

export const ICarouselObjectShapeType = {
	IMAGE: 'IMAGE',
	TEXT: 'TEXT',
} as const;

export interface ICarouselObject {
	type: (typeof ICarouselObjectShapeType)[keyof typeof ICarouselObjectShapeType];
	backgroundColor: string;
	opacity: number;
	boxShadow: string;
	border: IBorder;
	mediaIdsOrders: string[];
	timePerSlide: number;
	scrollDirection?: 'normal' | 'reverse' | 'up' | 'down';
	animationDuration?: number;
	animationEasing?: string;
}
