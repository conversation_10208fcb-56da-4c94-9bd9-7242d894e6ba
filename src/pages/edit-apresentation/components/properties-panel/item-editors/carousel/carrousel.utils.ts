import { IMAGE_TYPES, VIDEO_TYPES } from '../../shared/common';
import { MediaFile } from './corousel.types';

const isValidMediaType = (file: File): boolean => {
	return [...IMAGE_TYPES, ...VIDEO_TYPES].includes(file.type);
};

const createMediaPreview = (file: File): MediaFile | null => {
	if (!isValidMediaType(file)) return null;
	const preview = IMAGE_TYPES.includes(file.type) ? URL.createObjectURL(file) : null;
	return { file, preview };
};

const filterValidFiles = (files: FileList): File[] => Array.from(files).filter(isValidMediaType);

export { createMediaPreview, filterValidFiles, isValidMediaType };
