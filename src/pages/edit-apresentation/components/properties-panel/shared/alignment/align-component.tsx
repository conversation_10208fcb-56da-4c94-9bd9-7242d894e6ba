import { motion } from 'framer-motion';
import React from 'react';

import { AutoTooltip } from '../common/ui/tooltip';
import { AlignmentType } from './align-elements';

type AlignmentButtonProps = {
	icon: React.ElementType;
	tooltip: string;
	alignmentType: AlignmentType;
	onAlign: (type: AlignmentType) => void;
};

export const AlignmentButton: React.FC<AlignmentButtonProps> = ({ icon, tooltip, alignmentType, onAlign }) => {
	const handleClick = () => {
		onAlign(alignmentType);
	};

	return (
		<AutoTooltip preferredPlacement="top" text={tooltip}>
			<motion.button
				onClick={handleClick}
				whileHover={{ scale: 1.05 }}
				whileTap={{ scale: 0.95 }}
				className="group relative rounded-md p-2 transition-colors hover:bg-blue-500/20 hover:text-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
				aria-label={tooltip}
			>
				<span className="flex h-4 w-4 items-center justify-center">{React.createElement(icon, { size: 16 })}</span>
			</motion.button>
		</AutoTooltip>
	);
};
