import { alignmentButtonsConfig } from '@/pages/edit-apresentation/data/properties-panel/align-items';
import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';
import { AlignmentButton } from './align-component';
import { alignElements, AlignmentType } from './align-elements';

export const AlignmentPanel = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedIds] = useAtom(selectedItemsIdsAtom);

	const selectedItems = items.filter((item) => selectedIds.includes(item.tempId));

	const handleAlignment = (alignment: AlignmentType) => {
		const updatedItems = alignElements(alignment, selectedItems);
		const newItems = items.map((item) => {
			const alignedItem = updatedItems.find((ai) => ai.tempId === item.tempId);
			return alignedItem ? alignedItem : item;
		});
		setItems(newItems);
	};

	return (
		<>
			{alignmentButtonsConfig.map((button) => (
				<AlignmentButton
					key={button.alignmentType}
					icon={button.icon}
					tooltip={button.tooltip}
					alignmentType={button.alignmentType}
					onAlign={handleAlignment}
				/>
			))}
		</>
	);
};
