import { IItem } from '@/pages/edit-apresentation/types/item.type';

export enum AlignmentType {
	LEFT = 'left',
	CENTER = 'center',
	RIGHT = 'right',
	TOP = 'top',
	MIDDLE = 'middle',
	BOTTOM = 'bottom',
}

export enum DistributionType {
	HORIZONTAL_CENTERS = 'horizontal_centers',
	VERTICAL_CENTERS = 'vertical_centers',
}

export enum OrganizationType {
	BRING_TO_FRONT = 'bring_to_front',
	SEND_TO_BACK = 'send_to_back',
	BRING_FORWARD = 'bring_forward',
	SEND_BACKWARD = 'send_backward',
	DUPLICATE = 'duplicate',
	DELETE = 'delete',
}

interface Boundaries {
	minX: number;
	maxX: number;
	minY: number;
	maxY: number;
}

const calculateBoundaries = (elements: IItem[]): Boundaries => {
	const positions = elements.map(({ position, size }) => ({
		x: position.x,
		y: position.y,
		width: size.width,
		height: size.height,
	}));

	const minX = Math.min(...positions.map((pos) => pos.x));
	const maxX = Math.max(...positions.map((pos) => pos.x + pos.width));
	const minY = Math.min(...positions.map((pos) => pos.y));
	const maxY = Math.max(...positions.map((pos) => pos.y + pos.height));

	return { minX, maxX, minY, maxY };
};

const alignHorizontally = (alignment: AlignmentType, item: IItem, { minX, maxX }: Boundaries): number => {
	switch (alignment) {
		case AlignmentType.LEFT:
			return minX;
		case AlignmentType.CENTER:
			return minX + (maxX - minX - item.size.width) / 2;
		case AlignmentType.RIGHT:
			return maxX - item.size.width;
		default:
			return item.position.x;
	}
};

const alignVertically = (alignment: AlignmentType, item: IItem, { minY, maxY }: Boundaries): number => {
	switch (alignment) {
		case AlignmentType.TOP:
			return minY;
		case AlignmentType.MIDDLE:
			return minY + (maxY - minY - item.size.height) / 2;
		case AlignmentType.BOTTOM:
			return maxY - item.size.height;
		default:
			return item.position.y;
	}
};

export const alignElements = (alignment: AlignmentType, elements: IItem[]): IItem[] => {
	if (elements.length === 0) return [];
	const boundaries = calculateBoundaries(elements);

	return elements.map((item) => {
		let newX = item.position.x;
		let newY = item.position.y;

		if ([AlignmentType.LEFT, AlignmentType.CENTER, AlignmentType.RIGHT].includes(alignment)) {
			newX = alignHorizontally(alignment, item, boundaries);
		} else if ([AlignmentType.TOP, AlignmentType.MIDDLE, AlignmentType.BOTTOM].includes(alignment)) {
			newY = alignVertically(alignment, item, boundaries);
		}

		return {
			...item,
			position: {
				x: Math.round(newX),
				y: Math.round(newY),
			},
		};
	});
};

export const distributeElements = (distribution: DistributionType, elements: IItem[]): IItem[] => {
	if (elements.length < 3) return elements;

	const sortedElements = [...elements];

	switch (distribution) {
		case DistributionType.HORIZONTAL_CENTERS:
			return distributeHorizontalCenters(sortedElements);
		case DistributionType.VERTICAL_CENTERS:
			return distributeVerticalCenters(sortedElements);
		default:
			return elements;
	}
};

const distributeHorizontalCenters = (elements: IItem[]): IItem[] => {
	const sorted = elements.sort((a, b) => a.position.x - b.position.x);
	const firstCenter = sorted[0].position.x + sorted[0].size.width / 2;
	const lastCenter = sorted[sorted.length - 1].position.x + sorted[sorted.length - 1].size.width / 2;
	const totalDistance = lastCenter - firstCenter;
	const spacing = totalDistance / (sorted.length - 1);

	return sorted.map((item, index) => {
		if (index === 0 || index === sorted.length - 1) return item;

		const newCenterX = firstCenter + spacing * index;
		const newX = newCenterX - item.size.width / 2;

		return {
			...item,
			position: {
				...item.position,
				x: Math.round(newX),
			},
		};
	});
};

const distributeVerticalCenters = (elements: IItem[]): IItem[] => {
	const sorted = elements.sort((a, b) => a.position.y - b.position.y);
	const firstCenter = sorted[0].position.y + sorted[0].size.height / 2;
	const lastCenter = sorted[sorted.length - 1].position.y + sorted[sorted.length - 1].size.height / 2;
	const totalDistance = lastCenter - firstCenter;
	const spacing = totalDistance / (sorted.length - 1);

	return sorted.map((item, index) => {
		if (index === 0 || index === sorted.length - 1) return item;

		const newCenterY = firstCenter + spacing * index;
		const newY = newCenterY - item.size.height / 2;

		return {
			...item,
			position: {
				...item.position,
				y: Math.round(newY),
			},
		};
	});
};

export const organizeElements = (
	organization: OrganizationType,
	selectedElements: IItem[],
	allElements: IItem[],
): { updatedElements: IItem[]; newElements?: IItem[] } => {
	switch (organization) {
		case OrganizationType.BRING_TO_FRONT:
			return { updatedElements: bringToFront(selectedElements, allElements) };
		case OrganizationType.SEND_TO_BACK:
			return { updatedElements: sendToBack(selectedElements, allElements) };
		case OrganizationType.BRING_FORWARD:
			return { updatedElements: bringForward(selectedElements, allElements) };
		case OrganizationType.SEND_BACKWARD:
			return { updatedElements: sendBackward(selectedElements, allElements) };
		case OrganizationType.DUPLICATE:
			return duplicateElements(selectedElements, allElements);
		case OrganizationType.DELETE:
			return { updatedElements: deleteElements(selectedElements, allElements) };
		default:
			return { updatedElements: allElements };
	}
};

const bringToFront = (selectedElements: IItem[], allElements: IItem[]): IItem[] => {
	const maxLayer = Math.max(...allElements.map((item) => item.layer));
	const selectedIds = selectedElements.map((item) => item.tempId);

	return allElements.map((item) => {
		if (selectedIds.includes(item.tempId)) {
			return { ...item, layer: maxLayer + 1 };
		}
		return item;
	});
};

const sendToBack = (selectedElements: IItem[], allElements: IItem[]): IItem[] => {
	const minLayer = Math.min(...allElements.map((item) => item.layer));
	const selectedIds = selectedElements.map((item) => item.tempId);

	return allElements.map((item) => {
		if (selectedIds.includes(item.tempId)) {
			return { ...item, layer: minLayer - 1 };
		}
		return item;
	});
};

const bringForward = (selectedElements: IItem[], allElements: IItem[]): IItem[] => {
	const selectedIds = selectedElements.map((item) => item.tempId);
	const selectedIdsSet = new Set(selectedIds);
	const sortedSelectedElements = selectedElements.sort((a, b) => a.layer - b.layer);

	const nonSelectedLayers = allElements
		.filter((item) => !selectedIdsSet.has(item.tempId))
		.map((item) => item.layer)
		.sort((a, b) => a - b);

	const updatedSelectedElements = sortedSelectedElements.map((selectedItem) => {
		const currentLayer = selectedItem.layer;
		const nextAvailableLayer = findNextAvailableLayer(currentLayer, nonSelectedLayers);

		return { ...selectedItem, layer: nextAvailableLayer };
	});

	const layerUpdates = new Map(updatedSelectedElements.map((item) => [item.tempId, item.layer]));

	return allElements.map((item) => {
		const newLayer = layerUpdates.get(item.tempId);
		return newLayer !== undefined ? { ...item, layer: newLayer } : item;
	});
};

const sendBackward = (selectedElements: IItem[], allElements: IItem[]): IItem[] => {
	const selectedIds = selectedElements.map((item) => item.tempId);
	const selectedIdsSet = new Set(selectedIds);

	const sortedSelectedElements = selectedElements.sort((a, b) => b.layer - a.layer);

	const nonSelectedLayers = allElements
		.filter((item) => !selectedIdsSet.has(item.tempId))
		.map((item) => item.layer)
		.sort((a, b) => a - b);

	const updatedSelectedElements = sortedSelectedElements.map((selectedItem) => {
		const currentLayer = selectedItem.layer;
		const previousAvailableLayer = findPreviousAvailableLayer(currentLayer, nonSelectedLayers);
		return { ...selectedItem, layer: previousAvailableLayer };
	});

	const layerUpdates = new Map(updatedSelectedElements.map((item) => [item.tempId, item.layer]));

	return allElements.map((item) => {
		const newLayer = layerUpdates.get(item.tempId);
		return newLayer !== undefined ? { ...item, layer: newLayer } : item;
	});
};

const duplicateElements = (selectedElements: IItem[], allElements: IItem[]): { updatedElements: IItem[]; newElements: IItem[] } => {
	const maxLayer = Math.max(...allElements.map((item) => item.layer));

	const newElements = selectedElements.map((item, index) => ({
		...item,
		tempId: `${item.tempId}_copy_${Date.now()}_${index}`,
		id: undefined,
		status: 'NEW' as const,
		layer: maxLayer + index + 1,
		position: {
			x: item.position.x,
			y: item.position.y,
		},
	}));

	return {
		updatedElements: allElements,
		newElements,
	};
};

const deleteElements = (selectedElements: IItem[], allElements: IItem[]): IItem[] => {
	const selectedIds = selectedElements.map((item) => item.tempId);
	return allElements.filter((item) => !selectedIds.includes(item.tempId));
};

// Funções auxiliares para melhor cálculo de layers
const findNextAvailableLayer = (currentLayer: number, nonSelectedLayers: number[]): number => {
	// Encontrar o próximo layer ocupado acima do current
	const nextOccupiedLayer = nonSelectedLayers.find((layer) => layer > currentLayer);

	if (nextOccupiedLayer === undefined) {
		// Não há layers ocupados acima, usar currentLayer + 1
		return currentLayer + 1;
	}

	// Se o próximo layer está imediatamente acima, usar esse layer
	// (o elemento atual vai "trocar de lugar" com o elemento acima)
	return nextOccupiedLayer;
};

const findPreviousAvailableLayer = (currentLayer: number, nonSelectedLayers: number[]): number => {
	// Encontrar o layer ocupado imediatamente abaixo do current
	const previousOccupiedLayer = nonSelectedLayers.filter((layer) => layer < currentLayer).sort((a, b) => b - a)[0]; // Ordenar decrescente e pegar o primeiro

	if (previousOccupiedLayer === undefined) {
		// Não há layers ocupados abaixo, usar 1 (layer mínimo)
		return Math.max(1, currentLayer - 1);
	}

	// Se o layer anterior está imediatamente abaixo, usar esse layer
	// (o elemento atual vai "trocar de lugar" com o elemento abaixo)
	return previousOccupiedLayer;
};
