import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/shadcnui/tooltip';
import React from 'react';

type Placement = 'top' | 'bottom' | 'left' | 'right';

interface AutoTooltipProps {
	text: string;
	children: React.ReactNode;
	preferredPlacement?: Placement;
	className?: string;
}

export const AutoTooltip: React.FC<AutoTooltipProps> = ({ text, children, preferredPlacement = 'top', className = '' }) => {
	const getSide = (): 'top' | 'right' | 'bottom' | 'left' => {
		switch (preferredPlacement) {
			case 'top':
				return 'top';
			case 'bottom':
				return 'bottom';
			case 'left':
				return 'left';
			case 'right':
				return 'right';
			default:
				return 'top';
		}
	};

	return (
		<TooltipProvider>
			<Tooltip delayDuration={300}>
				<TooltipTrigger asChild>
					<span className="inline-block cursor-pointer" tabIndex={0}>
						{children}
					</span>
				</TooltipTrigger>
				<TooltipContent side={getSide()} className={`border-[#333333] bg-[#252525] text-xs text-[#e0e0e0] ${className}`} sideOffset={5}>
					{text}
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
};
