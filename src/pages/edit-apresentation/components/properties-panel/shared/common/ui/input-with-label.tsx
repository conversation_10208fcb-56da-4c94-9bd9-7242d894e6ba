import React, { memo } from 'react';
import TextInput, { ISizeUnit } from './input-item-with-icon';

interface LabeledInputProps {
	readonly label: string;
	readonly value: number | string;
	readonly onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	readonly sizeUnit?: ISizeUnit;
	readonly type?: string;
	readonly readOnly?: boolean;
	readonly icon?: React.ReactNode;
	readonly className?: string;
}

export const InputWithLabel = memo(function InputWithLabel({
	label,
	value,
	onChange,
	sizeUnit,
	type = 'text',
	readOnly,
	icon,
	className = '',
}: LabeledInputProps) {
	return (
		<div className={`relative flex flex-col ${className}`}>
			<span className="mb-1 text-xs text-[#6c6c6c]">{label}</span>
			<TextInput icon={icon} type={type} value={value} sizeUnit={sizeUnit} onChange={onChange} readOnly={readOnly} />
		</div>
	);
});
