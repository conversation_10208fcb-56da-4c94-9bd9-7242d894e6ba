import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/shadcnui/select';
import React from 'react';

export interface SelectItemOption {
	label: string;
	value: string;
}

export interface SelectItemPropertiesProps {
	value: string;
	onValueChange: (value: string) => void;
	items: SelectItemOption[];
	placeholder?: string;
	labelText?: string;
	className?: string;
}

const SelectItemList: React.FC<{ items: SelectItemOption[] }> = ({ items }) => (
	<SelectGroup>
		{items.map((item) => (
			<SelectItem key={item.value} value={item.value} className="text-xs text-[#e0e0e0] data-[highlighted]:bg-[#333333] data-[highlighted]:text-white">
				{item.label}
			</SelectItem>
		))}
	</SelectGroup>
);

export const SelectItemProperties: React.FC<SelectItemPropertiesProps> = ({ value, onValueChange, items, placeholder, labelText, className = '' }) => {
	return (
		<div className={`flex flex-col ${className}`}>
			{labelText && <span className="mb-1 text-xs text-[#6c6c6c]">{labelText}</span>}
			<Select value={value} onValueChange={onValueChange}>
				<SelectTrigger
					className="flex h-7 w-full items-center rounded bg-[#3a3a3a] px-2 text-xs text-[#e0e0e0] outline-none"
					onPointerDown={(e) => e.stopPropagation()}
				>
					<SelectValue placeholder={placeholder} />
				</SelectTrigger>
				<SelectContent className="border-[#333333] bg-[#3a3a3a] text-[#e0e0e0]" position="popper">
					<SelectItemList items={items} />
				</SelectContent>
			</Select>
		</div>
	);
};
