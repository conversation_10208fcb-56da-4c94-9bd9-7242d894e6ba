import { FC } from 'react';

interface SectionHeaderProps {
	title: string;
	subtitle?: string;
	icon?: React.ElementType;
}

export const SectionHeader: FC<SectionHeaderProps> = ({ title, subtitle, icon: Icon }) => {
	return (
		<div className="flex items-center gap-2 mb-2">
			{Icon && (
				<div className="flex h-4 w-4 items-center justify-center text-[#8c8c8c]">
					<Icon size={14} />
				</div>
			)}
			<h3 className="text-xs font-semibold uppercase tracking-wide text-[#8c8c8c]">
				{title}
				{subtitle && (
					<span className="ml-1 text-[10px] font-normal normal-case text-[#666]">
						{subtitle}
					</span>
				)}
			</h3>
		</div>
	);
};
