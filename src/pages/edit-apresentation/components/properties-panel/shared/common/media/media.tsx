import { Label } from '@/components/shadcnui/label';
import { IAddMediaResponse, IMediaRequest } from '@/pages/edit-apresentation/services/requests/elements/add-media';
import { IMediaElement } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { ChangeEvent, useRef } from 'react';
import { MediaUploaderProps } from '../../../item-editors/carousel/corousel.types';
import { IMAGE_TYPES, VIDEO_TYPES } from './media-types.data';
import { useMediaUpload } from './media-upload.hook';
import { MediaPreview } from './preview';

type MediaUploaderPropsExtended = Readonly<MediaUploaderProps> & {
	readonly onlyOne?: boolean;
};

const mapMediaResponse = (media: IMediaRequest, mediasToken: string, mediaTempTokenExpireAt: number): IMediaElement => ({
	id: media.id,
	filename: media.filename,
	mimetype: media.mimetype,
	size: Number(media.size),
	url: media.url,
	createdAt: media.createdAt,
	updatedAt: media.updatedAt,
	pathConfigurationId: media.pathConfigurationId,
	originalFilename: media.filename,
	mediaTempToken: mediasToken,
	mediaTempTokenExpireAt: mediaTempTokenExpireAt,
});

export const MediaUploader: React.FC<MediaUploaderPropsExtended> = ({ label = 'Mídias', id, onlyOne = false }) => {
	const inputRef = useRef<HTMLInputElement>(null);
	const [items, setItems] = useAtom(itemsAtom);
	const currentItem = items.find((item) => item.id === id);

	const updateMedia = (updater: (media: IMediaElement[]) => IMediaElement[]): void => {
		setItems((items) => items.map((item) => (item.id === id ? { ...item, media: updater(item.media ?? []) } : item)));
	};

	const handleUploadSuccess = (media: IAddMediaResponse): void => {
		updateMedia((prev) => {
			const existingIds = new Set(prev.map((m) => m.id));
			const newMedias = media.medias
				.filter((itemMedia) => !existingIds.has(itemMedia.id))
				.map((itemMedia) => mapMediaResponse(itemMedia, media.mediaTempToken, media.mediaTempTokenExpireAt));
			return [...prev, ...newMedias];
		});
	};

	const handleDeletedSuccess = (deletedIds: ReadonlyArray<string>): void => updateMedia((prev) => prev.filter((media) => !deletedIds.includes(media.id)));

	const { uploadFiles } = useMediaUpload({ id, onUploadSuccess: handleUploadSuccess });

	const handleFileChange = (e: ChangeEvent<HTMLInputElement>): void => {
		if (e.target.files) uploadFiles(e.target.files);
	};

	const canUpload = !onlyOne || (currentItem?.media?.length ?? 0) < 1;

	return (
		<div className="mt-2 w-full rounded-md">
			<Label className="text-[11px] uppercase text-gray-50/40">{label}:</Label>
			<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="flex flex-col gap-3 rounded-lg bg-black/15 p-2">
				<div className="flex max-h-60 flex-col gap-3 overflow-y-auto pr-1">
					<AnimatePresence mode="popLayout">
						{currentItem?.media?.map((media) => (
							<motion.div key={media.id} layout>
								<MediaPreview media={media} handleDeletedSuccess={handleDeletedSuccess} currentItem={currentItem} elementId={id} />
							</motion.div>
						))}
					</AnimatePresence>
				</div>
				<div className="flex w-full justify-center">
					<motion.button
						whileHover={{ scale: 1.05 }}
						whileTap={{ scale: 0.95 }}
						type="button"
						disabled={!canUpload}
						className="rounded border border-primary/60 bg-primary/15 px-3 text-white disabled:opacity-50"
						onClick={() => canUpload && inputRef.current?.click()}
					>
						+ Adicionar
					</motion.button>
				</div>
				<input type="file" ref={inputRef} className="hidden" accept={[...IMAGE_TYPES, ...VIDEO_TYPES].join(',')} onChange={handleFileChange} />
			</motion.div>
		</div>
	);
};
