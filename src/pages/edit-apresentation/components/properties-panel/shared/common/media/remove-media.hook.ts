import { removeMediaRequest } from '@/pages/edit-apresentation/services/requests/elements/remove-medias';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

interface UseMediaRemoveProps {
	id: string;
	onDeletedSuccess: () => void;
}

export const useMediaRemove = ({ id, onDeletedSuccess }: UseMediaRemoveProps) => {
	const { mutate: removeMedia } = useMutation({
		mutationKey: ['remove-presentation-media', id],
		mutationFn: async (mediaIds: string[]) => {
			const response = await removeMediaRequest({ mediaIds, elementId: id });
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success('Mídia removida com sucesso!');
			onDeletedSuccess();
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message ?? 'Erro ao remover mídia');
		},
	});

	return { removeMedia };
};
