import { IItem } from '@/pages/edit-apresentation/types/item.type';
import { useImageLoader } from '@/shared/hooks/image-loader.hook';
import { motion } from 'framer-motion';
import { Trash2, Video as VideoIcon } from 'lucide-react';
import { IMedia } from '../../../item-editors/carousel/corousel.types';
import { IMAGE_TYPES, VIDEO_TYPES } from './media-types.data';
import { useMediaRemove } from './remove-media.hook';

interface MediaPreviewProps {
	media: IMedia;
	handleDeletedSuccess: (deletedIds: string[]) => void;
	currentItem: IItem | undefined;
	elementId: string;
}

const RemoveButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
	<button
		onClick={onClick}
		className="absolute right-2 top-2 rounded-md border border-red-700 bg-red-600/70 p-2 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
	>
		<Trash2 className="h-6 w-6" />
	</button>
);

const PreviewContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => (
	<div className="group relative flex h-full w-full items-center justify-center overflow-hidden">{children}</div>
);

const ImagePreview: React.FC<{ src: string; alt: string }> = ({ src, alt }) => (
	<motion.img
		src={src}
		alt={alt}
		className="h-full w-auto object-contain"
		initial={{ opacity: 0 }}
		animate={{ opacity: 1 }}
		transition={{ duration: 0.3 }}
	/>
);

const VideoPreview: React.FC = () => (
	<div className="absolute inset-0 flex items-center justify-center">
		<VideoIcon className="h-8 w-8 text-white" />
	</div>
);

const Preview: React.FC<{ media: IMedia; onRemove: () => void; blobUrl?: string }> = ({ media, onRemove, blobUrl }) => {
	if (IMAGE_TYPES.includes(media.mimetype)) {
		return (
			<PreviewContainer>
				<ImagePreview src={blobUrl || media.url} alt={media.filename} />
				<RemoveButton onClick={onRemove} />
			</PreviewContainer>
		);
	}

	if (VIDEO_TYPES.includes(media.mimetype)) {
		return (
			<PreviewContainer>
				<VideoPreview />
				<RemoveButton onClick={onRemove} />
			</PreviewContainer>
		);
	}

	return null;
};

export const MediaPreview: React.FC<MediaPreviewProps> = ({ media, handleDeletedSuccess, elementId, currentItem }) => {
	const { removeMedia } = useMediaRemove({
		id: elementId,
		onDeletedSuccess: () => handleDeletedSuccess([media.id]),
	});

	const { blobUrl } = useImageLoader({
		imageUrl: media.url,
		token: currentItem?.media?.[0]?.mediaTempToken,
	});

	return (
		<motion.div
			initial={{ opacity: 0, scale: 0.8 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.8 }}
			transition={{ duration: 0.3 }}
			className="flex h-[80px] w-full items-center justify-center rounded-md bg-gray-400/10"
		>
			<Preview media={media} onRemove={() => removeMedia([media.id])} blobUrl={blobUrl ?? undefined} />
		</motion.div>
	);
};
