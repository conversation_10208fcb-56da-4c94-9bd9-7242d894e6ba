import { addMediaRequest, IAddMediaResponse } from '@/pages/edit-apresentation/services/requests/elements/add-media';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import { createMediaPreview, filterValidFiles } from '../../../item-editors/carousel/carrousel.utils';

interface MediaUploadProps {
	id: string;
	onUploadSuccess: (data: IAddMediaResponse) => void;
}

export const useMediaUpload = ({ id, onUploadSuccess }: MediaUploadProps) => {
	const { mutate, isPending } = useMutation({
		mutationKey: ['update-presentation-media', id],
		mutationFn: async (file: File) => {
			try {
				const response = await addMediaRequest({ id, files: file });
				if (!response.success) {
					throw new Error(response.data.message);
				}
				return response.data;
			} catch (error) {
				throw error instanceof Error ? error : new Error('Erro desconhecido ao carregar mídia');
			}
		},
		onMutate: () => {
			toast.loading('Carregando...');
		},
		onSuccess: (data) => {
			toast.dismiss();
			onUploadSuccess?.(data);
			toast.success('Mídia carregada com sucesso!');
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
			console.error('[MediaUpload] Error:', error);
		},
	});

	const handleSelectedFiles = (files: FileList) => {
		const validFiles = filterValidFiles(files);

		if (validFiles.length === 0) {
			toast.error('Nenhum arquivo válido selecionado');
			return;
		}

		validFiles.forEach((file) => {
			const preview = createMediaPreview(file);
			if (preview) {
				mutate(file);
			}
		});
	};

	return {
		uploadFiles: handleSelectedFiles,
		isUploading: isPending,
	};
};
