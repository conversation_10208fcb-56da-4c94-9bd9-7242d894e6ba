// import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
// import { motion } from 'framer-motion';
// import { IBoxShadow } from '../../../../item-editors/image/image-editor.types';

// export interface PresetDropdownProps {
// 	currentShadow: IBoxShadow;
// 	onSelect: (preset: IBoxShadow & { name: string }) => void;
// 	presets: (IBoxShadow & { name: string })[];
// 	getBoxShadowCSS: (shadow: IBoxShadow) => string;
// }

// const dropdownVariants = {
// 	hidden: { opacity: 0, y: -10 },
// 	visible: { opacity: 1, y: 0 },
// 	exit: { opacity: 0, y: -10 },
// };

// export const PresetDropdown: React.FC<PresetDropdownProps> = ({ currentShadow, onSelect, presets, getBoxShadowCSS }) => {
// 	const handleSelect = (preset: IBoxShadow & { name: string }, event: React.SyntheticEvent) => {
// 		event.preventDefault();
// 		onSelect(preset);
// 	};

// 	return (
// 		<div>
// 			<h2 className="mb-2 text-xs uppercase tracking-wide text-neutral-400">Sombra:</h2>
// 			<DropdownMenu>
// 				<DropdownMenuTrigger asChild>
// 					<button
// 						className="rounded border border-neutral-700 bg-neutral-800 px-2 py-1 text-xs tracking-wide hover:bg-neutral-700"
// 						title="Selecione um preset de sombra"
// 					>
// 						Presets de Sombra
// 					</button>
// 				</DropdownMenuTrigger>
// 				<DropdownMenuContent asChild>
// 					<motion.div
// 						className="z-10 w-40 border border-neutral-700 bg-neutral-800 p-1"
// 						initial="hidden"
// 						animate="visible"
// 						exit="exit"
// 						variants={dropdownVariants}
// 					>
// 						{presets.map((preset, index) => (
// 							<DropdownMenuItem
// 								key={preset.name + index}
// 								onSelect={(e) => handleSelect(preset, e as unknown as React.SyntheticEvent)}
// 								className="cursor-pointer text-xs text-neutral-200 hover:bg-neutral-700"
// 							>
// 								{preset.name}
// 							</DropdownMenuItem>
// 						))}
// 					</motion.div>
// 				</DropdownMenuContent>
// 			</DropdownMenu>
// 			<span className="mt-1 block text-[11px] text-neutral-500">Atual: {getBoxShadowCSS(currentShadow)}</span>
// 		</div>
// 	);
// };
