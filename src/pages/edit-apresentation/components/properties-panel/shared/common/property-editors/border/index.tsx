import { IBorder } from '@/pages/edit-apresentation/types/elements/border-type';
import { InputGroup } from '../../../../core/components/input-group';
import { BORDER_STYLES } from '../../../../item-editors/shape/square.data';
import { InputWithLabel, SelectItemProperties } from '../../ui';

export interface IBorderEditorProps<T> {
	border: IBorder;
	onChange: <K extends keyof T>(field: K, value: string | T[K]) => void;
}

export function BorderEditor({ border, onChange }: Readonly<IBorderEditorProps<IBorder>>) {
	return (
		<section>
			<span className="mb-1 text-xs text-[#6c6c6c]">Borda</span>
			<div className="flex w-full flex-col rounded-lg bg-black/20 p-2">
				<InputGroup ariaLabel="Largura da borda">
					<fieldset className="flex w-full items-center justify-center border-0 p-0" aria-labelledby="border-style-radius">
						<legend id="border-style-radius" className="sr-only">
							Estilo e Raio da Borda
						</legend>
						<div className="w-1/2 pr-1">
							<SelectItemProperties
								value={border?.style}
								onValueChange={(value) => onChange('style', value)}
								items={BORDER_STYLES}
								labelText="Estilo:"
							/>
						</div>
						<div className="w-1/2 pl-1">
							<InputWithLabel
								type="number"
								value={border?.radius ?? ''}
								onChange={(e) => onChange('radius', Number(e.target.value))}
								sizeUnit="px"
								label="Radius"
							/>
						</div>
					</fieldset>
				</InputGroup>
				<InputGroup columns={2} ariaLabel="Cor e largura da borda">
					<InputWithLabel type="color" value={border?.color} onChange={(e) => onChange('color', e.target.value)} label="Cor" />
					<InputWithLabel
						type="number"
						value={border?.width ?? ''}
						onChange={(e) => onChange('width', Number(e.target.value))}
						sizeUnit="px"
						label="Largura"
					/>
				</InputGroup>
			</div>
		</section>
	);
}
