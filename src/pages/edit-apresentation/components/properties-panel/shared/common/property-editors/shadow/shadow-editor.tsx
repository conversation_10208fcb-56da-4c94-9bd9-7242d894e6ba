// import { Label } from '@/components/shadcnui/label';
// import { motion } from 'framer-motion';
// import React from 'react';
// import { Range, getTrackBackground } from 'react-range';
// import { IBoxShadow } from '../../../../item-editors/image/image-editor.types';
// import TextInput from '../../ui/input-item-with-icon';
// import { PresetDropdown } from './presets-menu';

// const SHADOW_PRESETS: (IBoxShadow & { name: string })[] = [
// 	{ name: '<PERSON><PERSON>', x: 2, y: 2, blur: 6, spread: 0, color: '#000000', inset: false },
// 	{ name: '<PERSON><PERSON> definida', x: 4, y: 4, blur: 8, spread: 2, color: '#888888', inset: false },
// 	{ name: 'Interna', x: 0, y: 0, blur: 5, spread: 5, color: '#444444', inset: true },
// ];

// export interface IShadowEditorProps {
// 	boxShadow: IBoxShadow;
// 	onChange: (boxShadow: IBoxShadow) => void;
// }

// const getBoxShadowCSS = (shadow: IBoxShadow): string => {
// 	const { x, y, blur, spread, color, inset } = shadow;
// 	return `${inset ? 'inset ' : ''}${x}px ${y}px ${blur}px ${spread}px ${color}`;
// };

// interface ShadowSliderProps {
// 	label: string;
// 	value: number;
// 	min: number;
// 	max: number;
// 	step?: number;
// 	trackColors: string[];
// 	onChange: (value: number) => void;
// }

// const ShadowSlider: React.FC<ShadowSliderProps> = ({ label, value, min, max, step = 1, trackColors, onChange }) => {
// 	return (
// 		<div className="flex flex-1 flex-col gap-2">
// 			<Label className="text-[10px] uppercase text-neutral-500">{label}</Label>
// 			<Range
// 				step={step}
// 				min={min}
// 				max={max}
// 				values={[value]}
// 				onChange={(values) => onChange(values[0])}
// 				renderTrack={({ props, children }) => (
// 					<div
// 						{...props}
// 						style={{
// 							width: '100%',
// 							height: '6px',
// 							borderRadius: '3px',
// 							background: getTrackBackground({
// 								values: [value],
// 								colors: trackColors,
// 								min,
// 								max,
// 							}),
// 							alignSelf: 'center',
// 							cursor: 'pointer',
// 						}}
// 					>
// 						{children}
// 					</div>
// 				)}
// 				renderThumb={({ props }) => (
// 					<div
// 						{...props}
// 						style={{
// 							height: '16px',
// 							width: '16px',
// 							borderRadius: '50%',
// 							backgroundColor: 'rgb(249 250 251 / 0.9)',
// 							border: '2px solid hsl(var(--primary))',
// 							cursor: 'grab',
// 						}}
// 					/>
// 				)}
// 			/>
// 			<TextInput
// 				type="number"
// 				value={value}
// 				onChange={(e) => onChange(Number(e.target.value))}
// 				className="mt-1 w-full border border-neutral-700 bg-neutral-800 text-sm"
// 			/>
// 		</div>
// 	);
// };

// export const ShadowEditor: React.FC<IShadowEditorProps> = ({ boxShadow = { x: 0, y: 0, blur: 0, spread: 0, color: '#000000', inset: false }, onChange }) => {
// 	const handleFieldChange = (field: keyof IBoxShadow, value: string | number | boolean) => {
// 		onChange({
// 			...boxShadow,
// 			[field]: value,
// 		});
// 	};

// 	const handleSelectPreset = (preset: IBoxShadow) => {
// 		onChange(preset);
// 	};

// 	return (
// 		<div className="mt-2 w-full rounded-md">
// 			<Label className="text-[11px] uppercase text-gray-50/40">Sombra:</Label>
// 			<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="flex flex-col gap-3 bg-black bg-black/15 p-2">
// 				<PresetDropdown getBoxShadowCSS={getBoxShadowCSS} currentShadow={boxShadow} onSelect={handleSelectPreset} presets={SHADOW_PRESETS} />

// 				<div className="flex w-full space-x-2">
// 					<ShadowSlider
// 						label="X Offset"
// 						value={boxShadow.x}
// 						min={-50}
// 						max={50}
// 						trackColors={['hsl(var(--primary))', 'rgb(249 250 251 / 0.2)']}
// 						onChange={(val) => handleFieldChange('x', val)}
// 					/>
// 					<ShadowSlider
// 						label="Y Offset"
// 						value={boxShadow.y}
// 						min={-50}
// 						max={50}
// 						trackColors={['hsl(var(--primary))', 'rgb(249 250 251 / 0.2)']}
// 						onChange={(val) => handleFieldChange('y', val)}
// 					/>
// 				</div>

// 				<div className="flex w-full space-x-2">
// 					<ShadowSlider
// 						label="Blur"
// 						value={boxShadow.blur}
// 						min={0}
// 						max={50}
// 						trackColors={['hsl(var(--primary))', 'rgb(249 250 251 / 0.2)']}
// 						onChange={(val) => handleFieldChange('blur', val)}
// 					/>
// 					<ShadowSlider
// 						label="Spread"
// 						value={boxShadow.spread}
// 						min={-20}
// 						max={20}
// 						trackColors={['hsl(var(--primary))', 'rgb(249 250 251 / 0.2)']}
// 						onChange={(val) => handleFieldChange('spread', val)}
// 					/>
// 				</div>

// 				<div className="flex items-end gap-4">
// 					<div className="flex flex-col">
// 						<Label className="text-[10px] uppercase text-neutral-500">Cor</Label>
// 						<TextInput
// 							type="color"
// 							value={boxShadow.color}
// 							onChange={(e) => handleFieldChange('color', e.target.value)}
// 							className="mt-1 h-8 w-12 border-none p-0"
// 						/>
// 					</div>
// 					<div className="flex flex-col">
// 						<Label className="mb-1 text-[10px] uppercase text-neutral-500">Tipo</Label>
// 						<label className="inline-flex cursor-pointer items-center gap-2 text-sm">
// 							<input
// 								type="checkbox"
// 								checked={!!boxShadow.inset}
// 								onChange={(e) => handleFieldChange('inset', e.target.checked)}
// 								className="peer hidden"
// 							/>
// 							<span className="h-4 w-4 rounded-full border border-neutral-700 bg-neutral-800 peer-checked:border-green-500 peer-checked:bg-green-500" />
// 							<span className="text-gray-50/40">Interna</span>
// 						</label>
// 					</div>
// 				</div>

// 				<div className="mt-3 flex flex-col">
// 					<Label className="mb-1 text-[10px] uppercase text-neutral-500">Preview:</Label>
// 					<div className="mx-auto h-24 w-24 border border-neutral-700 bg-neutral-800" style={{ boxShadow: getBoxShadowCSS(boxShadow) }} />
// 				</div>
// 			</motion.div>
// 		</div>
// 	);
// };
