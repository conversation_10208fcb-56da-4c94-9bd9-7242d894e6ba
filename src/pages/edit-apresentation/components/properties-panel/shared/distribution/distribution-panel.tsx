import { distributionButtonsConfig } from '@/pages/edit-apresentation/data/properties-panel/distribution-items';
import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';

import { distributeElements, DistributionType } from '../alignment/align-elements';
import { DistributionButton } from './distribution-component';

export const DistributionPanel = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedIds] = useAtom(selectedItemsIdsAtom);

	const selectedItems = items.filter((item) => selectedIds.includes(item.tempId));

	const handleDistribution = (distribution: DistributionType) => {
		const updatedItems = distributeElements(distribution, selectedItems);
		const newItems = items.map((item) => {
			const distributedItem = updatedItems.find((di) => di.tempId === item.tempId);
			return distributedItem ? distributedItem : item;
		});
		setItems(newItems);
	};

	return (
		<>
			{distributionButtonsConfig.map((button) => (
				<DistributionButton
					key={button.distributionType}
					icon={button.icon}
					tooltip={button.tooltip}
					distributionType={button.distributionType}
					onDistribute={handleDistribution}
					disabled={selectedItems.length < button.minItems}
				/>
			))}
		</>
	);
};
