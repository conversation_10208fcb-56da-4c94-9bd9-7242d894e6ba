import { motion } from 'framer-motion';
import React from 'react';

import { DistributionType } from '../alignment/align-elements';
import { AutoTooltip } from '../common/ui/tooltip';

type DistributionButtonProps = {
	icon: React.ElementType;
	tooltip: string;
	distributionType: DistributionType;
	onDistribute: (distributionType: DistributionType) => void;
	disabled?: boolean;
};

export const DistributionButton: React.FC<DistributionButtonProps> = ({ icon, tooltip, distributionType, onDistribute, disabled = false }) => {
	const handleClick = () => {
		if (!disabled) {
			onDistribute(distributionType);
		}
	};

	const getButtonStyles = () => {
		if (disabled) {
			return 'cursor-not-allowed opacity-30 bg-gray-800/50';
		}
		return 'hover:bg-green-500/20 hover:text-green-400 cursor-pointer focus:outline-none focus:ring-2 focus:ring-green-500/50';
	};

	return (
		<AutoTooltip preferredPlacement="top" text={disabled ? `${tooltip} (mínimo 3 itens)` : tooltip}>
			<motion.button
				onClick={handleClick}
				whileHover={!disabled ? { scale: 1.05 } : {}}
				whileTap={!disabled ? { scale: 0.95 } : {}}
				className={`group relative rounded-md p-2 transition-colors ${getButtonStyles()}`}
				aria-label={tooltip}
				disabled={disabled}
			>
				<span className="flex h-4 w-4 items-center justify-center">{React.createElement(icon, { size: 16 })}</span>
			</motion.button>
		</AutoTooltip>
	);
};
