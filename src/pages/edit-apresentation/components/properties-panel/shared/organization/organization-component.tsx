import { motion } from 'framer-motion';
import React from 'react';

import { OrganizationType } from '../alignment/align-elements';
import { AutoTooltip } from '../common/ui/tooltip';

type OrganizationButtonProps = {
	icon: React.ElementType;
	tooltip: string;
	organizationType: OrganizationType;
	onOrganize: (type: OrganizationType) => void;
	disabled?: boolean;
	variant?: 'default' | 'destructive';
};

export const OrganizationButton: React.FC<OrganizationButtonProps> = ({
	icon,
	tooltip,
	organizationType,
	onOrganize,
	disabled = false,
	variant = 'default',
}) => {
	const handleClick = () => {
		if (!disabled) {
			onOrganize(organizationType);
		}
	};

	const getButtonStyles = () => {
		if (disabled) {
			return 'cursor-not-allowed opacity-30 bg-gray-800/50';
		}

		if (variant === 'destructive') {
			return 'hover:bg-red-500/20 hover:text-red-500 cursor-pointer focus:outline-none focus:ring-2 focus:ring-red-500/50';
		}

		return 'hover:bg-purple-500/20 hover:text-purple-400 cursor-pointer focus:outline-none focus:ring-2 focus:ring-purple-500/50';
	};

	return (
		<AutoTooltip preferredPlacement="top" text={tooltip}>
			<motion.button
				onClick={handleClick}
				whileHover={!disabled ? { scale: 1.05 } : {}}
				whileTap={!disabled ? { scale: 0.95 } : {}}
				className={`group relative rounded-md p-2 transition-colors ${getButtonStyles()}`}
				aria-label={tooltip}
				disabled={disabled}
			>
				<span className="flex h-4 w-4 items-center justify-center">{React.createElement(icon, { size: 16 })}</span>
			</motion.button>
		</AutoTooltip>
	);
};
