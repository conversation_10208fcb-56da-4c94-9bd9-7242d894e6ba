import { organizationButtonsConfig } from '@/pages/edit-apresentation/data/properties-panel/organization-items';
import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';

import { ITEM_STATUS } from '@/pages/edit-apresentation/types/item.type';
import { OrganizationType, organizeElements } from '../alignment/align-elements';
import { OrganizationButton } from './organization-component';

export const OrganizationPanel = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [selectedIds, setSelectedIds] = useAtom(selectedItemsIdsAtom);

	const selectedItems = items.filter((item) => selectedIds.includes(item.tempId));

	const handleOrganization = (organization: OrganizationType) => {
		const result = organizeElements(organization, selectedItems, items);

		if (result.newElements) {
			const allNewItems = [...result.updatedElements, ...result.newElements];
			setItems(allNewItems);
			const newIds = result.newElements.map((item) => item.tempId);
			setSelectedIds(newIds);
		} else if (organization === OrganizationType.DELETE) {
			setItems(items.map((item) => (selectedIds.includes(item.tempId) ? { ...item, status: ITEM_STATUS.DELETED } : item)));
			setSelectedIds([]);
		} else {
			setItems(result.updatedElements);
		}
	};

	return (
		<>
			{organizationButtonsConfig.map((button) => (
				<OrganizationButton
					key={button.organizationType}
					icon={button.icon}
					tooltip={button.tooltip}
					organizationType={button.organizationType}
					onOrganize={handleOrganization}
					disabled={selectedItems.length < button.minItems}
					variant={button.variant}
				/>
			))}
		</>
	);
};
