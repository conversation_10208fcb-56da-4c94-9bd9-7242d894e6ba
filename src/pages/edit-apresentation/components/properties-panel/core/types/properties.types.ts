import { IItem } from '@/pages/edit-apresentation/types/item.type';

export interface ItemDetailsProps {
	selectedItem: IItem;
	presentation?: { width: number; height: number };
}

export interface SizeInputProps {
	dimension: keyof IItem['size'];
	value: number;
	onChange: (dimension: keyof IItem['size'], value: number) => void;
	error?: string;
}

export interface ElementSizeProps {
	selectedItem: IItem;
	handleSizeChange: (dimension: keyof IItem['size'], value: number) => void;
	widthError: string;
	heightError: string;
}

export interface ValidationResult {
	isValid: boolean;
	error: string;
}

export interface EditorProps {
	id: string;
	content: string;
	onChange: (content: string) => void;
}
