import { IItem, ITEM_SHAPE_TYPE } from '@/pages/edit-apresentation/types/item.type';
import { CalendarEditor } from '../../item-editors/calendar';
import { ICalendarObject } from '../../item-editors/calendar/calendar.types';
import CarouselObjectForm from '../../item-editors/carousel/carousel';
import { ICarouselObject } from '../../item-editors/carousel/corousel.types';
import { ImageEditor } from '../../item-editors/image';
import { IImagePreview } from '../../item-editors/image/image-editor.types';
import { RectangleEditor } from '../../item-editors/shape';
import { IShapeSquareStyle } from '../../item-editors/shape/square-editor.types';
import { TextEditorProperties } from '../../item-editors/text/text-editor';
import { WeatherEditor } from '../../item-editors/weather';
import { IWeather } from '../../item-editors/weather/weather-editor.type';

interface ItemEditorProps {
	item: IItem;
	onChange: (content: object) => void;
}

export const ItemEditorSelector = ({ item, onChange }: ItemEditorProps) => {
	const { id, type, content } = item;

	const renderEditor = () => {
		try {
			switch (type) {
				case ITEM_SHAPE_TYPE.CAROUSEL:
					return <CarouselObjectForm content={content as ICarouselObject} onChange={onChange} idElement={id!} />;
				case ITEM_SHAPE_TYPE.RECTANGLE:
					return <RectangleEditor id={id!} onChange={onChange} content={content as IShapeSquareStyle} />;
				case ITEM_SHAPE_TYPE.IMAGE:
					return <ImageEditor idElement={id!} onChange={onChange} content={content as IImagePreview} />;
				case ITEM_SHAPE_TYPE.TEXT:
					return <TextEditorProperties onChange={onChange} content={content} />;
				case ITEM_SHAPE_TYPE.WEATHER:
					return <WeatherEditor onChange={onChange} content={content as IWeather} />;
				case ITEM_SHAPE_TYPE.CALENDAR:
					return <CalendarEditor onChange={onChange} content={content as ICalendarObject} />;
				default:
					return <div className="text-xs text-[#8c8c8c]">Sem propriedades disponíveis</div>;
			}
		} catch (error) {
			console.error(`Erro ao renderizar editor para o tipo ${type}:`, error);
			return <div className="text-xs text-[#ff4d4f]">Erro ao carregar o editor. Por favor, tente novamente.</div>;
		}
	};

	return <section>{renderEditor()}</section>;
};
