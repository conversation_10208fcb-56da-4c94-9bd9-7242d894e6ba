import { ElementSizeProps } from '../types/properties.types';
import { SizeInput } from './size-input';

export const ElementSize = ({ selectedItem, handleSizeChange, widthError, heightError }: ElementSizeProps) => (
	<section className="mb-1">
		<h3 className="mb-1 text-xs font-normal uppercase text-[#8c8c8c]">Dimensões</h3>
		<div className="flex w-full gap-3">
			<SizeInput dimension="width" value={selectedItem.size.width} onChange={handleSizeChange} error={widthError} />
			<SizeInput dimension="height" value={selectedItem.size.height} onChange={handleSizeChange} error={heightError} />
		</div>
	</section>
);
