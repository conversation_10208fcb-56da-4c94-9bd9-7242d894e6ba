import { InputWithLabel } from '../../shared/common/ui/input-with-label';
import { SizeInputProps } from '../types/properties.types';

export const SizeInput = ({ dimension, value, onChange, error }: SizeInputProps) => {
	const label = dimension === 'width' ? 'W' : 'H';

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const val = e.target.value;
		onChange(dimension, val === '' ? 0 : Number(val));
	};

	return (
		<div className="flex flex-1 flex-col">
			<div className="flex items-center">
				<span className="mr-2 text-xs text-[#6c6c6c]">{label}</span>
				<InputWithLabel label="" type="number" value={value === 0 ? '' : value} onChange={handleChange} sizeUnit="px" className="flex-1" />
			</div>
			{error && <p className="mt-1 text-xs text-[#ff4d4f]">{error}</p>}
		</div>
	);
};
