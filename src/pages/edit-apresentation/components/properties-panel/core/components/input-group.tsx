interface InputGroupProps {
	children: React.ReactNode;
	ariaLabel: string;
	columns?: 1 | 2 | 3;
	className?: string;
}

export const InputGroup = ({ children, ariaLabel, columns = 1, className = '' }: InputGroupProps) => {
	const gridColumnsMap = {
		1: 'grid-cols-1',
		2: 'grid-cols-2',
		3: 'grid-cols-3',
	};

	return (
		<div className={`grid ${gridColumnsMap[columns]} items-center gap-3 ${className}`} aria-label={ariaLabel}>
			{children}
		</div>
	);
};
