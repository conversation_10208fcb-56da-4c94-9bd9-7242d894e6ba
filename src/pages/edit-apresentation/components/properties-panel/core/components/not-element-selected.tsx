import { motion } from 'framer-motion';
import <PERSON><PERSON> from 'lottie-react';
import { FC } from 'react';
import MouseData from '../../../../../../shared/constants/lotties/mouse.json';
import { containerVariants, textVariants, titleVariants } from '../data/animations/properties-panel/no-itens-selected';

const NoSelectionPanel: FC = () => (
	<motion.section
		className="flex h-full w-full max-w-[250px] flex-col items-center justify-center rounded-lg border p-6 text-center"
		variants={containerVariants}
		initial="hidden"
		animate="visible"
	>
		<figure className="flex w-full flex-col items-center justify-center gap-2">
			<div aria-hidden="true" className="flex h-16 w-16 items-center justify-center rounded-full bg-black/10">
				<Lottie animationData={MouseData} loop autoplay />
			</div>
			<figcaption>
				<motion.h3 className="mb-1 text-base font-medium text-gray-200" variants={titleVariants} initial="hidden" animate="visible">
					Nenhum item selecionado
				</motion.h3>
				<motion.p className="text-xs text-gray-400" variants={textVariants} initial="hidden" animate="visible">
					Clique em um elemento na apresentação para editar suas propriedades
				</motion.p>
			</figcaption>
		</figure>
	</motion.section>
);
export default NoSelectionPanel;
