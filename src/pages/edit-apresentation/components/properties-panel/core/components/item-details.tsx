import { ItemDetailsProps } from '../types/properties.types';

export const ItemDetails = ({ selectedItem }: ItemDetailsProps) => (
	<section>
		<div className="flex flex-col gap-1.5">
			<div className="flex items-center justify-between">
				<span className="text-xs uppercase text-[#6c6c6c]">Nome</span>
				<span className="text-xs text-[#e0e0e0]">{selectedItem.name}</span>
			</div>
			<div className="flex items-center justify-between">
				<span className="text-xs uppercase text-[#6c6c6c]">Tamanho</span>
				<span className="text-xs text-[#e0e0e0]">{`${selectedItem.size.width} x ${selectedItem.size.height}`}</span>
			</div>
		</div>
	</section>
);
