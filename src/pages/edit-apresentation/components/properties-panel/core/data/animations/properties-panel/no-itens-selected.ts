import { Variants } from 'framer-motion';

export const containerVariants: Variants = {
	hidden: { opacity: 0 },
	visible: { opacity: 1, transition: { duration: 0.3 } },
};

export const titleVariants: Variants = {
	hidden: { y: 10, opacity: 0 },
	visible: { y: 0, opacity: 1, transition: { delay: 0.3, duration: 0.4 } },
};

export const textVariants: Variants = {
	hidden: { y: 10, opacity: 0 },
	visible: { y: 0, opacity: 1, transition: { delay: 0.4, duration: 0.4 } },
};
