import { IPresentationInfo } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import { IItem } from '@/pages/edit-apresentation/types/item.type';
import { motion } from 'framer-motion';
import { AlignCenter, Grid3X3, Settings } from 'lucide-react';
import { FC } from 'react';
import { AlignmentPanel } from '../shared/alignment/alignment-panel';
import { SectionHeader } from '../shared/common/section-header';
import { DistributionPanel } from '../shared/distribution';
import { OrganizationPanel } from '../shared/organization';
import { ElementSize } from './components/element-size';
import { ItemDetails } from './components/item-details';
import { ItemEditorSelector } from './components/item-editor-selector';
import NoSelectionPanel from './components/not-element-selected';
import { usePropertiesPanel } from './hooks/properties-panel.hook';

const AnimatedSection: FC<{
	children: React.ReactNode;
	className?: string;
	ariaLabel?: string;
	delay?: number;
}> = ({ children, className = '', ariaLabel, delay = 0 }) => (
	<motion.section className={className} aria-label={ariaLabel} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay, duration: 0.2 }}>
		{children}
	</motion.section>
);

const Separator: FC = () => <div className="my-2 h-[1px] w-full bg-[#333333]" />;

export const PropertiesPanel: FC = () => {
	const { selectedItems, presentation, widthError, heightError, handleItemChange, handleSizeChange } = usePropertiesPanel();
	if (!selectedItems.length) return <NoSelectionPanel />;
	const isMultipleSelection = selectedItems.length > 1;
	const selectedItem: IItem = selectedItems[0];
	const safePresentation: IPresentationInfo | undefined = presentation || undefined;

	return (
		<motion.section
			className="relative flex h-auto w-full flex-col p-2"
			aria-label={isMultipleSelection ? 'Painel de Alinhamento de Múltiplos Itens' : 'Painel de Propriedades do Item'}
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.2 }}
		>
			<ItemDetails selectedItem={selectedItem} presentation={safePresentation} />
			<Separator />
			{isMultipleSelection ? (
				<>
					<div className="flex flex-col space-y-4">
						<div>
							<SectionHeader title="Alinhamento" icon={AlignCenter} />
							<AnimatedSection delay={0.1} className="flex w-full flex-wrap gap-1 rounded-lg border border-[#333] bg-[#1a1a1a] p-3">
								<AlignmentPanel />
							</AnimatedSection>
						</div>
						<div>
							<SectionHeader title="Distribuição" icon={Grid3X3} subtitle={selectedItems.length < 3 ? '(mínimo 3 itens)' : undefined} />
							<AnimatedSection delay={0.15} className="flex w-full flex-wrap gap-1 rounded-lg border border-[#333] bg-[#1a1a1a] p-3">
								<DistributionPanel />
							</AnimatedSection>
						</div>
						<div>
							<SectionHeader title="Organização" icon={Settings} />
							<AnimatedSection delay={0.2} className="flex w-full flex-wrap gap-1 rounded-lg border border-[#333] bg-[#1a1a1a] p-3">
								<OrganizationPanel />
							</AnimatedSection>
						</div>
					</div>
					<Separator />
				</>
			) : (
				<>
					<AnimatedSection ariaLabel="Tamanho do Elemento" delay={0.1}>
						<ElementSize
							selectedItem={selectedItem}
							handleSizeChange={(dimension, value) => handleSizeChange(selectedItem, dimension, value)}
							widthError={widthError}
							heightError={heightError}
						/>
					</AnimatedSection>
					<Separator />
					<AnimatedSection ariaLabel="Editor de conteúdo" className="pb-4" delay={0.2}>
						<ItemEditorSelector item={selectedItem} onChange={(content) => handleItemChange(selectedItem, 'content', content)} />
					</AnimatedSection>
				</>
			)}
		</motion.section>
	);
};

export default PropertiesPanel;
