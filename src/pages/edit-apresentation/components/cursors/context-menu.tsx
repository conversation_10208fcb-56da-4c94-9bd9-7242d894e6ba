// import { AnimatePresence, motion } from 'framer-motion';
// import React, { useEffect, useRef } from 'react';

// export interface IMenuItem {
// 	label: string;
// 	onClick: () => void;
// }

// interface ContextMenuProps {
// 	visible: boolean;
// 	x: number;
// 	y: number;
// 	items: IMenuItem[];
// 	onRequestClose: () => void;
// }

// export const ContextMenu: React.FC<ContextMenuProps> = ({ visible, x, y, items, onRequestClose }) => {
// 	const menuRef = useRef<HTMLDivElement>(null);

// 	useEffect(() => {
// 		function handleClickOutside(e: MouseEvent) {
// 			if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
// 				onRequestClose();
// 			}
// 		}
// 		if (visible) {
// 			document.addEventListener('mousedown', handleClickOutside);
// 		}
// 		return () => {
// 			document.removeEventListener('mousedown', handleClickOutside);
// 		};
// 	}, [visible, onRequestClose]);

// 	return (
// 		<AnimatePresence>
// 			{visible && (
// 				<motion.div
// 					ref={menuRef}
// 					initial={{ opacity: 0, scale: 0.8 }}
// 					animate={{ opacity: 1, scale: 1 }}
// 					exit={{ opacity: 0, scale: 0.8 }}
// 					transition={{ type: 'spring', stiffness: 300, damping: 20 }}
// 					className="absolute z-50 w-48 rounded-md border bg-black shadow-md"
// 					style={{
// 						top: y,
// 						left: x,
// 					}}
// 				>
// 					{items.map((item, idx) => (
// 						<div
// 							key={idx}
// 							className="hover:bg-gray-50/2 cursor-pointer px-4 py-2"
// 							onClick={() => {
// 								item.onClick();
// 								onRequestClose();
// 							}}
// 						>
// 							{item.label}
// 						</div>
// 					))}
// 				</motion.div>
// 			)}
// 		</AnimatePresence>
// 	);
// };
