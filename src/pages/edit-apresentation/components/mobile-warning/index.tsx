import { motion } from 'framer-motion';

export const MobileWarning = () => {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0, y: -20 }}
			transition={{ duration: 0.5 }}
			className="flex h-full w-full flex-col items-center justify-center p-6 text-center"
		>
			<motion.svg
				xmlns="http://www.w3.org/2000/svg"
				className="mb-4 h-16 w-16 text-gray-500"
				fill="none"
				viewBox="0 0 24 24"
				stroke="currentColor"
				initial={{ scale: 0 }}
				animate={{ scale: 1 }}
				transition={{ duration: 0.5 }}
			>
				<motion.path
					strokeLinecap="round"
					strokeLinejoin="round"
					strokeWidth={2}
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
					initial={{ pathLength: 0 }}
					animate={{ pathLength: 1 }}
					transition={{ duration: 1 }}
				/>
			</motion.svg>
			<motion.h2 className="mb-2 text-xl font-bold" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3, duration: 0.5 }}>
				Dispositivo não compatível
			</motion.h2>
			<motion.p className="text-gray-600" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.5, duration: 0.5 }}>
				Esta funcionalidade está disponível apenas em dispositivos desktop. Por favor, acesse novamente em um computador.
			</motion.p>
		</motion.div>
	);
};
