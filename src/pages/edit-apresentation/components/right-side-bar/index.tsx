import { Toolt<PERSON>, Toolt<PERSON>Content, Too<PERSON><PERSON>Provider, TooltipTrigger } from '@/components/shadcnui/tooltip';
import { motion, Variants } from 'framer-motion';
import { Palette } from 'lucide-react';
import React from 'react';
import { PropertiesPanel } from '../properties-panel';

const sidebarVariants: Variants = {
	hidden: { x: 100, opacity: 0 },
	visible: { x: 0, opacity: 1, transition: { duration: 0.5 } },
};

interface RightLiveSideBarProps {
	propertiesPanelRef: React.RefObject<HTMLDivElement>;
}

const SidebarHeader: React.FC = () => (
	<header className="flex h-10 items-center px-3 py-2">
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<div className="flex cursor-pointer items-center gap-2">
						<Palette className="text-muted-foreground" size={16} />
						<h3 className="text-xs font-medium text-foreground">Estilos</h3>
					</div>
				</TooltipTrigger>
				<TooltipContent side="left" className="border-border bg-background text-foreground">
					<p>Gerencie os estilos do seu projeto</p>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	</header>
);

const RightLiveSideBar: React.FC<RightLiveSideBarProps> = ({ propertiesPanelRef }) => (
	<motion.aside
		ref={propertiesPanelRef}
		initial="hidden"
		animate="visible"
		variants={sidebarVariants}
		className="relative right-0 flex h-full min-w-[227px] max-w-[227px] select-none flex-col rounded-l-lg border-t border-primary/40 bg-muted shadow-lg"
		aria-label="Barra lateral de Estilos"
	>
		<SidebarHeader />
		<main className="scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent flex-1 overflow-y-auto bg-background/15">
			<PropertiesPanel />
		</main>
	</motion.aside>
);

export default RightLiveSideBar;
