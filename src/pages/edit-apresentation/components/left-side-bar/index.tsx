import { Toolt<PERSON>, Toolt<PERSON>Content, Too<PERSON><PERSON><PERSON>rovider, TooltipTrigger } from '@/components/shadcnui/tooltip';
import { motion, Variants } from 'framer-motion';
import { Layers } from 'lucide-react';
import React from 'react';
import { ElementsLayer } from '../elements-layers';

const sidebarVariants: Variants = {
	hidden: { x: -100, opacity: 0 },
	visible: { x: 0, opacity: 1, transition: { duration: 0.5 } },
};

const SidebarHeader: React.FC = () => (
	<header className="ju flex h-10 items-center px-3 py-2">
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<div className="flex cursor-pointer items-center gap-2">
						<Layers className="text-muted-foreground" size={16} />
						<h3 className="text-xs font-medium text-foreground">Camadas</h3>
					</div>
				</TooltipTrigger>
				<TooltipContent side="right" className="border-border bg-background text-foreground">
					<p><PERSON><PERSON><PERSON><PERSON> as camadas do seu projeto</p>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	</header>
);

const LeftLiveSideBar: React.FC = () => (
	<motion.aside
		initial="hidden"
		animate="visible"
		variants={sidebarVariants}
		className="relative left-0 flex h-full min-w-[240px] max-w-[260px] select-none flex-col rounded-r-lg border-t border-primary/40 bg-muted shadow-lg"
		aria-label="Barra lateral de Camadas"
	>
		<SidebarHeader />
		<main className="scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent flex-1 overflow-y-auto bg-background/45">
			<ElementsLayer />
		</main>
	</motion.aside>
);

export default LeftLiveSideBar;
