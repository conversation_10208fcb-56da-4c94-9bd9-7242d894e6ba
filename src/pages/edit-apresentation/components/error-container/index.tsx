import Particles from '@/components/shadcnui/particles';
import { motion } from 'framer-motion';
import { AlertOctagon } from 'lucide-react';

interface ErrorContainerProps {
	message: string;
	status?: number;
}

export const ErrorContainer: React.FC<ErrorContainerProps> = ({ message, status }) => {
	return (
		<div className="relative flex min-h-screen w-full items-center justify-center overflow-hidden bg-gradient-to-br from-background via-muted/60 to-background/90 p-4">
			<motion.div
				className="relative z-10 flex max-w-md flex-col items-center rounded-3xl border border-border/30 bg-background/95 p-12 shadow-2xl ring-2 ring-destructive/10 backdrop-blur-2xl"
				initial={{ opacity: 0, scale: 0.85, y: 30 }}
				animate={{ opacity: 1, scale: 1, y: 0 }}
				transition={{ duration: 0.7, ease: [0.23, 1, 0.32, 1] }}
			>
				<motion.div
					initial={{ scale: 0.8, opacity: 0, rotate: -10 }}
					animate={{ scale: 1, opacity: 1, rotate: 0 }}
					transition={{ duration: 0.6, ease: 'easeInOut' }}
					className="flex flex-col items-center"
				>
					<div className="mb-6 flex animate-pulse items-center justify-center rounded-full bg-gradient-to-tr from-destructive/20 to-destructive/40 p-5 shadow-xl">
						<AlertOctagon className="h-16 w-16 text-destructive drop-shadow-xl" />
					</div>
					{status && <p className="mb-1 text-center text-base font-semibold tracking-wide text-destructive/80">Código: {status}</p>}
				</motion.div>
				<h2 className="animate-gradient-x mb-3 bg-gradient-to-r from-destructive to-destructive/70 bg-clip-text text-3xl font-extrabold text-transparent drop-shadow-lg">
					Ops, encontramos um problema!
				</h2>

				<motion.div
					className="w-full"
					initial={{ opacity: 0, height: 0 }}
					animate={{ opacity: 1, height: 'auto' }}
					transition={{ delay: 0.2, duration: 0.5 }}
				>
					<p className="mb-6 min-h-[40px] w-full overflow-hidden rounded-lg border border-destructive/30 bg-muted/50 p-4 text-muted-foreground/90 shadow-inner backdrop-blur-lg">
						<span className="font-mono text-sm">{message}</span>
					</p>
				</motion.div>

				<p className="mb-6 text-center text-sm text-muted-foreground/80">
					Tente retornar à página anterior ou recarregar a página para resolver o problema.
				</p>
				<motion.button
					whileHover={{ scale: 1.07, boxShadow: '0 0 16px #ef4444aa' }}
					whileTap={{ scale: 0.97 }}
					onClick={() => window.location.reload()}
					className="rounded-lg bg-gradient-to-r from-destructive to-destructive/70 px-6 py-2 font-semibold text-white shadow-lg transition-all hover:brightness-110 focus:outline-none focus:ring-2 focus:ring-destructive/50"
				>
					Recarregar página
				</motion.button>
			</motion.div>

			<Particles className="absolute inset-0" quantity={220} refresh />
			<div className="pointer-events-none absolute inset-0 bg-gradient-to-br from-transparent via-destructive/10 to-transparent opacity-80 blur-2xl" />
		</div>
	);
};
