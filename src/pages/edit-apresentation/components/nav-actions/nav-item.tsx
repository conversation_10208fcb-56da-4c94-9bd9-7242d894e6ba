import { Button } from '@/components/shadcnui/button';
import { INavElement } from '@/shared/constants/my-figma';
import { useAtomValue, useSetAtom } from 'jotai';
import { itemsSelecteds, updateItemAtom } from '../../../../shared/states/items/object-item.state';

import { undoAtom } from '../../states/history/history.state';
import { ITEM_STATUS } from '../../types/item.type';
import { AutoTooltip } from '../properties-panel/shared/common/ui/tooltip';
import { DropMenu } from './drop-menu';

interface NavItemProps {
	item: INavElement;
}

const buttonBaseClass = 'relative h-full w-[80px] transform rounded-md bg-[#121214] text-white border border-gray-800/40 shadow-sm transition-all duration-200';

const buttonActiveClass =
	'ease-in-out hover:scale-105 hover:bg-[#1c1c20] hover:border-green-500/30 hover:shadow-green-400/20 focus:ring-1 focus:ring-green-500/40';

const buttonDisabledClass = 'cursor-not-allowed opacity-40 border-gray-800/20';

export const NavItem = ({ item }: NavItemProps) => {
	const selectedItems = useAtomValue(itemsSelecteds);

	const updateItem = useSetAtom(updateItemAtom);
	const undoAction = useSetAtom(undoAtom);

	const handleDelete = () => {
		selectedItems.forEach((item) => {
			updateItem({ ...item, status: ITEM_STATUS.DELETED });
		});
	};

	const renderButton = () => {
		if (item.value === 'delete') {
			const disabled = selectedItems.length === 0;
			return (
				<Button
					disabled={disabled}
					onClick={handleDelete}
					className={`${buttonBaseClass} ${disabled ? buttonDisabledClass : buttonActiveClass}`}
					data-nav-action="delete"
				>
					{item.icon}
				</Button>
			);
		}

		if (item.value === 'reset') {
			return (
				<Button onClick={() => undoAction()} className={`${buttonBaseClass} ${buttonActiveClass}`} data-nav-action="reset">
					{item.icon}
				</Button>
			);
		}

		return <Button className={`${buttonBaseClass} ${buttonActiveClass}`}>{item.icon}</Button>;
	};

	const tooltipText = item.value === 'delete' && selectedItems.length === 0 ? 'Selecione pelo menos um item para deletar' : (item.actionLabel ?? '');

	return (
		<li className="group flex items-center justify-center">
			{Array.isArray(item.value) ? (
				<DropMenu item={item} />
			) : (
				<AutoTooltip text={tooltipText} preferredPlacement="top">
					{renderButton()}
				</AutoTooltip>
			)}
		</li>
	);
};
