'use client';

import { But<PERSON> } from '@/components/shadcnui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
import { INavElement } from '@/shared/constants/my-figma';
import { useAtom } from 'jotai';
import { twMerge } from 'tailwind-merge';
import { nextElementAtom } from '../../../../shared/states/items/next-items.state';
import { getContextNextElement } from '../../data/base-format-elements';
import { IItemMenu } from '../../data/items/shapes-menu';
import { IItemShapeType } from '../../types/item.type';

const MENU_ITEM_CLASSES =
	'flex h-fit justify-between gap-10 rounded-none bg-[#121214] px-5 py-3 text-white focus:border-none hover:bg-[#1c1c20] hover:text-green-400 transition-colors duration-150';

interface MenuActionProps {
	menuItem: IItemMenu;
	onSelect: (shape: IItemShapeType, icon: React.ReactElement<{ className?: string }>) => void;
}

const MenuAction: React.FC<MenuActionProps> = ({ menuItem, onSelect }) => (
	<DropdownMenuItem asChild>
		<Button variant="default" onClick={() => onSelect(menuItem.value, menuItem.icon)} className={twMerge(MENU_ITEM_CLASSES)}>
			<div className="group flex items-center gap-3">
				<span className="text-green-400/80 transition-colors group-hover:text-green-400">{menuItem.icon}</span>
				<p className="text-sm font-medium">{menuItem.name}</p>
			</div>
		</Button>
	</DropdownMenuItem>
);

interface DropMenuProps {
	item: INavElement;
}

export const DropMenu: React.FC<DropMenuProps> = ({ item }) => {
	const [nextElement, setNextElement] = useAtom(nextElementAtom);

	const handleSelect = (shape: IItemShapeType, icon: React.ReactElement<{ className?: string }>) => {
		setNextElement({ type: shape, icon, contentDefault: getContextNextElement(shape) });
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button
						variant="secondary"
						className="relative h-full w-[80px] transform rounded-md border border-gray-800/40 bg-[#121214] text-white shadow-sm transition-all duration-200 ease-in-out hover:scale-105 hover:border-green-500/30 hover:bg-[#1c1c20] hover:shadow-green-400/20 focus:ring-1 focus:ring-green-500/40"
					>
						{nextElement?.icon ?? item.icon}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent className="mt-2 flex flex-col gap-0.5 rounded-md border border-gray-800/60 bg-[#121214] p-0.5 shadow-md shadow-green-500/5">
					{Array.isArray(item.value) &&
						item.value.map((menuItem) => <MenuAction key={menuItem.name} menuItem={menuItem} onSelect={handleSelect} />)}
				</DropdownMenuContent>
			</DropdownMenu>
			<input type="file" className="hidden" accept="image/*" />
		</>
	);
};
