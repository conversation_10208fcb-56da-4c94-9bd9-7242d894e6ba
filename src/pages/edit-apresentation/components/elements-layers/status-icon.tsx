import { Toolt<PERSON>, Toolt<PERSON>Content, Too<PERSON><PERSON><PERSON>rovider, TooltipTrigger } from '@/components/shadcnui/tooltip';
import { motion } from 'framer-motion';
import { Check, Clock } from 'lucide-react';
import { ITEM_STATUS } from '../../types/item.type';

interface StatusIconProps {
	status: ITEM_STATUS;
	currentItemActive: boolean;
}

const statusDescriptions = {
	[ITEM_STATUS.NEW]: 'Novo elemento (não salvo)',
	[ITEM_STATUS.SAVED]: 'Elemento salvo',
	[ITEM_STATUS.UPDATING]: 'Atualizando elemento...',
	[ITEM_STATUS.DELETED]: 'Elemento excluído',
};

const NewStatusIcon: React.FC = () => (
	<TooltipProvider>
		<Tooltip>
			<TooltipTrigger asChild>
				<div className="flex h-4 w-4 items-center justify-center">
					<div className="h-2 w-2 rounded-full bg-[#F5A623]" />
				</div>
			</TooltipTrigger>
			<TooltipContent side="left" className="border-[#333333] bg-[#252525] text-[#e0e0e0]">
				<p className="text-xs">{statusDescriptions[ITEM_STATUS.NEW]}</p>
			</TooltipContent>
		</Tooltip>
	</TooltipProvider>
);

interface SavedStatusIconProps {
	currentItemActive: boolean;
}

const SavedStatusIcon: React.FC<SavedStatusIconProps> = ({ currentItemActive }) => (
	<TooltipProvider>
		<Tooltip>
			<TooltipTrigger asChild>
				<div className="flex h-4 w-4 items-center justify-center">
					<Check size={12} className={`${currentItemActive ? 'text-white' : 'text-[#66BF3C]'}`} />
				</div>
			</TooltipTrigger>
			<TooltipContent side="left" className="border-[#333333] bg-[#252525] text-[#e0e0e0]">
				<p className="text-xs">{statusDescriptions[ITEM_STATUS.SAVED]}</p>
			</TooltipContent>
		</Tooltip>
	</TooltipProvider>
);

const UpdatingStatusIcon: React.FC = () => (
	<TooltipProvider>
		<Tooltip>
			<TooltipTrigger asChild>
				<div className="flex h-4 w-4 items-center justify-center">
					<motion.div animate={{ rotate: 360 }} transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}>
						<Clock size={12} className="text-[#4B9FFF]" />
					</motion.div>
				</div>
			</TooltipTrigger>
			<TooltipContent side="left" className="border-[#333333] bg-[#252525] text-[#e0e0e0]">
				<p className="text-xs">{statusDescriptions[ITEM_STATUS.UPDATING]}</p>
			</TooltipContent>
		</Tooltip>
	</TooltipProvider>
);

export const StatusIcon: React.FC<StatusIconProps> = ({ status, currentItemActive }) => {
	switch (status) {
		case ITEM_STATUS.NEW:
			return <NewStatusIcon />;
		case ITEM_STATUS.SAVED:
			return <SavedStatusIcon currentItemActive={currentItemActive} />;
		case ITEM_STATUS.UPDATING:
			return <UpdatingStatusIcon />;
		default:
			return null;
	}
};
