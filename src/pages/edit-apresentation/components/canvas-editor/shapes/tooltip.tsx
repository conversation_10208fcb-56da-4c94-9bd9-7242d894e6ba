import { AnimatePresence, MotionStyle, motion } from 'framer-motion';
import { useLayoutEffect, useRef, useState } from 'react';

export interface IShapeTooltipProps {
	visible: boolean;
	x: number;
	y: number;
	name: string;
	width: number;
	height: number;
	containerWidth: number;
}

export const ShapeTooltip = ({ visible, x, y, name, width, height, containerWidth }: IShapeTooltipProps) => {
	const tooltipRef = useRef<HTMLDivElement>(null);
	const [computedLeft, setComputedLeft] = useState(x);
	const offset = 20;

	useLayoutEffect(() => {
		if (tooltipRef.current && containerWidth) {
			const tooltipWidth = tooltipRef.current.offsetWidth;
			if (x + tooltipWidth > containerWidth) {
				setComputedLeft(x - tooltipWidth - offset);
			} else {
				setComputedLeft(x);
			}
		}
	}, [x, name, width, height, containerWidth, offset]);

	const tooltipStyle: MotionStyle = {
		position: 'absolute',
		top: y,
		left: computedLeft,
		backgroundColor: 'rgba(0, 0, 0, 0.7)',
		color: '#fff',
		padding: '4px 8px',
		borderRadius: '4px',
		pointerEvents: 'none' as const,
		fontSize: '12px',
		zIndex: 9999,
		userSelect: 'none',
	};

	return (
		<AnimatePresence>
			{visible && (
				<motion.div
					ref={tooltipRef}
					initial={{ opacity: 0, scale: 0.8 }}
					animate={{ opacity: 1, scale: 1 }}
					exit={{ opacity: 0, scale: 0.8 }}
					transition={{ duration: 0.2 }}
					style={tooltipStyle}
				>
					{name} - {width} x {height}
				</motion.div>
			)}
		</AnimatePresence>
	);
};
