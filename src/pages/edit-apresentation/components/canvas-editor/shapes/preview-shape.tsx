import { Rect } from 'react-konva';
import { IItemShapeType } from '../../../types/item.type';

interface PreviewShapeProps {
	shapeType: IItemShapeType;
	x: number;
	y: number;
	size?: number;
}
export interface Position {
	x: number;
	y: number;
}

export interface Size {
	width: number;
	height: number;
}

export function PreviewShape({ x, y, size = 100 }: PreviewShapeProps) {
	return <Rect key="preview" x={x - size / 2} y={y - size / 2} width={size} height={size} fill="gray" opacity={0.5} listening={false} />;
}
