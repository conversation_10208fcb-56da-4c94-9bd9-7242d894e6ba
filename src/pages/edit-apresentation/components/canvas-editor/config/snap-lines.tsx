import { snapLinesAtom } from '@/pages/edit-apresentation/states/canvas/snap-lines.state';
import { useAtomValue } from 'jotai';
import { Group, Layer, Line } from 'react-konva';

interface CanvasSnapLinesProps {
	width: number;
	height: number;
	padding?: number;
}

export const CanvasSnapLines = ({ width, height, padding = 0 }: CanvasSnapLinesProps) => {
	const snapLines = useAtomValue(snapLinesAtom);

	return (
		<Layer listening={false}>
			<Group x={padding} y={padding}>
				{snapLines.vertical.map((x) => (
					<Line key={`v-${x}`} points={[x, 0, x, height]} stroke="#FFFFFF" strokeWidth={1} dash={[4, 4]} opacity={0.3} />
				))}
				{snapLines.horizontal.map((y) => (
					<Line key={`h-${y}`} points={[0, y, width, y]} stroke="#FFFFFF" strokeWidth={1} dash={[4, 4]} opacity={0.3} />
				))}
			</Group>
		</Layer>
	);
};
