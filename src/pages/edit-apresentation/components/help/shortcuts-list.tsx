import { Badge } from '@/components/shadcnui/badge';

import { AnimatePresence, motion } from 'framer-motion';
import { Search } from 'lucide-react';
import { CATEGORY_COLORS, CATEGORY_ICONS } from '../../data/constants/keyboard-shortcuts-modal.constants';
import { ShortcutInfo } from '../../hooks/help/keyboard-shortcuts-data.hook';

interface ShortcutsListProps {
	filteredShortcuts: Record<string, ShortcutInfo[]>;
	formatShortcut: (shortcut: ShortcutInfo['shortcut']) => string;
}

export function ShortcutsList({ filteredShortcuts, formatShortcut }: ShortcutsListProps) {
	if (Object.keys(filteredShortcuts).length === 0) {
		return (
			<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="py-12 text-center">
				<Search className="mx-auto mb-4 h-12 w-12 text-gray-600" />
				<p className="text-lg text-gray-400">Nenhum atalho encontrado</p>
				<p className="text-sm text-gray-500">Tente ajustar sua busca ou filtros</p>
			</motion.div>
		);
	}

	return (
		<div className="h-full max-h-screen">
			<div className="over space-y-6 pb-4 pr-4">
				<AnimatePresence>
					{Object.entries(filteredShortcuts).map(([category, shortcuts]) => (
						<motion.div
							key={category}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -20 }}
							transition={{ duration: 0.2 }}
							className="space-y-3"
						>
							<div className="flex items-center gap-3">
								<Badge className={`${CATEGORY_COLORS[category]} flex items-center gap-2 px-3 py-1`}>
									{CATEGORY_ICONS[category]}
									{category}
								</Badge>
								<div className="h-px flex-1 bg-primary"></div>
							</div>
							<div className="grid gap-2">
								{shortcuts.map((shortcut) => (
									<motion.div
										key={shortcut.key}
										initial={{ opacity: 0, x: -20 }}
										animate={{ opacity: 1, x: 0 }}
										transition={{ duration: 0.2, delay: 0.1 }}
										className="group flex items-center justify-between rounded-lg border border-primary/20 bg-black/50 p-3 transition-colors hover:bg-primary/10"
									>
										<span className="text-gray-200 transition-colors group-hover:text-white">{shortcut.description}</span>
										<div className="flex items-center gap-1">
											{formatShortcut(shortcut.shortcut)
												.split(' + ')
												.map((key, index, array) => (
													<div key={index} className="flex items-center gap-1">
														<kbd className="rounded border border-primary-50 bg-gradient-to-br from-primary/40 via-primary/60 to-primary/80 px-2 py-1 font-mono text-xs text-gray-300 transition-colors group-hover:from-gray-700 group-hover:via-gray-800 group-hover:to-gray-900 group-hover:text-white">
															{key}
														</kbd>
														{index < array.length - 1 && <span className="text-xs text-gray-500">+</span>}
													</div>
												))}
										</div>
									</motion.div>
								))}
							</div>
						</motion.div>
					))}
				</AnimatePresence>
			</div>
		</div>
	);
}
