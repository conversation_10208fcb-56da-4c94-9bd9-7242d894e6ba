import { Button } from '@/components/shadcnui/button';
import { Input } from '@/components/shadcnui/input';
import { Search } from 'lucide-react';
import { CATEGORY_ICONS } from '../../data/constants/keyboard-shortcuts-modal.constants';

interface ShortcutsSearchAndFilterProps {
	searchTerm: string;
	setSearchTerm: (term: string) => void;
	selectedCategory: string | null;
	setSelectedCategory: (category: string | null) => void;
	categories: string[];
}

export function ShortcutsSearchAndFilter({ searchTerm, setSearchTerm, selectedCategory, setSelectedCategory, categories }: ShortcutsSearchAndFilterProps) {
	return (
		<div className="flex flex-col gap-4">
			<div className="relative flex flex-1 items-center">
				<Search className="pointer-events-none absolute left-3 h-4 w-4 text-gray-400" style={{ top: '50%', transform: 'translateY(-50%)' }} />
				<Input
					type="text"
					placeholder="Buscar atalhos..."
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
					className="rounded-md border border-white/10 bg-black/20 pl-10 text-white placeholder:text-[#6B7280] focus:border-green-500 focus:ring-0"
					style={{ height: '2.5rem' }}
				/>
			</div>
			<div className="flex flex-wrap gap-2">
				<Button
					variant={selectedCategory === null ? 'default' : 'outline'}
					size="sm"
					onClick={() => setSelectedCategory(null)}
					className={`flex items-center gap-2 ${
						selectedCategory === null
							? 'mt-2 w-full border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 focus:ring-4 focus:ring-primary/30 sm:mt-0 sm:w-auto'
							: 'border border-primary/20 bg-black/10'
					}`}
				>
					Todas
				</Button>
				{categories.map((category) => (
					<Button
						key={category}
						variant={selectedCategory === category ? 'default' : 'outline'}
						size="sm"
						onClick={() => setSelectedCategory(category)}
						className={`flex items-center gap-2 ${
							selectedCategory === category
								? 'mt-2 w-full border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 focus:ring-4 focus:ring-primary/30 sm:mt-0 sm:w-auto'
								: 'border border-primary/20 bg-black/20'
						}`}
					>
						{CATEGORY_ICONS[category]}
						{category}
					</Button>
				))}
			</div>
		</div>
	);
}
