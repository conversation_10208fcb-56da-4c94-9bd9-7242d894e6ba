import { <PERSON>dal, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, Mo<PERSON>Header } from '@nextui-org/react';
import { Keyboard } from 'lucide-react';

import { Button } from '@/components/shadcnui/button';
import { useKeyboardShortcutsData } from '../../hooks/help/keyboard-shortcuts-data.hook';
import { useKeyboardShortcutsFilter } from '../../hooks/help/use-keyboard-shortcuts-filter.hook';
import { ShortcutsList } from './shortcuts-list';
import { ShortcutsSearchAndFilter } from './shortcuts-search-and-filter';

interface KeyboardShortcutsModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const KeyboardShortcutsModal: React.FC<KeyboardShortcutsModalProps> = ({ isOpen, onClose }) => {
	const { categorizedShortcuts, formatShortcut } = useKeyboardShortcutsData();

	const { searchTerm, setSearchTerm, selected<PERSON>ate<PERSON><PERSON>, setSelectedCategory, filteredShortcuts, categories, totalShortcuts } = useKeyboardShortcutsFilter({
		categorizedShortcuts,
		formatShortcut,
	});

	return (
		<Modal
			isOpen={isOpen}
			onOpenChange={onClose}
			size="5xl"
			className="bg-muted"
			scrollBehavior="inside"
			placement="center"
			backdrop="blur"
			classNames={{
				wrapper: 'overflow-hidden',
				base: 'rounded-xl border border-[#232728] bg-muted shadow-lg',
			}}
		>
			<ModalContent className="max-h-[90vh] max-w-4xl bg-muted p-2">
				<ModalHeader className="flex flex-col gap-2 pb-0">
					<div className="flex items-center gap-2">
						<Keyboard className="h-6 w-6 text-primary" />
						<h1 className="text-lg font-semibold text-white">Atalhos de Teclado</h1>
					</div>
					<p className="text-sm font-normal text-muted-foreground">Acelere seu fluxo de trabalho com estes atalhos úteis</p>
				</ModalHeader>

				<ModalBody className="pt-2">
					<div className="space-y-4">
						<ShortcutsSearchAndFilter
							searchTerm={searchTerm}
							setSearchTerm={setSearchTerm}
							selectedCategory={selectedCategory}
							setSelectedCategory={setSelectedCategory}
							categories={categories}
						/>

						<ShortcutsList filteredShortcuts={filteredShortcuts} formatShortcut={formatShortcut} />
					</div>
				</ModalBody>

				<ModalFooter className="border-t border-gray-800 pt-4">
					<div className="flex w-full items-center justify-between">
						<p className="text-sm text-gray-500">{totalShortcuts} atalhos disponíveis</p>
						<Button onClick={onClose} className={` `} size="sm">
							Fechar
						</Button>
					</div>
				</ModalFooter>
			</ModalContent>
		</Modal>
	);
};
