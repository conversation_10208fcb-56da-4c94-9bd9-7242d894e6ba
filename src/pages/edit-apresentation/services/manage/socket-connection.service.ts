import { io, ManagerOptions, Socket, SocketOptions } from 'socket.io-client';
import { ISocketConnection } from '../../types/socket/socket-connection.type';

export type SocketFactory = (url: string, opts?: Partial<ManagerOptions & SocketOptions>) => Socket;

export class SocketConnectionService implements ISocketConnection {
	private socket: Socket;

	constructor(
		private url: string,
		private socketFactory: SocketFactory = io,
	) {
		this.socket = this.socketFactory(this.url);
	}

	public connect(): void {
		this.socket.connect();
	}

	public on<T>(event: string, listener: (data: T) => void): void {
		this.socket.on(event, listener);
	}

	public off<T>(event: string, listener: (data: T) => void): void {
		this.socket.off(event, listener);
	}

	public emit(event: string, payload: unknown): void {
		this.socket.emit(event, payload);
	}

	public get<T>(event: string, payload?: unknown): Promise<T> {
		return new Promise((resolve, reject) => {
			this.socket.timeout(5000).emit(event, payload, (err: unknown, response: T) => {
				if (err) {
					return reject(err);
				}
				resolve(response);
			});
		});
	}

	public disconnect(): void {
		this.socket.disconnect();
	}

	public getStatus(): 'connected' | 'disconnected' | 'connecting' | 'reconnecting' {
		if (this.socket.connected) {
			return 'connected';
		} else if (this.socket.disconnected) {
			return 'disconnected';
		}
		return 'reconnecting';
	}
}
