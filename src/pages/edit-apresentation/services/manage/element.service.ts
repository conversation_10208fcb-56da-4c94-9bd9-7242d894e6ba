import { ICreateElementDto } from '../../dtos/elements/create-element.dto';
import { ISocketConnection } from '../../types/socket/socket-connection.type';
import { IElementService } from '../../types/socket/socket-elements.type';

export class ElementService implements IElementService {
	private readonly connection: ISocketConnection;

	constructor(connection: ISocketConnection) {
		this.connection = connection;
	}

	// public async createElement(payload: ICreateElementDto): Promise<void> {
	// 	this.connection.emit('createElement', payload);
	// }

	public async updateElement(payload: { elementId: string; updateElementDto: ICreateElementDto }): Promise<void> {
		this.connection.emit('updateElements', payload);
	}

	public async removeElement(payload: { elementId: string; presentationId: string }): Promise<void> {
		this.connection.emit('removeElement', payload);
	}
}
