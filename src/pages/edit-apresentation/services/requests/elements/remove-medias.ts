import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { ELEMENTS_ROUTES } from '../../endpoints';

export const removeMediaRequest = async ({ mediaIds, elementId }: { mediaIds: string[]; elementId: string }): Promise<ApiResponse<string>> => {
	try {
		const response = await api.delete(ELEMENTS_ROUTES.REMOVE_MEDIA({ mediaIds, elementId }));
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
