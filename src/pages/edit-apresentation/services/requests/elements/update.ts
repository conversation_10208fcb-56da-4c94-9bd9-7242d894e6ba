import { ICreateElementDto, IReturnCreateElementDto } from '@/pages/edit-apresentation/dtos/elements/create-element.dto';
import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { ELEMENTS_ROUTES } from '../../endpoints';

export const updateElementRequest = async ({ id, items }: { id: string; items: ICreateElementDto }): Promise<ApiResponse<IReturnCreateElementDto>> => {
	try {
		const response = await api.patch(ELEMENTS_ROUTES.UPDATE({ id }), items);
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
