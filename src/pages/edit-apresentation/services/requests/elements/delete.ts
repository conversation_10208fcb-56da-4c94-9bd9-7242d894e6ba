import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { ELEMENTS_ROUTES } from '../../endpoints';

export const deleteElementRequest = async ({ id }: { id: string }): Promise<ApiResponse<void>> => {
	try {
		const response = await api.delete(ELEMENTS_ROUTES.DELETE({ id }));
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
