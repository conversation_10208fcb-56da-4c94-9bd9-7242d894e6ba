import { ICreateElementDto, IReturnCreateElementDto } from '@/pages/edit-apresentation/dtos/elements/create-element.dto';
import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { ELEMENTS_ROUTES } from '../../endpoints';

export const createElementRequest = async ({ items }: { items: ICreateElementDto }): Promise<ApiResponse<IReturnCreateElementDto>> => {
	try {
		const response = await api.post<IReturnCreateElementDto>(ELEMENTS_ROUTES.CREATE, items);
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
