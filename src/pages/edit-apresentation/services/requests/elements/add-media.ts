import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { ELEMENTS_ROUTES } from '../../endpoints';

export interface AddMediaParams {
	id: string;
	files: File;
}

export interface IAddMediaResponse {
	mediaTempToken: string;
	medias: IMediaRequest[];
	mediaTempTokenExpireAt: number;
}

export interface IMediaRequest {
	id: string;
	createdAt: string;
	updatedAt: string;
	pathConfigurationId: string;
	filename: string;
	originalFilename: string;
	size: string;
	mimetype: string;
	url: string;
}

export const addMediaRequest = async ({ id, files }: AddMediaParams): Promise<ApiResponse<IAddMediaResponse>> => {
	try {
		const res = await api.postForm<IAddMediaResponse>(ELEMENTS_ROUTES.ADD_MEDIA({ id }), {
			files: files,
		});
		return {
			success: true,
			data: res.data,
			status: res.status,
		};
	} catch (error) {
		console.error('Erro ao adicionar mídia', error);
		return resolveGlobalErrors(error);
	}
};
