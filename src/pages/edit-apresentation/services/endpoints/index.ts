export const ELEMENTS_ROUTES = {
	CREATE: '/elements',
	FIND_ALL: '/elements',
	FIND_BY_ID: ({ id }: { id: string }) => `/elements/${id}`,
	UPDATE: ({ id }: { id: string }) => `/elements/${id}`,
	DELETE: ({ id }: { id: string }) => `/elements/${id}`,
	ADD_MEDIA: ({ id }: { id: string }) => `/elements/media/${id}`,
	REMOVE_MEDIA: ({ mediaIds, elementId }: { mediaIds: string[]; elementId: string }) =>
		`/elements/media/${elementId}?mediaIds=${mediaIds.join(',')}&elementId=${elementId}`,
} as const;
