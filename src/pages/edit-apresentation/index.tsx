import LoadingPage from '@/templates/loading';
import { useRef } from 'react';
import { useParams } from 'react-router-dom';
import { CanvasEditorWrapper } from './components/canvas-editor';
import { GlobalContextMenu } from './components/cursors/global-context-menu';
import LeftLiveSideBar from './components/left-side-bar';
import { MobileWarning } from './components/mobile-warning';
import { NavActions } from './components/nav-actions';
import RightLiveSideBar from './components/right-side-bar';
import { useSetupPresentation } from './hooks/presentation/setup-presentation.hook';

const PresentationEditor = () => {
	const { id } = useParams();
	const { isLoading, success, errorMessage } = useSetupPresentation(id!);
	const propertiesPanelRef = useRef<HTMLDivElement>(null);
	if (isLoading) return <LoadingPage />;
	if (!success) throw new Error(errorMessage);

	return (
		<main className="bg-muted">
			<div className="flex h-screen w-full flex-col bg-gradient-to-b from-background/50 to-background/95">
				<NavActions />
				<section className="w-full flex-1 overflow-hidden lg:hidden">
					<MobileWarning />
				</section>
				<section className="hidden w-full flex-1 overflow-hidden lg:flex">
					<LeftLiveSideBar />
					<div className="relative flex-1 items-center justify-center overflow-hidden">
						<CanvasEditorWrapper propertiesPanelRef={propertiesPanelRef} />
					</div>
					<RightLiveSideBar propertiesPanelRef={propertiesPanelRef} />
				</section>
				<GlobalContextMenu />
			</div>
		</main>
	);
};

export default PresentationEditor;
