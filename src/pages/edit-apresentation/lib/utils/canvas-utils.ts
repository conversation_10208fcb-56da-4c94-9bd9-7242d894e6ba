import { InteractionType } from '@/pages/edit-apresentation/types/cursor/interaction.type';
import { IElementToAdd } from '@/shared/states/items/next-items.state';

export const determineInteraction = (movingItem: boolean, nextElement: IElementToAdd | null, selectionMode: boolean): InteractionType => {
	if (movingItem) return 'dragging';
	if (nextElement) return 'drawing';
	if (selectionMode) return 'selecting';
	return null;
};

export const getCursorClass = (selectionBox: boolean, nextCanvasElement: IElementToAdd | null): string => {
	if (selectionBox) {
		return 'cursor-crosshair';
	} else if (nextCanvasElement) {
		return nextCanvasElement.type === 'text' ? 'cursor-text' : 'cursor-crosshair';
	}
	return '';
};
