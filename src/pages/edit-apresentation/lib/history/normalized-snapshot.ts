import { IItem, ITEM_STATUS } from '../../types/item.type';

export const normalizeUndoSnapshot = ({
	previousSnapshot,
	currentSnapshot,
	nextSnapshot,
}: {
	previousSnapshot: IItem[];
	currentSnapshot: IItem[];
	nextSnapshot: IItem[];
}): IItem[] => {
	type TransformationCase = (prevItem: IItem, _currentItem?: IItem, _nextSnapshot?: IItem[]) => IItem;

	const transformationCases: Partial<Record<ITEM_STATUS, TransformationCase>> = {
		[ITEM_STATUS.DELETED]: (prevItem, currentItem) => (currentItem ? { ...currentItem, status: ITEM_STATUS.NEW } : prevItem),
	};

	const processItem = (prevItem: IItem): IItem => {
		if (prevItem.status === ITEM_STATUS.NEW) {
			const futureItem = (nextSnapshot || []).find((item) => item.tempId === prevItem.tempId);
			return {
				...prevItem,
				id: futureItem ? futureItem.id : prevItem.id,
				status: ITEM_STATUS.DELETED,
			};
		}

		const currentItem = currentSnapshot?.find((cur) => cur.tempId === prevItem.tempId);

		if (currentItem && transformationCases[currentItem.status]) {
			return transformationCases[currentItem.status]!(prevItem, currentItem, nextSnapshot);
		}

		return prevItem;
	};

	return previousSnapshot.map(processItem);
};
