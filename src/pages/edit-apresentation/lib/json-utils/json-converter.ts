export interface IJsonConverter {
	stringToJson<T>(jsonString: string): T;
	jsonToString<T>(json: T): string;
}

export class JsonConversionError extends Error {
	constructor(message: string) {
		super(message);
		this.name = 'JsonConversionError';
	}
}

export class JsonConverter implements IJsonConverter {
	public stringToJson<T>(jsonString: string): T {
		if (!this.isValidString(jsonString)) {
			throw new JsonConversionError('A string JSON informada é inválida ou vazia.');
		}

		try {
			const parsed = JSON.parse(jsonString);
			return parsed as T;
		} catch (error) {
			throw new JsonConversionError(`Falha ao converter para JSON: ${(error as Error).message}`);
		}
	}

	public jsonToString<T>(json: T): string {
		if (json === undefined || json === null) {
			throw new JsonConversionError('O objeto informado é inválido (nulo ou indefinido).');
		}

		try {
			return JSON.stringify(json);
		} catch (error) {
			throw new JsonConversionError(`Falha ao converter para string JSON: ${(error as Error).message}`);
		}
	}

	private isValidString(value: string): boolean {
		return typeof value === 'string' && value.trim().length > 0;
	}
}
