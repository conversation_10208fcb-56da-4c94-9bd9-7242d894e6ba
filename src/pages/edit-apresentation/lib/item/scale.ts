export interface IScaleTransformer {
	normalize(value: number): number;
	apply(value: number): number;
}

export class ScaleTransformer implements IScaleTransformer {
	private readonly scale: number;

	constructor(scale: number) {
		if (scale === 0) {
			throw new Error('A escala não pode ser zero.');
		}
		this.scale = scale;
	}

	normalize(value: number): number {
		return value / this.scale;
	}

	apply(value: number): number {
		return value * this.scale;
	}
}
