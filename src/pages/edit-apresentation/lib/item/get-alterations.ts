import { IItem } from '../../types/item.type';

function areItemsEqual(item1: IItem, item2: IItem): boolean {
	return (
		item1.status === item2.status &&
		item1.name === item2.name &&
		item1.type === item2.type &&
		item1.position.x === item2.position.x &&
		item1.position.y === item2.position.y &&
		item1.size.width === item2.size.width &&
		item1.size.height === item2.size.height &&
		item1.content === item2.content &&
		item1.layer === item2.layer
	);
}

export function diffItems(newItems: IItem[], oldItems: IItem[]): IItem[] {
	return newItems.filter((newItem) => {
		const oldItem = oldItems.find((item) => item.tempId === newItem.tempId);
		return !oldItem || !areItemsEqual(newItem, oldItem);
	});
}
