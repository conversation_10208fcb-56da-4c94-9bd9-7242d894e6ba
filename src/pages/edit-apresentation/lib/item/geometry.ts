export interface IRectangle {
	x: number;
	y: number;
	width: number;
	height: number;
}

export interface IPositionElement {
	position: { x: number; y: number };
	size: { width: number; height: number };
}

const toRectangle = (element: IPositionElement): IRectangle => ({
	x: element.position.x,
	y: element.position.y,
	width: element.size.width,
	height: element.size.height,
});

export const intersects = (item: IPositionElement, box: IRectangle) => {
	const itemRect = toRectangle(item);

	const itemRight = itemRect.x + itemRect.width;
	const itemBottom = itemRect.y + itemRect.height;
	const boxRight = box.x + box.width;
	const boxBottom = box.y + box.height;

	const noIntersect = itemRect.x > boxRight || itemRight < box.x || itemRect.y > boxBottom || itemBottom < box.y;

	return !noIntersect;
};
