import { useAuthCheck } from '@/contexts/auth/hooks/auth-check.hook';
import { userState } from '@/contexts/auth/states/user.state';
import { useAuthEvents } from '@/hooks/use-auth-events';
import { useConnection } from '@/hooks/use-connection.ts';
import { Devices } from '@/pages/devices';
import PresentationEditor from '@/pages/edit-apresentation';
import Home from '@/pages/home';
import { Login } from '@/pages/login';
import PresentationPage from '@/pages/presentation';
import Presentations from '@/pages/presentations';
import ProgrammingPage from '@/pages/programing';
import LoadingPage from '@/templates/loading';
import LostPage from '@/templates/lost';
import { MainPage } from '@/templates/main-page';
import WithoutConnection from '@/templates/without-connection';
import { useAtomValue } from 'jotai';
import { Suspense } from 'react';
import { BrowserRouter, Outlet, Route, Routes } from 'react-router-dom';
import { toast } from 'sonner';

function NavigationContent() {
	const { isConnected } = useConnection();
	const { isLoading } = useAuthCheck();
	const user = useAtomValue(userState);

	useAuthEvents({
		'auth:error': ({ status }) => {
			toast.dismiss();
			toast.error(status === 403 ? 'Acesso negado: Recurso não autorizado' : 'Erro de autenticação');
		},
		'auth:refresh-failed': ({ attempts }) => {
			toast.dismiss();
			toast.error(`Falha ao renovar token (tentativa ${attempts})`);
		},
		'auth:token-expired': () => {
			toast.dismiss();
			toast.error('Sua sessão expirou');
		},
	});

	if (!isConnected) return <WithoutConnection />;
	if (isLoading) return <LoadingPage />;
	if (!user) return <Login />;

	return (
		<Routes>
			<Route
				element={
					<Suspense fallback={<LoadingPage />}>
						<Outlet />
					</Suspense>
				}
			>
				<Route path="/" element={<MainPage />}>
					<Route path="home" element={<Home />} />
					<Route path="devices" element={<Devices />} />
					<Route path="presentations" element={<Presentations />} />
					<Route path="programacao" element={<ProgrammingPage />} />
				</Route>
				<Route path="new/:id" element={<PresentationEditor />} />
				<Route path="apresentacao/:token" element={<PresentationPage />} />
			</Route>
			<Route path="*" element={<LostPage />} />
		</Routes>
	);
}

export default function NavigationProvider() {
	return (
		<BrowserRouter>
			<NavigationContent />
		</BrowserRouter>
	);
}
