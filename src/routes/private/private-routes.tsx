// import LoadingPage from '@/templates/loading';
// import { Suspense } from 'react';
// import { Outlet } from 'react-router-dom';

// export const PrivateRoutes = () => {
// 	// const { signed, isLoading, refreshToken } = useContext(AuthContext);
// 	// const { token } = useToken();
// 	// const { isSessionExpiring, timeRemaining } = useSessionExpiration(token!);

// 	// const modalIsSessionExpiring = useDisclosure();
// 	// useEffect(() => {
// 	// 	if (isSessionExpiring) return modalIsSessionExpiring.onOpen();
// 	// }, [isSessionExpiring, modalIsSessionExpiring]);

// 	// function handleRefreshAndCloseModal() {
// 	// 	refreshToken();
// 	// 	modalIsSessionExpiring.onClose();
// 	// 	return setTimeout(() => {
// 	// 		return window.location.reload();
// 	// 	}, 500);
// 	// }
// 	// if (isLoading) return <LoadingPage />;
// 	// if (!signed && !isLoading) return <Login />;

// 	return (
// 		<Suspense fallback={<LoadingPage />}>
// 			<Outlet />

// 			{/* <Modal backdrop="blur" isOpen={modalIsSessionExpiring.isOpen} onOpenChange={modalIsSessionExpiring.onOpenChange}>
// 				<ModalContent>
// 					<ModalHeader>
// 						<h1>Sua sessão irá expirar</h1>
// 					</ModalHeader>
// 					<ModalBody>
// 						<p className="text-pretty">Tempo restante: {timeRemaining} segundos</p>
// 					</ModalBody>
// 					<ModalFooter>
// 						<Button onClick={handleRefreshAndCloseModal}>Manter conectado</Button>
// 					</ModalFooter>
// 				</ModalContent>
// 			</Modal> */}
// 		</Suspense>
// 	);
// };
