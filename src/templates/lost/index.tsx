import { Button } from '@/components/shadcnui/button';
import Particles from '@/components/shadcnui/particles';
import { Image } from '@nextui-org/react';
import { useNavigate } from 'react-router-dom';

export default function LostPage() {
	const navigate = useNavigate();

	return (
		<div className="flex h-screen w-full flex-col items-center justify-center gap-4">
			<Image src={'/assets/svgs/lost.svg'} alt="lost" width={600} />
			<div className="z-[1001!important] flex items-center gap-4">
				<Button size={'lg'} onClick={() => navigate('/')}>
					Início
				</Button>
				<Button variant={'secondary'} size={'lg'} onClick={() => navigate(-1)}>
					Voltar onde eu estava
				</Button>
			</div>
			<Particles className="absolute inset-0" quantity={200} refresh />
		</div>
	);
}
