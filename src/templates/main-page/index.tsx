import { Navbar } from '@/components/shadcnui/navbar';
import { DetailedHTMLProps, HTMLAttributes } from 'react';
import { Outlet } from 'react-router-dom';
import { twMerge } from 'tailwind-merge';

interface MainPageProps extends DetailedHTMLProps<HTMLAttributes<HTMLElement>, HTMLElement> {}
export function MainPage({ className, ...rest }: MainPageProps) {
	return (
		<main className={twMerge('relative mx-auto flex min-h-screen w-full flex-col overflow-hidden bg-muted', className)} {...rest}>
			<Navbar />
			<div className="flex-1 overflow-auto bg-gradient-to-b from-background/50 to-background/95">
				<div className="mx-auto w-full max-w-[1440px] space-y-4 px-2 py-4 sm:space-y-6 sm:px-4 sm:py-6 md:space-y-8 md:px-8 md:py-8">
					<Outlet />
				</div>
			</div>
			<div className="pointer-events-none fixed inset-0 z-30 bg-gradient-to-t from-[#fff]/0 via-[#fff]/0 to-[#fff]/5 opacity-30" />
			<div className="pointer-events-none absolute bottom-0 h-px w-full bg-gradient-to-r from-transparent via-green-400/0 to-transparent" />
		</main>
	);
}
