import { motion } from 'framer-motion';
import './loader.css';

export default function LoadingPage() {
	return (
		// <div className=" relative flex min-h-screen flex-col place-items-center items-center justify-center self-center">
		// 	<div className="loader h-[36px] w-[36px]" />
		// 	<div className="scanner">
		// 		<span>Carregando...</span>
		// 	</div>
		// </div>

		<div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
			<motion.div className="loader h-[36px] w-[36px]" animate={{ rotate: 360 }} transition={{ ease: 'linear', repeat: Infinity, duration: 1 }} />
		</div>
	);
}
