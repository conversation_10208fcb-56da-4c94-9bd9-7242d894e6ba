import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { AUTH_ROUTES } from '../endpoints/auth';

export const logoutRequest = async ({ token }: { token: string }): Promise<ApiResponse<boolean>> => {
	try {
		const { data, status } = await api.post(AUTH_ROUTES.LOGOUT, {
			token,
		});
		return { data, status, success: true };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
