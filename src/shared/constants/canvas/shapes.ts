// import { ElementDirection, ImageUpload, ModifyShape } from '@/shared/interfaces/canvas';
// import * as fabric from 'fabric';
// import { v4 as uuidv4 } from 'uuid';

// export const createRectangle = (pointer: PointerEvent) => {
// 	const rect = new fabric.Rect({
// 		left: pointer.x,
// 		top: pointer.y,
// 		width: 100,
// 		height: 100,
// 		fill: '#aabbcc',
// 		objectId: uuidv4(),
// 	} as fabric.TOptions<fabric.Rect>);

// 	return rect;
// };

// export const createTriangle = (pointer: PointerEvent) => {
// 	return new fabric.Triangle({
// 		left: pointer.x,
// 		top: pointer.y,
// 		width: 100,
// 		height: 100,
// 		fill: '#aabbcc',
// 		objectId: uuidv4(),
// 	} as fabric.TOptions<fabric.Triangle>);
// };

// export const createCircle = (pointer: PointerEvent) => {
// 	return new fabric.Circle({
// 		left: pointer.x,
// 		top: pointer.y,
// 		radius: 100,
// 		fill: '#aabbcc',
// 		objectId: uuidv4(),
// 	} as fabric.TOptions<fabric.Circle>);
// };

// export const createLine = (pointer: PointerEvent) => {
// 	return new fabric.Line([pointer.x, pointer.y, pointer.x + 100, pointer.y + 100], {
// 		stroke: '#aabbcc',
// 		strokeWidth: 2,
// 		objectId: uuidv4(),
// 	} as fabric.TOptions<fabric.Line>);
// };

// export const createText = (pointer: PointerEvent, text: string) => {
// 	return new fabric.IText(text, {
// 		left: pointer.x,
// 		top: pointer.y,
// 		fill: '#aabbcc',
// 		fontFamily: 'Helvetica',
// 		fontSize: 36,
// 		fontWeight: '400',
// 		objectId: uuidv4(),
// 	} as fabric.TOptions<fabric.FabricText>);
// };

// export const createSpecificShape = (shapeType: string, pointer: PointerEvent) => {
// 	switch (shapeType) {
// 		case 'rectangle':
// 			return createRectangle(pointer);

// 		case 'triangle':
// 			return createTriangle(pointer);

// 		case 'circle':
// 			return createCircle(pointer);

// 		case 'line':
// 			return createLine(pointer);

// 		case 'text':
// 			return createText(pointer, 'Tap to Type');

// 		default:
// 			return null;
// 	}
// };

// export const handleImageUpload = ({ file, canvas, shapeRef, syncShapeInStorage }: ImageUpload) => {
// 	const reader = new FileReader();

// 	reader.onload = () => {
// 		fabric.FabricImage.fromURL(reader.result as string, (img) => {
// 			img.scaleToWidth(200);
// 			img.scaleToHeight(200);

// 			canvas.current.add(img);

// 			// @ts-ignore
// 			img.objectId = uuidv4();

// 			shapeRef.current = img;

// 			syncShapeInStorage(img);
// 			canvas.current.requestRenderAll();
// 		});
// 	};

// 	reader.readAsDataURL(file);
// };

// export const createShape = (canvas: fabric.Canvas, pointer: PointerEvent, shapeType: string) => {
// 	if (shapeType === 'freeform') {
// 		canvas.isDrawingMode = true;
// 		return null;
// 	}

// 	return createSpecificShape(shapeType, pointer);
// };

// export const modifyShape = ({ canvas, property, value, activeObjectRef, syncShapeInStorage }: ModifyShape) => {
// 	const selectedElement = canvas.getActiveObject();

// 	if (!selectedElement || selectedElement?.type === 'activeSelection') return;

// 	// if  property is width or height, set the scale of the selected element
// 	if (property === 'width') {
// 		selectedElement.set('scaleX', 1);
// 		selectedElement.set('width', value);
// 	} else if (property === 'height') {
// 		selectedElement.set('scaleY', 1);
// 		selectedElement.set('height', value);
// 	} else {
// 		if (selectedElement[property as keyof object] === value) return;
// 		selectedElement.set(property as keyof object, value);
// 	}

// 	// set selectedElement to activeObjectRef
// 	activeObjectRef.current = selectedElement;

// 	syncShapeInStorage(selectedElement);
// };

// export const bringElement = ({ canvas, direction, syncShapeInStorage }: ElementDirection) => {
// 	if (!canvas) return;

// 	// get the selected element. If there is no selected element or there are more than one selected element, return
// 	const selectedElement = canvas.getActiveObject();

// 	if (!selectedElement || selectedElement?.type === 'activeSelection') return;

// 	// bring the selected element to the front
// 	if (direction === 'front') {
// 		canvas.bringObjectToFront(selectedElement);
// 	} else if (direction === 'back') {
// 		canvas.sendObjectToBack(selectedElement);
// 	}

// 	// canvas.renderAll();
// 	syncShapeInStorage(selectedElement);

// 	// re-render all objects on the canvas
// };
