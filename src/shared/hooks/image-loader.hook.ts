import axios from 'axios';
import { useEffect, useState } from 'react';

interface UseImageLoaderProps {
	imageUrl?: string;
	token?: string;
}

export const useImageLoader = ({ imageUrl, token }: UseImageLoaderProps) => {
	const [blobUrl, setBlobUrl] = useState<string | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		let revoked = false;
		let localBlobUrl: string | null = null;

		if (imageUrl && token) {
			setIsLoading(true);
			setError(null);
			const fetchImage = async () => {
				try {
					const res = await axios.get(imageUrl, {
						responseType: 'blob',
						headers: {
							Authorization: `Bearer ${token}`,
						},
					});

					if (!revoked) {
						localBlobUrl = URL.createObjectURL(res.data);
						setBlobUrl(localBlobUrl);
						setError(null);
					}
				} catch (err) {
					console.error('Erro ao carregar imagem:', err);

					if (!revoked) {
						console.error('Erro ao carregar imagem:', err);
						setError(`Erro ao carregar imagem: ${err}`);
					}
				} finally {
					if (!revoked) {
						setIsLoading(false);
					}
				}
			};

			fetchImage();
		}

		return () => {
			revoked = true;
			if (localBlobUrl) URL.revokeObjectURL(localBlobUrl);
			setBlobUrl(null);
		};
	}, [imageUrl, token]);

	return { blobUrl, error, isLoading };
};
