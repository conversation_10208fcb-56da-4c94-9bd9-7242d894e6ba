import { SocketConnectionService } from '@/pages/edit-apresentation/services/manage/socket-connection.service';
import { ISocketConnection } from '@/pages/edit-apresentation/types/socket/socket-connection.type';
import { socketStatusAtom } from '@/shared/states/socket/socket-status.state';
import { useSetAtom } from 'jotai';
import { useEffect, useMemo, useRef } from 'react';

const STATUS_INTERVAL_MS = 5000;

const subscribeToSocketEvents = (
	socketService: ISocketConnection,
	setSocketStatus: (status: 'connected' | 'disconnected' | 'connecting' | 'reconnecting') => void,
): void => {
	const eventHandlers: Record<string, () => void> = {
		connect: () => setSocketStatus('connected'),
		disconnect: () => setSocketStatus('disconnected'),
		connect_error: () => setSocketStatus('connecting'),
		reconnect_attempt: () => setSocketStatus('reconnecting'),
	};

	Object.entries(eventHandlers).forEach(([event, handler]) => {
		socketService.on(event, handler);
	});
};

export const usePageSocket = (baseUrl: string): ISocketConnection => {
	const setSocketStatus = useSetAtom(socketStatusAtom);
	const reconnectAttemptsRef = useRef<number>(0);
	const MAX_RECONNECT_ATTEMPTS = 5;

	const socketService = useMemo(() => new SocketConnectionService(baseUrl), [baseUrl]);

	useEffect(() => {
		socketService.connect();
		setSocketStatus(socketService.getStatus());

		const handleDisconnect = (): void => {
			if (reconnectAttemptsRef.current < MAX_RECONNECT_ATTEMPTS) {
				reconnectAttemptsRef.current += 1;
				console.info(`Tentativa de reconexão socket ${reconnectAttemptsRef.current}/${MAX_RECONNECT_ATTEMPTS}`);
				setTimeout(
					() => {
						socketService.connect();
					},
					1000 * Math.min(reconnectAttemptsRef.current, 5),
				);
			}
		};

		const handleConnect = (): void => {
			reconnectAttemptsRef.current = 0;
		};

		socketService.on('disconnect', handleDisconnect);
		socketService.on('connect', handleConnect);

		subscribeToSocketEvents(socketService, setSocketStatus);

		const statusInterval = setInterval(() => {
			const currentStatus = socketService.getStatus();
			setSocketStatus(currentStatus);

			if (currentStatus === 'disconnected') {
				handleDisconnect();
			}
		}, STATUS_INTERVAL_MS);

		return () => {
			clearInterval(statusInterval);
			socketService.off('disconnect', handleDisconnect);
			socketService.off('connect', handleConnect);
			socketService.disconnect();
		};
	}, [socketService, setSocketStatus]);

	return socketService;
};
