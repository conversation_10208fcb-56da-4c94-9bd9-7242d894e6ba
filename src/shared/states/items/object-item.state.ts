import { atom } from 'jotai';
import { IItem } from '../../../pages/edit-apresentation/types/item.type';

export const itemsAtom = atom<IItem[]>([]);
export const selectedItemsIdsAtom = atom<string[]>([]);

export const itemsSelecteds = atom((get) => {
	const items = get(itemsAtom);
	const selectedIds = get(selectedItemsIdsAtom);
	return items.filter((item) => selectedIds.includes(item.tempId));
});

export const updateLayers = (items: IItem[]): IItem[] => {
	return items.map((item, index) => ({
		...item,
		layer: items.length - index,
	}));
};

export const updateItemAtom = atom(null, (get, set, updatedItem: IItem) => {
	const current = get(itemsAtom);
	const next = current.map((item) => (item.tempId === updatedItem.tempId ? updatedItem : item));
	set(itemsAtom, next);
});

export const removeItemAtom = atom(null, (get, set, itemId: string) => {
	const current = get(itemsAtom);
	const next = current.filter((item) => item.tempId !== itemId);
	set(itemsAtom, next);
});
