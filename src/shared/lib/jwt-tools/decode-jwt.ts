interface JWTPayload {
	[key: string]: string | number | boolean | null | object;
}

export const decodeJWT = (token: string | null): JWTPayload | null => {
	try {
		if (!token) return null;
		const base64Url = token.split('.')[1];
		const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
		const jsonPayload = decodeURIComponent(
			atob(base64)
				.split('')
				.map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
				.join(''),
		);
		return JSON.parse(jsonPayload);
	} catch (error) {
		console.error('O JWT informado é inválido:', error);
		return null;
	}
};
