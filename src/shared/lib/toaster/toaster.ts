import { toast } from 'sonner';

interface IToaster {
	success(message: string): void;
	error(message: string): void;
	info(message: string): void;
}

class SonnerToaster implements IToaster {
	success(message: string): void {
		toast.dismiss();
		toast.success(message);
	}
	error(message: string): void {
		toast.dismiss();
		toast.error(message);
	}
	info(message: string): void {
		toast.dismiss();
		toast.info(message);
	}
}

export const toaster: IToaster = new SonnerToaster();
