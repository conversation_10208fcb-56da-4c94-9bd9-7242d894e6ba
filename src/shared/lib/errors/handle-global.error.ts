import { IErrorResponse } from '@/shared/types/response';
import axios, { AxiosError } from 'axios';

const createDefaultErrorResponse = (): IErrorResponse => {
	return {
		success: false,
		data: {
			message: 'Ocorreu um erro desconhecido',
		},
		status: 0,
	};
};

const parseAxiosResponseError = (error: AxiosError): IErrorResponse => {
	const { status, data, config } = error.response!;

	if (status === 403) {
		return {
			success: false,
			data: {
				message: 'Acesso negado: Recurso não autorizado',
				method: config.method,
				url: config.url,
			},
			status,
		};
	}

	if (typeof data === 'string' && data.startsWith('<!DOCTYPE html>')) {
		return {
			success: false,
			data: {
				message: 'Ocorreu um erro de servidor',
				method: config.method,
				url: config.url,
			},
			status,
		};
	}

	let message: string;

	if (data && typeof data === 'object' && 'message' in data) {
		const rawMessage = data.message;
		if (Array.isArray(rawMessage)) {
			message = rawMessage.join(', ');
		} else if (typeof rawMessage === 'string') {
			message = rawMessage;
		} else {
			message = 'Ocorreu um erro desconhecido';
		}
	} else if (typeof data === 'string') {
		message = data;
	} else {
		message = 'Ocorreu um erro desconhecido';
	}
	return {
		success: false,
		data: {
			message,
			method: config.method,
			url: config.url,
		},
		status,
	};
};

const parseAxiosRequestError = (error: AxiosError): IErrorResponse => {
	const { url, method } = error.request as { url: string; method: string };

	return {
		success: false,
		data: {
			message: 'O servidor não respondeu',
			method,
			url,
		},
		status: 0,
	};
};

const parseAxiosError = (error: AxiosError): IErrorResponse => {
	if (error.response) return parseAxiosResponseError(error);
	if (error.request) return parseAxiosRequestError(error);
	return createDefaultErrorResponse();
};

const parseGeneralError = (error: Error): IErrorResponse => {
	return {
		success: false,
		data: {
			message: error.message ?? 'Ocorreu um erro desconhecido',
		},
		status: 0,
	};
};

export const resolveGlobalErrors = (error: unknown): IErrorResponse => {
	if (axios.isAxiosError(error)) return parseAxiosError(error);
	if (error instanceof Error) return parseGeneralError(error);
	return createDefaultErrorResponse();
};
