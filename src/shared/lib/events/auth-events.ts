type AuthEventType = 'auth:error' | 'auth:refresh-failed' | 'auth:token-expired' | 'auth:logout';

type AuthEventPayload = {
	type: AuthEventType;
	error?: Error;
	attempts?: number;
	status?: number;
};

type AuthEventListener = (payload: AuthEventPayload) => void;

class AuthEventEmitter {
	private static readonly listeners: Map<AuthEventType, Set<AuthEventListener>> = new Map();

	static subscribe(event: AuthEventType, listener: AuthEventListener): () => void {
		if (!this.listeners.has(event)) {
			this.listeners.set(event, new Set());
		}

		this.listeners.get(event)?.add(listener);

		return () => {
			this.listeners.get(event)?.delete(listener);
		};
	}

	static emit(event: AuthEventType, payload: Omit<AuthEventPayload, 'type'>): void {
		this.listeners.get(event)?.forEach((listener) => {
			listener({ type: event, ...payload });
		});
	}
}

export { AuthEventEmitter, type AuthEventPayload, type AuthEventType };
