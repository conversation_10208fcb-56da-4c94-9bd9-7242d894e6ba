import { cookies } from '../clients/cookies-client';
import { AUTH_CONFIG } from './auth-config';

export const setAuthCookie = (token: string): void => {
	cookies.set(AUTH_CONFIG.TOKEN_NAME, token, {
		path: '/',
		sameSite: 'strict',
		secure: import.meta.env.PROD,
		expires: new Date(Date.now() + AUTH_CONFIG.TOKEN_EXPIRY),
	});
};

export const removeAuthCookie = (): void => {
	cookies.remove(AUTH_CONFIG.TOKEN_NAME);
};

export const getAuthCookie = (): string | undefined => {
	return cookies.get(AUTH_CONFIG.TOKEN_NAME);
};
