import { AUTH_CONFIG } from './auth-config';

export class RefreshTokenManager {
	private static attempts = 0;

	static resetAttempts(): void {
		this.attempts = 0;
	}

	static incrementAttempts(): number {
		return ++this.attempts;
	}

	static getAttempts(): number {
		return this.attempts;
	}

	static getBackoffDelay(): number {
		const delay = AUTH_CONFIG.BASE_DELAY * Math.pow(2, this.attempts - 1);
		const jitter = Math.random() * 100;
		return Math.min(delay + jitter, AUTH_CONFIG.MAX_DELAY);
	}
}
