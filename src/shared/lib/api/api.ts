import axios from 'axios';
import { handleAuthError } from '../auth';
import { cookies } from '../clients/cookies-client';

export const BaseURL = import.meta.env.VITE_BASE_URL;
const api = axios.create({
	baseURL: BaseURL,
});

api.interceptors.request.use(
	async (config) => {
		const token = await cookies.get('access-token-PSH');
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		return config;
	},
	(error) => {
		return Promise.reject(error instanceof Error ? error : new Error(String(error)));
	},
);

api.interceptors.response.use((response) => response, handleAuthError);

export default api;
