import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AuthCheckService } from '../auth-check.service';
import { CurrentUser } from '@/shared/interfaces/users/current-user';

// Mock das dependências
vi.mock('@/shared/lib/jwt-tools/get-expiration-time', () => ({
	getJWTExpiration: vi.fn(),
}));

vi.mock('../../hooks/login-mutation.hook', () => ({
	buildUserObject: vi.fn(),
}));

import { getJWTExpiration } from '@/shared/lib/jwt-tools/get-expiration-time';
import { buildUserObject } from '../../hooks/login-mutation.hook';

describe('AuthCheckService', () => {
	const mockSetUser = vi.fn();
	const mockSetExpiration = vi.fn();
	const mockUser: CurrentUser = {
		name: '<PERSON>',
		email: '<EMAIL>',
		roles: [1, 2],
	};
	const mockToken = 'valid.jwt.token';
	const mockExpiration = new Date('2024-12-31');

	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('authenticateUser', () => {
		it('deve autenticar usuário com token válido', () => {
			// Arrange
			vi.mocked(buildUserObject).mockReturnValue(mockUser);
			vi.mocked(getJWTExpiration).mockReturnValue(mockExpiration);

			// Act
			AuthCheckService.authenticateUser({
				token: mockToken,
				setUser: mockSetUser,
				setExpiration: mockSetExpiration,
			});

			// Assert
			expect(buildUserObject).toHaveBeenCalledWith(mockToken);
			expect(getJWTExpiration).toHaveBeenCalledWith(mockToken);
			expect(mockSetUser).toHaveBeenCalledWith(mockUser);
			expect(mockSetExpiration).toHaveBeenCalledWith(mockExpiration);
		});

		it('deve limpar estados quando token é inválido', () => {
			// Arrange
			vi.mocked(buildUserObject).mockImplementation(() => {
				throw new Error('Token inválido');
			});

			// Act & Assert
			expect(() => {
				AuthCheckService.authenticateUser({
					token: 'invalid-token',
					setUser: mockSetUser,
					setExpiration: mockSetExpiration,
				});
			}).toThrow('Token inválido');

			expect(mockSetUser).toHaveBeenCalledWith(null);
			expect(mockSetExpiration).toHaveBeenCalledWith(undefined);
		});

		it('deve lançar erro quando token não é fornecido', () => {
			// Act & Assert
			expect(() => {
				AuthCheckService.authenticateUser({
					token: '',
					setUser: mockSetUser,
					setExpiration: mockSetExpiration,
				});
			}).toThrow('Token inválido ou não fornecido');
		});
	});

	describe('clearAuthenticationState', () => {
		it('deve limpar estados de autenticação', () => {
			// Act
			AuthCheckService.clearAuthenticationState(mockSetUser, mockSetExpiration);

			// Assert
			expect(mockSetUser).toHaveBeenCalledWith(null);
			expect(mockSetExpiration).toHaveBeenCalledWith(undefined);
		});
	});

	describe('isValidToken', () => {
		it('deve retornar true para token válido', () => {
			// Act & Assert
			expect(AuthCheckService.isValidToken('valid-token')).toBe(true);
		});

		it('deve retornar false para token undefined', () => {
			// Act & Assert
			expect(AuthCheckService.isValidToken(undefined)).toBe(false);
		});

		it('deve retornar false para token vazio', () => {
			// Act & Assert
			expect(AuthCheckService.isValidToken('')).toBe(false);
		});

		it('deve retornar false para token apenas com espaços', () => {
			// Act & Assert
			expect(AuthCheckService.isValidToken('   ')).toBe(false);
		});
	});
});
