import { CurrentUser } from '@/shared/interfaces/users/current-user';
import { getJWTExpiration } from '@/shared/lib/jwt-tools/get-expiration-time';
import { buildUserObject } from '../hooks/login-mutation.hook';
import { IAuthenticateUserService } from '../interfaces/auth-check.interface';

/**
 * Serviço responsável por autenticar o usuário com base no token armazenado
 * Segue o princípio da responsabilidade única (SRP) do SOLID
 *
 * Responsabilidades:
 * - Validar e processar token JWT
 * - Construir objeto do usuário
 * - Definir expiração do token
 * - Atualizar estados de autenticação
 */
export class AuthCheckService {
	/**
	 * Autentica o usuário com base no token fornecido
	 * @param params - Parâmetros necessários para autenticação
	 */
	static authenticateUser({ token, setUser, setExpiration }: IAuthenticateUserService): void {
		try {
			// Valida se o token é válido antes de processar
			if (!token || typeof token !== 'string') {
				throw new Error('Token inválido ou não fornecido');
			}

			// Constrói o objeto do usuário a partir do token JWT
			const userObject = buildUserObject(token);

			// Obtém a data de expiração do token
			const tokenExpiration = getJWTExpiration(token);

			// Atualiza os estados de autenticação
			setUser(userObject);
			setExpiration(tokenExpiration);
		} catch (error) {
			console.error('Erro ao autenticar usuário:', error);

			// Em caso de erro, limpa os estados de autenticação
			setUser(null);
			setExpiration(undefined);

			// Re-lança o erro para que possa ser tratado pelo chamador se necessário
			throw error;
		}
	}

	/**
	 * Limpa os estados de autenticação
	 * Útil para logout ou quando o token é inválido
	 */
	static clearAuthenticationState(setUser: (user: CurrentUser | null) => void, setExpiration: (expiration: Date | undefined) => void): void {
		setUser(null);
		setExpiration(undefined);
	}

	/**
	 * Valida se um token está presente e é uma string válida
	 * @param token - Token a ser validado
	 * @returns true se o token é válido, false caso contrário
	 */
	static isValidToken(token: string | undefined): token is string {
		return Boolean(token && typeof token === 'string' && token.trim().length > 0);
	}
}
