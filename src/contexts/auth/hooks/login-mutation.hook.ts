import { setAuth<PERSON><PERSON>ie } from '@/shared/lib/auth';
import { getJWTExpiration } from '@/shared/lib/jwt-tools/get-expiration-time';
import { getJWTPayloadProperty } from '@/shared/lib/jwt-tools/get-jwt-payload-property.lib';
import { toaster } from '@/shared/lib/toaster/toaster';
import { loginRequest } from '@/shared/services/requests/login';
import { useMutation } from '@tanstack/react-query';
import { useSetAtom } from 'jotai';
import { tokenExpirationAtom } from '../states/token-expiration.state';
import { userState } from '../states/user.state';

export function buildUserObject(token: string) {
	const name = getJWTPayloadProperty<string>(token, 'nome') ?? '';
	const roles = getJWTPayloadProperty<number[]>(token, 'roles') ?? [];
	const email = getJWTPayloadProperty<{ EMAIL: string }>(token, 'dadosAdicionais')?.EMAIL ?? '';
	return { name, roles, email };
}

export const useLoginMutation = () => {
	const setUser = useSetAtom(userState);
	const setTokenExpiration = useSetAtom(tokenExpirationAtom);

	const mutation = useMutation({
		mutationKey: ['login'],
		mutationFn: async (credentials: { user: string; password: string }) => {
			const response = await loginRequest(credentials);
			if (!response.success) {
				throw new Error(response.data?.message || 'Erro ao fazer login');
			}
			return response.data;
		},
		onSuccess: (token) => {
			if (!token) return;
			setAuthCookie(token);
			setUser(buildUserObject(token));
			setTokenExpiration(getJWTExpiration(token));
			toaster.success(`Seja bem-vindo(a) ${getJWTPayloadProperty<string>(token, 'nome')}`);
		},
		onError: (error) => {
			toaster.error(error.message);
		},
	});

	return { login: mutation.mutate, isLoading: mutation.isPending };
};
