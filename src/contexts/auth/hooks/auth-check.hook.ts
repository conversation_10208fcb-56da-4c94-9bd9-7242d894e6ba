import { cookies } from '@/shared/lib/clients/cookies-client';
import { getJWTExpiration } from '@/shared/lib/jwt-tools/get-expiration-time';
import { logoutRequest } from '@/shared/services/requests/logout';
import { refreshRequest } from '@/shared/services/requests/request';
import { useAtom, useSet<PERSON>tom } from 'jotai';

import { CurrentUser } from '@/shared/interfaces/users/current-user';
import { useCallback, useEffect, useRef, useState } from 'react';
import { tokenExpirationAtom } from '../states/token-expiration.state';
import { userState } from '../states/user.state';
import { buildUserObject } from './login-mutation.hook';

const TOKEN_NAME = 'access-token-PSH';
const REFRESH_THRESHOLD_MS = 30 * 60 * 1000;
const MAX_REFRESH_ATTEMPTS = 5;

const performLogout = async ({ setUser, token }: { setUser: (user: CurrentUser | null) => void; token: string }) => {
	try {
		await logoutRequest({ token });
	} finally {
		cookies.remove(TOKEN_NAME);
		setUser(null);
	}
};

const attemptRefresh = async ({
	token,
	setUser,
	setExpiration,
	refreshAttempts,
}: {
	token: string;
	setUser: (user: CurrentUser | null) => void;
	setExpiration: (date: Date | undefined) => void;
	refreshAttempts: React.MutableRefObject<number>;
}): Promise<boolean> => {
	const { data, success } = await refreshRequest({ token });
	if (success && data) {
		cookies.set(TOKEN_NAME, data);
		setUser(buildUserObject(data));
		setExpiration(getJWTExpiration(data));
		refreshAttempts.current = 0;
		return true;
	}
	await performLogout({ setUser, token });
	return false;
};

export const useAuthCheck = () => {
	const setUser = useSetAtom(userState);
	const [, setExpiration] = useAtom(tokenExpirationAtom);
	const [isLoading, setIsLoading] = useState(true);
	const refreshAttempts = useRef(0);

	const authenticate = useCallback(async () => {
		const token = cookies.get(TOKEN_NAME);
		if (!token) return;
		setUser(buildUserObject(token));
		const tokenExp = getJWTExpiration(token);
		setExpiration(tokenExp);
		if (tokenExp && tokenExp.getTime() - Date.now() < REFRESH_THRESHOLD_MS && refreshAttempts.current < MAX_REFRESH_ATTEMPTS) {
			refreshAttempts.current++;
			await attemptRefresh({ token, setUser, setExpiration, refreshAttempts });
		}
	}, [setUser, setExpiration]);

	useEffect(() => {
		(async () => {
			try {
				await authenticate();
			} catch (error) {
				console.error(error);
			} finally {
				setIsLoading(false);
			}
		})();
	}, [setUser, setExpiration, authenticate]);

	return { isLoading };
};
