import { getAuthCookie } from '@/shared/lib/auth/auth-cookies';
import { useAtom, useSetAtom } from 'jotai';

import { useCallback, useEffect, useState } from 'react';
import { IAuthCheckHookReturn } from '../interfaces/auth-check.interface';
import { AuthCheckService } from '../services/auth-check.service';
import { tokenExpirationAtom } from '../states/token-expiration.state';
import { userState } from '../states/user.state';

/**
 * Hook responsável apenas pela verificação inicial de autenticação
 * Integra com o sistema de autenticação existente e delega o refresh para o interceptor da API
 *
 * Responsabilidades:
 * - Verificar se existe token válido
 * - Autenticar usuário inicial
 * - Gerenciar estado de loading
 *
 * O refresh de tokens é gerenciado automaticamente pelo handleAuthError nos interceptors da API
 *
 * Princípios SOLID aplicados:
 * - SRP: Responsabilidade única de verificação inicial de autenticação
 * - OCP: Extensível através do AuthCheckService
 * - DIP: Depende de abstrações (interfaces) e não de implementações concretas
 */
export const useAuthCheck = (): IAuthCheckHookReturn => {
	const setUser = useSetAtom(userState);
	const [, setExpiration] = useAtom(tokenExpirationAtom);
	const [isLoading, setIsLoading] = useState(true);

	const authenticate = useCallback(async (): Promise<void> => {
		try {
			const token = getAuthCookie();

			// Valida se o token existe e é válido
			if (!AuthCheckService.isValidToken(token)) {
				AuthCheckService.clearAuthenticationState(setUser, setExpiration);
				return;
			}

			// Autentica o usuário usando o serviço especializado
			AuthCheckService.authenticateUser({
				token,
				setUser,
				setExpiration,
			});
		} catch (error) {
			console.error('Erro durante autenticação:', error);
			AuthCheckService.clearAuthenticationState(setUser, setExpiration);
		}
	}, [setUser, setExpiration]);

	useEffect(() => {
		const initializeAuth = async (): Promise<void> => {
			try {
				await authenticate();
			} catch (error) {
				console.error('Erro ao inicializar autenticação:', error);
			} finally {
				setIsLoading(false);
			}
		};

		initializeAuth();
	}, [authenticate]);

	return { isLoading };
};
