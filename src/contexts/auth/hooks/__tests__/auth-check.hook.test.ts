import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useAuthCheck } from '../auth-check.hook';

// Mock das dependências
vi.mock('@/shared/lib/auth/auth-cookies', () => ({
	getAuthCookie: vi.fn(),
}));

vi.mock('../services/auth-check.service', () => ({
	AuthCheckService: {
		isValidToken: vi.fn(),
		authenticateUser: vi.fn(),
		clearAuthenticationState: vi.fn(),
	},
}));

vi.mock('jotai', () => ({
	useAtom: vi.fn(),
	useSetAtom: vi.fn(),
}));

import { getAuthCookie } from '@/shared/lib/auth/auth-cookies';
import { AuthCheckService } from '../services/auth-check.service';
import { useAtom, useSetAtom } from 'jotai';

describe('useAuthCheck', () => {
	const mockSetUser = vi.fn();
	const mockSetExpiration = vi.fn();

	beforeEach(() => {
		vi.clearAllMocks();
		
		// Setup mocks padrão
		vi.mocked(useSetAtom).mockReturnValue(mockSetUser);
		vi.mocked(useAtom).mockReturnValue([undefined, mockSetExpiration]);
	});

	it('deve retornar isLoading true inicialmente e false após autenticação', async () => {
		// Arrange
		vi.mocked(getAuthCookie).mockReturnValue('valid-token');
		vi.mocked(AuthCheckService.isValidToken).mockReturnValue(true);

		// Act
		const { result } = renderHook(() => useAuthCheck());

		// Assert inicial
		expect(result.current.isLoading).toBe(true);

		// Aguarda a autenticação completar
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});
	});

	it('deve autenticar usuário quando token válido existe', async () => {
		// Arrange
		const mockToken = 'valid-token';
		vi.mocked(getAuthCookie).mockReturnValue(mockToken);
		vi.mocked(AuthCheckService.isValidToken).mockReturnValue(true);

		// Act
		renderHook(() => useAuthCheck());

		// Assert
		await waitFor(() => {
			expect(AuthCheckService.authenticateUser).toHaveBeenCalledWith({
				token: mockToken,
				setUser: mockSetUser,
				setExpiration: mockSetExpiration,
			});
		});
	});

	it('deve limpar estados quando token não existe', async () => {
		// Arrange
		vi.mocked(getAuthCookie).mockReturnValue(undefined);
		vi.mocked(AuthCheckService.isValidToken).mockReturnValue(false);

		// Act
		renderHook(() => useAuthCheck());

		// Assert
		await waitFor(() => {
			expect(AuthCheckService.clearAuthenticationState).toHaveBeenCalledWith(
				mockSetUser,
				mockSetExpiration
			);
		});
	});

	it('deve limpar estados quando token é inválido', async () => {
		// Arrange
		vi.mocked(getAuthCookie).mockReturnValue('invalid-token');
		vi.mocked(AuthCheckService.isValidToken).mockReturnValue(false);

		// Act
		renderHook(() => useAuthCheck());

		// Assert
		await waitFor(() => {
			expect(AuthCheckService.clearAuthenticationState).toHaveBeenCalledWith(
				mockSetUser,
				mockSetExpiration
			);
		});
	});

	it('deve tratar erros durante autenticação', async () => {
		// Arrange
		const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
		vi.mocked(getAuthCookie).mockReturnValue('valid-token');
		vi.mocked(AuthCheckService.isValidToken).mockReturnValue(true);
		vi.mocked(AuthCheckService.authenticateUser).mockImplementation(() => {
			throw new Error('Erro de autenticação');
		});

		// Act
		const { result } = renderHook(() => useAuthCheck());

		// Assert
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(consoleErrorSpy).toHaveBeenCalledWith(
			'Erro durante autenticação:',
			expect.any(Error)
		);
		expect(AuthCheckService.clearAuthenticationState).toHaveBeenCalledWith(
			mockSetUser,
			mockSetExpiration
		);

		consoleErrorSpy.mockRestore();
	});

	it('deve definir isLoading como false mesmo quando há erro na inicialização', async () => {
		// Arrange
		const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
		vi.mocked(getAuthCookie).mockImplementation(() => {
			throw new Error('Erro ao obter cookie');
		});

		// Act
		const { result } = renderHook(() => useAuthCheck());

		// Assert
		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(consoleErrorSpy).toHaveBeenCalledWith(
			'Erro ao inicializar autenticação:',
			expect.any(Error)
		);

		consoleErrorSpy.mockRestore();
	});
});
