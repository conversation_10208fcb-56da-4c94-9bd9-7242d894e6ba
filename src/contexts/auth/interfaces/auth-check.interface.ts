import { CurrentUser } from '@/shared/interfaces/users/current-user';

/**
 * Interface para o retorno do hook useAuthCheck
 * Segue o padrão I<Ação><Nome><Escopo>
 */
export interface IAuthCheckHookReturn {
	isLoading: boolean;
}

/**
 * Interface para o serviço de autenticação de usuário
 * Define os parâmetros necessários para autenticar um usuário
 */
export interface IAuthenticateUserService {
	token: string;
	setUser: (user: CurrentUser | null) => void;
	setExpiration: (expiration: Date | undefined) => void;
}

/**
 * Interface para configurações de autenticação
 * Centraliza as configurações relacionadas à verificação de autenticação
 */
export interface IAuthCheckConfig {
	readonly REFRESH_THRESHOLD_MS: number;
	readonly MAX_REFRESH_ATTEMPTS: number;
}
