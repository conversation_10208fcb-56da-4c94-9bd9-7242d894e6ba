import { AuthEventEmitter, AuthEventPayload, AuthEventType } from '@/shared/lib/events/auth-events';
import { useEffect } from 'react';

export const useAuthEvents = (listeners: Partial<Record<AuthEventType, (payload: AuthEventPayload) => void>>) => {
	useEffect(() => {
		const unsubscribers = Object.entries(listeners).map(([event, handler]) => {
			return AuthEventEmitter.subscribe(event as AuthEventType, handler!);
		});

		return () => {
			unsubscribers.forEach((unsubscribe) => unsubscribe());
		};
	}, [listeners]);
};
