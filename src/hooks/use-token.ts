import { cookies } from '@/shared/lib/clients/cookies-client';
import { useEffect, useState } from 'react';

export const useToken = () => {
	const [token, setToken] = useState<string | undefined>(undefined);
	const [tokenLoaded, setTokenLoaded] = useState(false);

	useEffect(() => {
		const fillToken = async () => {
			const tokenFromCookie = await cookies.get('token');
			setToken(tokenFromCookie);
			setTokenLoaded(true);
		};

		fillToken();
	}, [token]);

	return {
		token,
		tokenLoaded,
		setToken,
	};
};
