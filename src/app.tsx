import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'sonner';
import { ConfirmDialogProvider } from './components/confirm-dialog/confirm-dialog';

import { APIProvider } from '@vis.gl/react-google-maps';
import { ErrorBoundary } from './components/error-boundary/error-boundary';
import { Button } from './components/shadcnui/button';
import { ThemeProvider } from './components/shadcnui/theme-provider';
import { StatusConnectionProvider } from './contexts/status-connection-context';
import Navigation from './routes';
import { queryClient } from './shared/lib/clients/tanstack-client';

export default function App() {
	return (
		<ErrorBoundary>
			<QueryClientProvider client={queryClient}>
				<StatusConnectionProvider>
					<ThemeProvider defaultTheme="dark" storageKey="stream-hub-ui-theme">
						<ConfirmDialogProvider
							defaultOptions={{
								customActions(onConfirm, onCancel) {
									return (
										<>
											<Button variant={'destructive'} onClick={onCancel}>
												Cancelar
											</Button>
											<Button onClick={onConfirm}>Confirmar</Button>
										</>
									);
								},
							}}
						>
							<APIProvider apiKey={import.meta.env.VITE_MAPS_API_KEY}>
								<Navigation />
								<Toaster theme={'dark'} richColors />
							</APIProvider>
						</ConfirmDialogProvider>
					</ThemeProvider>
				</StatusConnectionProvider>
				<ReactQueryDevtools initialIsOpen={false} />
			</QueryClientProvider>
		</ErrorBoundary>
	);
}
