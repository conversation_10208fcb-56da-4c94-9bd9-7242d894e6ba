/* @import '@liveblocks/react-ui/styles.css'; */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	input[type='number']::-webkit-inner-spin-button,
	input[type='number']::-webkit-outer-spin-button {
		-webkit-appearance: none;
		margin: 0;
	}

	input[type='number'] {
		-moz-appearance: textfield;
	}
}

@layer base {
	:root {
		--background: 20 14.3% 4.1%;
		--foreground: 0 0% 95%;
		--card: 24 9.8% 10%;
		--card-foreground: 0 0% 95%;
		--popover: 0 0% 9%;
		--popover-foreground: 0 0% 95%;
		--primary: 142.1 70.6% 45.3%;
		--primary-foreground: 144.9 80.4% 10%;
		--secondary: 240 3.7% 15.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 0 0% 15%;
		--muted-foreground: 240 5% 64.9%;
		--accent: 12 6.5% 15.1%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 85.7% 97.3%;
		--border: 240 3.7% 15.9%;
		--input: 240 3.7% 15.9%;
		--ring: 142.4 71.8% 29.2%;
		--radius: 0.5rem;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
	transition: background-color 5000s ease-in-out 0s;
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}

*::-webkit-scrollbar {
	width: 3px;
	height: 3px;
}

*::-webkit-scrollbar-track {
	background: #2c2c2c;
}

*::-webkit-scrollbar-thumb {
	background-color: #555555;
	border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
	background-color: #777777;
}

*::-webkit-scrollbar-corner {
	background-color: #2c2c2c;
}

.embla {
	--slide-spacing: 0;
	--slide-size: 100%;
	position: relative;
}

.embla[data-axis='y'] .embla__container {
	flex-direction: column;
	height: calc(100% + (var(--slide-spacing) * (var(--slides-per-view) - 1)));
}

.embla[data-axis='x'] .embla__container {
	flex-direction: row;
	width: calc(100% + (var(--slide-spacing) * (var(--slides-per-view) - 1)));
}

.embla__slide {
	flex: 0 0 var(--slide-size);
	min-width: 0;
	min-height: 0;
	position: relative;
}

/* Zebra striping para linhas da tabela de apresentações */
.presentations-table-row:nth-child(even) {
	background-color: rgba(255, 255, 255, 0.02); /* tom de cinza sutil para dark */
}

/* Header da tabela mais destacado */
.presentations-table-header th {
	background: linear-gradient(90deg, rgba(40, 40, 40, 0.95) 0%, rgba(30, 30, 30, 0.95) 100%);
	color: #e5e7eb;
	font-size: 1.08rem;
	font-weight: 700;
	letter-spacing: 0.01em;
	border-bottom: 2px solid #2e2e2e;
}

/* Espaçamento maior abaixo do título */
.presentations-toolbar-title {
	margin-bottom: 1.5rem;
}

/* Botão de nova apresentação destacado (reforço extra para hover) */
.button-nova-apresentacao {
	background: linear-gradient(90deg, #22c55e 0%, #4ade80 100%);
	color: #fff;
	box-shadow: 0 2px 8px 0 rgba(34, 197, 94, 0.15);
	border: none;
	transition:
		background 0.2s,
		box-shadow 0.2s;
}
.button-nova-apresentacao:hover {
	background: linear-gradient(90deg, #4ade80 0%, #22c55e 100%);
	box-shadow: 0 4px 16px 0 rgba(34, 197, 94, 0.25);
}
