import { AnimatePresence, motion } from 'framer-motion';
import React, { useLayoutEffect, useRef, useState } from 'react';

type Placement = 'top' | 'bottom' | 'left' | 'right';

interface AutoTooltipProps {
	text: string;
	children: React.ReactNode;
	preferredPlacement?: Placement;
}

export const AutoTooltip: React.FC<AutoTooltipProps> = ({ text, children, preferredPlacement = 'top' }) => {
	const [showTooltip, setShowTooltip] = useState(false);
	const [tooltipPos, setTooltipPos] = useState({ top: 0, left: 0 });

	const refTrigger = useRef<HTMLButtonElement | null>(null);
	const refTooltip = useRef<HTMLDivElement | null>(null);

	useLayoutEffect(() => {
		if (!showTooltip) return;

		const triggerEl = refTrigger.current;
		const tooltipEl = refTooltip.current;
		if (!triggerEl || !tooltipEl) return;

		const triggerRect = triggerEl.getBoundingClientRect();
		const tooltipRect = tooltipEl.getBoundingClientRect();
		let top = 0;
		let left = 0;

		if (preferredPlacement === 'top') {
			top = triggerRect.top - tooltipRect.height;
			left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
		} else if (preferredPlacement === 'bottom') {
			top = triggerRect.bottom;
			left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
		} else if (preferredPlacement === 'left') {
			top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
			left = triggerRect.left - tooltipRect.width;
		} else if (preferredPlacement === 'right') {
			top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
			left = triggerRect.right;
		}

		const viewportWidth = window.innerWidth;
		const viewportHeight = window.innerHeight;

		if (left < 0) {
			left = triggerRect.left;
		} else if (left + tooltipRect.width > viewportWidth) {
			left = triggerRect.right - tooltipRect.width;
		}

		if (top < 0) {
			top = triggerRect.bottom;
		} else if (top + tooltipRect.height > viewportHeight) {
			top = triggerRect.top - tooltipRect.height;
		}

		setTooltipPos({ top, left });
	}, [showTooltip, preferredPlacement]);

	return (
		<>
			<span
				ref={refTrigger}
				onMouseEnter={() => setShowTooltip(true)}
				onMouseLeave={() => setShowTooltip(false)}
				onFocus={() => setShowTooltip(true)}
				onBlur={() => setShowTooltip(false)}
				style={{ display: 'inline-block', cursor: 'pointer' }}
				tabIndex={0}
			>
				{children}
			</span>
			<AnimatePresence>
				{showTooltip && (
					<motion.div
						ref={refTooltip}
						initial={{ opacity: 0, y: -5, scale: 0.95 }}
						animate={{ opacity: 1, y: 0, scale: 1 }}
						exit={{ opacity: 0, y: -5, scale: 0.95 }}
						transition={{ duration: 0.2, ease: 'easeOut' }}
						className="rounded-md border border-gray-800/60 bg-gradient-to-br from-green-500/80 via-[#121214] to-green-900/80 px-3 py-2 text-xs font-medium text-white shadow-sm shadow-green-500/5"
						style={{
							position: 'fixed',
							top: tooltipPos.top,
							left: tooltipPos.left,
							zIndex: 9999,
						}}
					>
						{text}
					</motion.div>
				)}
			</AnimatePresence>
		</>
	);
};
