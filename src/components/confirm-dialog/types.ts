import {
	AlertDialog,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/shadcnui/alert-dialog';
import { buttonVariants } from '@/components/shadcnui/button';
import { type VariantProps } from 'class-variance-authority';
import { ButtonHTMLAttributes, ComponentPropsWithoutRef, ReactNode } from 'react';

export interface ConfirmDialogButtonProps extends ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof buttonVariants> {}

export type ConfirmOptions = {
	title: ReactNode | string;
	description?: ReactNode | string;
	confirmButton?: Omit<ConfirmDialogButtonProps, 'key'>;
	cancelButton?: Omit<ConfirmDialogButtonProps, 'key'>;
	confirmText?: string;
	cancelText?: string;
	icon?: ReactNode;
	customActions?: (onConfirm: () => void, onCancel: () => void) => ReactNode;
	alertDialog?: ComponentPropsWithoutRef<typeof AlertDialog>;
	alertDialogContent?: ComponentPropsWithoutRef<typeof AlertDialogContent>;
	alertDialogHeader?: ComponentPropsWithoutRef<typeof AlertDialogHeader>;
	alertDialogTitle?: ComponentPropsWithoutRef<typeof AlertDialogTitle>;
	alertDialogDescription?: ComponentPropsWithoutRef<typeof AlertDialogDescription>;
	alertDialogFooter?: ComponentPropsWithoutRef<typeof AlertDialogFooter>;
};

export interface ConfirmContextType {
	confirm: (options: ConfirmOptions) => Promise<boolean>;
}

export type ConfirmDialogProps = {
	isOpen: boolean;
	onOpenChange: (isOpen: boolean) => void;
	config: Partial<ConfirmOptions>;
	onConfirm: () => void;
	onCancel: () => void;
};

export type ConfirmDialogProviderProps = {
	defaultOptions?: Partial<ConfirmOptions>;
	children: ReactNode;
};
