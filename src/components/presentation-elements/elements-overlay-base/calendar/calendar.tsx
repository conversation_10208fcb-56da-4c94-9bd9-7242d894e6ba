import { ICalendarObject } from '@/pages/edit-apresentation/components/properties-panel/item-editors/calendar/calendar.types';
import { addMonths } from 'date-fns';
import { useMemo } from 'react';
import { CustomCalendar } from './calendar-component';
import './calendar.css';

export interface CalendarDemoProps {
	id: string;
	content: ICalendarObject;
}

export function CalendarDemo({ id, content }: Readonly<CalendarDemoProps>) {
	const calendarInfo = useMemo(() => content, [content]);
	const numberOfMonths = useMemo(
		() => (calendarInfo.calendarMode === 'single' ? 1 : (calendarInfo.numberOfMonths ?? 3)),
		[calendarInfo.calendarMode, calendarInfo.numberOfMonths],
	);

	const styles = {
		container: {
			width: '100%',
			height: '100%',
			display: 'flex',
			flexWrap: 'wrap',
			gap: `${calendarInfo.cellSpacing}px`,
			backgroundColor: calendarInfo.backgroundColor,
			padding: `${calendarInfo.padding?.top}px ${calendarInfo.padding?.right}px ${calendarInfo.padding?.bottom}px ${calendarInfo.padding?.left}px`,
			alignItems: 'stretch',
			justifyContent: 'center',
			border:
				calendarInfo.border?.style !== 'none'
					? `${calendarInfo.border?.width}px ${calendarInfo.border?.style} ${calendarInfo.border?.color}`
					: 'none',
			borderRadius: `${calendarInfo.border?.radius}px`,
		} as const,
		calendarWrapper: {
			flex: '1 1 auto',
			minWidth: '250px',
			maxWidth: `${100 / Math.min(numberOfMonths, 3)}%`,
		} as const,
		calendar: {
			fontSize: 'calc(0.7vw + 0.7vh)',
			height: '100%',
			minHeight: '100%',
			width: '100%',
			pointerEvents: 'none' as const,
			'--accent-foreground': calendarInfo.mainColor,
			'--accent': calendarInfo.secondaryColor,
			'--foreground': calendarInfo.textColor,
		} as const,
	};

	const getCalendarProps = (monthsToAdd: number = 0) => {
		const commonProps = {
			onSelect: () => {},
			showOutsideDays: calendarInfo.showOutsideDays,
			className: 'h-full w-full max-w-full flex-1',
			style: styles?.calendar,
			defaultMonth: monthsToAdd > 0 ? addMonths(new Date(), monthsToAdd) : undefined,
		};
		switch (calendarInfo.calendarMode) {
			case 'single':
				return {
					...commonProps,
					mode: 'single' as const,
					selected: calendarInfo.selectedDate ? new Date(calendarInfo.selectedDate) : undefined,
				};
			case 'range':
				return {
					...commonProps,
					mode: 'range' as const,
					className: 'h-full w-full max-w-full flex-1',
					month: monthsToAdd > 0 ? addMonths(new Date(), monthsToAdd) : undefined,
					selected: {
						from: calendarInfo.dateRange?.from ? new Date(calendarInfo.dateRange.from) : undefined,
						to: calendarInfo.dateRange?.to ? new Date(calendarInfo.dateRange.to) : undefined,
					},
				};
			case 'multiple':
				return {
					...commonProps,
					mode: 'multiple' as const,
					selected: (calendarInfo.selectedDates ?? []).map((d) => new Date(d)),
				};
			default:
				return commonProps;
		}
	};

	return (
		<div style={styles.container}>
			{Array.from({ length: numberOfMonths }).map((_, monthOffset) => (
				<div key={`calendar-${id}-${monthOffset}`} style={styles.calendarWrapper}>
					<CustomCalendar {...getCalendarProps(monthOffset)} />
				</div>
			))}
		</div>
	);
}
