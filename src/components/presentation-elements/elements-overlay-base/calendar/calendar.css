.calendar {
	--calendar-main-color: var(--calendar-main-color, #2563eb);
	--calendar-secondary-color: var(--calendar-secondary-color, #93c5fd);
	--calendar-text-color: var(--calendar-text-color, #000000);
	--calendar-background-color: var(--calendar-background-color, #ffffff);
	--calendar-opacity: var(--calendar-opacity, 1);
}

.calendar button {
	color: var(--calendar-text-color);
}

.calendar button[data-selected] {
	background-color: var(--calendar-main-color) !important;
	color: white !important;
}

.calendar button[data-selected]:hover {
	background-color: var(--calendar-main-color) !important;
	opacity: 0.9;
}

.calendar button[data-in-range] {
	background-color: var(--calendar-secondary-color) !important;
	color: var(--calendar-text-color) !important;
}

.calendar button[data-in-range]:hover {
	background-color: var(--calendar-secondary-color) !important;
	opacity: 0.9;
}

.calendar button:hover {
	background-color: var(--calendar-secondary-color) !important;
	opacity: 0.8;
}

/* Estilos específicos para o range */
.calendar [data-state='range-start'] button,
.calendar [data-state='range-end'] button {
	background-color: var(--calendar-main-color) !important;
	color: white !important;
	border-radius: 4px;
}

.calendar [data-state='range-middle'] button {
	background-color: var(--calendar-secondary-color) !important;
	color: var(--calendar-text-color) !important;
	border-radius: 0;
}

.calendar button[data-disabled] {
	opacity: 0.3;
}

.calendar button[data-outside] {
	opacity: 0.5;
}

.rdp {
	--accent-foreground: #000000;
	--accent: #ffffff;
	--foreground: #000000;
}

.rdp-day {
	color: var(--foreground);
}

.rdp-day_selected {
	background-color: var(--accent-foreground) !important;
	color: var(--accent) !important;
}

.rdp-day_today {
	background-color: var(--accent) !important;
	color: var(--accent-foreground) !important;
}

.rdp-day_range_middle {
	background-color: var(--accent) !important;
	color: var(--accent-foreground) !important;
}

.rdp-caption_label {
	color: var(--foreground);
}

.rdp-head_cell {
	color: var(--foreground);
}
