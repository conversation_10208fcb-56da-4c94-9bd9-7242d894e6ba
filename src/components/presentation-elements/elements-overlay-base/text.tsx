// import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
// import { useAtomValue } from 'jotai';

// import { useEffect, useRef, useState } from 'react';

// interface TextEditorProps {
// 	id: string;
// }

// export const TextEditor = ({ id }: TextEditorProps) => {
// 	const [isEditing, setIsEditing] = useState(false);
// 	const items = useAtomValue(itemsAtom);
// 	const currentItem = items.find((item) => item.id === id);
// 	const initialText = currentItem?.content as unknown as string;
// 	const selectedIds = useAtomValue(selectedItemsIdsAtom);

// 	useEffect(() => {
// 		const selectedLength = selectedIds.length;

// 		if (selectedLength > 1) {
// 			setIsEditing(false);
// 		} else if (selectedLength === 1) {
// 			setIsEditing(true);
// 		}
// 	}, [selectedIds]);

// 	const editorRef = useRef<HTMLTextAreaElement>(null);

// 	useEffect(() => {
// 		if (editorRef.current && !editorRef.current.value.trim()) {
// 			editorRef.current.value = initialText || 'Clique para editar...';
// 		}
// 	}, [initialText]);

// 	useEffect(() => {
// 		if (!selectedIds || selectedIds.length > 1) {
// 			setIsEditing(false);
// 		} else {
// 			if (selectedIds.includes(id)) {
// 				setIsEditing(true);
// 				if (editorRef.current) {
// 					editorRef.current.focus();
// 					const length = editorRef.current.value.length;
// 					editorRef.current.setSelectionRange(length, length);
// 				}
// 			}
// 		}
// 	}, [selectedIds, id]);

// 	const stopEditing = () => {
// 		if (editorRef.current) {
// 			// const newText = editorRef.current.innerText;
// 			// // onSave(newText);
// 			const stopEditing = () => {
// 				// if (editorRef.current) {
// 				// 	const newText = editorRef.current.value;
// 				// }
// 				setIsEditing(false);
// 			};
// 			stopEditing();
// 		}

// 		// else if (e.key === 'Escape') {
// 		// 	setIsEditing(false);
// 		// }
// 	};

// 	function handleKeyDown(event: React.KeyboardEvent<HTMLTextAreaElement>): void {
// 		if (event.key === 'Escape') {
// 			setIsEditing(false);
// 		} else if (event.key === 'Enter' && !event.shiftKey) {
// 			event.preventDefault();
// 			// if (editorRef.current) {
// 			// 	const newText = editorRef.current.value;
// 			// }
// 			setIsEditing(false);
// 		}
// 	}
// 	return (
// 		<div style={{ position: 'relative', width: '100%', height: '100%' }}>
// 			{isEditing ? (
// 				<textarea
// 					ref={editorRef}
// 					aria-label="Text Editor"
// 					tabIndex={0}
// 					onBlur={stopEditing}
// 					onKeyDown={handleKeyDown}
// 					defaultValue={initialText || 'Clique para editar...'}
// 					style={{
// 						backgroundColor: 'red',
// 						width: '100%',
// 						minHeight: '60px',
// 						fontSize: '18px',
// 						fontFamily: 'sans-serif',
// 						padding: '12px',
// 						borderRadius: '6px',
// 						outline: 'none',
// 						background: 'transparent',
// 						cursor: 'text',
// 					}}
// 				/>
// 			) : (
// 				<h1>data</h1>
// 			)}
// 		</div>
// 	);
// };
