import { ICarouselObject } from '@/pages/edit-apresentation/components/properties-panel/item-editors/carousel/corousel.types';
import { VIDEO_TYPES } from '@/pages/edit-apresentation/components/properties-panel/shared/common';
import { useImageLoader } from '@/shared/hooks/image-loader.hook';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import useEmblaCarousel from 'embla-carousel-react';
import { useAtomValue } from 'jotai';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ImagePlaceholder } from './image';

interface CarouselDemoProps {
	content: ICarouselObject;
	id: string;
}

type MediaItemType = {
	url: string;
	mimetype: string;
	media_temp_token?: string;
};

const isVideoMedia = (media: MediaItemType): boolean => {
	const mime = media.mimetype ? media.mimetype.toLowerCase() : '';
	return VIDEO_TYPES.some((videoType) => videoType.toLowerCase() === mime);
};

export const CarouselDemo: React.FC<CarouselDemoProps> = ({ content, id }) => {
	const items = useAtomValue(itemsAtom);
	const isVertical = content?.scrollDirection === 'up' || content?.scrollDirection === 'down';
	const [emblaRef, embla] = useEmblaCarousel({ loop: true, axis: isVertical ? 'y' : 'x' });
	const [timePerSlide, setTimePerSlide] = useState<number | null>(null);
	const [activeIndex, setActiveIndex] = useState(0);
	const videoRefs = useRef<HTMLVideoElement[]>([]);

	const currentItem = items.find((item) => item.id === id);

	const mediaItems = useMemo(() => {
		if (!currentItem?.media || currentItem.media.length === 0) return [];
		return currentItem.media;
	}, [currentItem?.media]);

	const currentMedia = mediaItems[activeIndex % mediaItems.length] || null;
	const isCurrentVideo = currentMedia ? isVideoMedia(currentMedia) : false;

	const {
		blobUrl: currentBlobUrl,
		isLoading: isImageLoading,
		error: imageError,
	} = useImageLoader({
		imageUrl: currentMedia && !isCurrentVideo ? currentMedia.url : undefined,
		token: currentMedia && !isCurrentVideo ? currentMedia.mediaTempToken : undefined,
	});

	useEffect(() => {
		if (content) {
			setTimePerSlide(content.timePerSlide);
		}
	}, [content]);

	useEffect(() => {
		if (!embla) return;
		const onSelect = () => {
			const index = embla.selectedScrollSnap();
			setActiveIndex(index);
		};
		embla.on('select', onSelect);
		onSelect();
		return () => {
			embla.off('select', onSelect);
		};
	}, [embla]);

	useEffect(() => {
		const currentMedia = mediaItems[activeIndex % mediaItems.length];
		if (currentMedia && isVideoMedia(currentMedia)) {
			const videoElement = videoRefs.current[activeIndex];
			if (videoElement) {
				videoElement.play().catch((err) => console.error('Erro ao reproduzir o vídeo:', err));
			}
		}
	}, [activeIndex, mediaItems]);

	useEffect(() => {
		if (!embla || timePerSlide === null) return;
		const currentMedia = mediaItems[activeIndex % mediaItems.length];
		if (currentMedia && !isVideoMedia(currentMedia)) {
			const timer = setTimeout(() => {
				if (embla) {
					if (content?.scrollDirection === 'reverse') {
						embla.scrollPrev();
					} else if (content?.scrollDirection === 'up') {
						embla.scrollNext();
					} else if (content?.scrollDirection === 'down') {
						embla.scrollPrev();
					} else {
						embla.scrollNext();
					}
				}
			}, timePerSlide * 1000);
			return () => clearTimeout(timer);
		}
	}, [embla, timePerSlide, activeIndex, mediaItems, content]);

	if (!items || !mediaItems || mediaItems.length === 0) return <ImagePlaceholder message="Nenhuma mídia adicionada." />;
	if (!content) return <ImagePlaceholder message="Conteúdo não encontrado." />;

	return (
		<div
			ref={emblaRef}
			style={{
				transition: `transform ${content.animationDuration ?? 300}ms ${content.animationEasing ?? 'ease-in-out'}`,
				width: '100%',
				overflow: 'hidden',
				height: '100%',
				backgroundColor: content.backgroundColor,
				borderRadius: `${content.border?.radius ?? 0}px`,
				opacity: content.opacity,
				boxShadow: content.boxShadow,
				border: content.border ? `${content.border.width}px ${content.border.style} ${content.border.color}` : 'none',
			}}
		>
			<div style={{ display: 'flex', flexDirection: isVertical ? 'column' : 'row', height: '100%', width: '100%' }}>
				{mediaItems.map((media, index) => {
					if (isVideoMedia(media)) {
						const isSingleVideo = mediaItems.length === 1;
						return (
							<div
								key={media.url}
								style={isVertical ? { minHeight: '100%', height: '100%', width: '100%' } : { minWidth: '100%', height: '100%' }}
							>
								<video
									ref={(el) => (videoRefs.current[index] = el!)}
									src={media.url}
									muted
									playsInline
									loop={isSingleVideo}
									onEnded={() => {
										if (!isSingleVideo && embla) {
											if (content?.scrollDirection === 'reverse') {
												embla.scrollPrev();
											} else if (content?.scrollDirection === 'up') {
												embla.scrollNext();
											} else if (content?.scrollDirection === 'down') {
												embla.scrollPrev();
											} else {
												embla.scrollNext();
											}
										}
									}}
									style={{
										width: '100%',
										height: '100%',
										objectFit: 'contain',
										borderRadius: `${content.border?.radius ?? 0}px`,
									}}
								/>
							</div>
						);
					}

					const isActive = index === activeIndex;
					const showLoading = isActive && isImageLoading;
					const showError = isActive && imageError;
					const imageUrl = isActive && currentBlobUrl ? currentBlobUrl : media.url;

					if (showLoading) {
						return (
							<div
								key={media.url}
								style={isVertical ? { minHeight: '100%', height: '100%', width: '100%' } : { minWidth: '100%', height: '100%' }}
							>
								<div
									style={{
										display: 'flex',
										alignItems: 'center',
										justifyContent: 'center',
										height: '100%',
									}}
								>
									Carregando imagem...
								</div>
							</div>
						);
					}

					if (showError) {
						return (
							<div
								key={media.url}
								style={isVertical ? { minHeight: '100%', height: '100%', width: '100%' } : { minWidth: '100%', height: '100%' }}
							>
								<div
									style={{
										display: 'flex',
										alignItems: 'center',
										justifyContent: 'center',
										height: '100%',
										color: 'red',
									}}
								>
									Erro ao carregar imagem.
								</div>
							</div>
						);
					}

					return (
						<div
							key={media.url}
							style={isVertical ? { minHeight: '100%', height: '100%', width: '100%' } : { minWidth: '100%', height: '100%' }}
						>
							<img
								src={imageUrl}
								alt={`Slide ${index + 1}`}
								style={{
									width: '100%',
									height: '100%',
									objectFit: 'contain',
									borderRadius: `${content.border?.radius ?? 0}px`,
								}}
							/>
						</div>
					);
				})}
			</div>
		</div>
	);
};
