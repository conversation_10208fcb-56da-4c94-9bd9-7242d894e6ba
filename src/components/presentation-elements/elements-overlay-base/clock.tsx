import useInterval from '@/hooks/use-interval';
import React, { useState } from 'react';

type ClockFormat = '12h' | '24h';
type TimeUnit = 'hours' | 'minutes' | 'seconds';
type ClockVariant = 'digital' | 'analog' | 'flip';
type ClockAnimation = 'fade' | 'slide' | 'pulse' | 'none';

interface ResponsiveClockProps {
	format?: ClockFormat;
	showSeconds?: boolean;
	showDate?: boolean;
	theme?: 'light' | 'dark' | 'auto';
	size?: 'sm' | 'md' | 'lg';
	className?: string;
	variant?: ClockVariant;
	animation?: ClockAnimation;
	primaryColor?: string;
	secondaryColor?: string;
	timezone?: string;
}

const getCurrentTimeData = (
	timezone?: string,
): {
	hours: number;
	minutes: number;
	seconds: number;
	milliseconds: number;
	ampm: string;
	day: number;
	month: number;
	year: number;
	dayName: string;
	monthName: string;
	timestamp: number;
} => {
	const date = new Date();
	let localDate = date;

	if (timezone) {
		try {
			const options: Intl.DateTimeFormatOptions = {
				timeZone: timezone,
				hour12: false,
				year: 'numeric',
				month: 'numeric',
				day: 'numeric',
				hour: 'numeric',
				minute: 'numeric',
				second: 'numeric',
			};

			const formatter = new Intl.DateTimeFormat('pt-BR', options);
			const parts = formatter.formatToParts(date);

			const dateParts: Record<string, number> = {};
			parts.forEach((part) => {
				if (['year', 'month', 'day', 'hour', 'minute', 'second'].includes(part.type)) {
					dateParts[part.type] = parseInt(part.value, 10);
				}
			});

			localDate = new Date(dateParts.year, (dateParts.month || 1) - 1, dateParts.day, dateParts.hour, dateParts.minute, dateParts.second);
		} catch (error) {
			console.error(`Erro ao processar fuso horário: ${timezone}`, error);
		}
	}

	const hours = localDate.getHours();
	const minutes = localDate.getMinutes();
	const seconds = localDate.getSeconds();
	const milliseconds = localDate.getMilliseconds();
	const ampm = hours >= 12 ? 'PM' : 'AM';

	const day = localDate.getDate();
	const month = localDate.getMonth() + 1;
	const year = localDate.getFullYear();
	const timestamp = localDate.getTime();

	const dayNames = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];
	const monthNames = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];

	const dayName = dayNames[localDate.getDay()];
	const monthName = monthNames[localDate.getMonth()];

	return {
		hours,
		minutes,
		seconds,
		milliseconds,
		ampm,
		day,
		month,
		year,
		dayName,
		monthName,
		timestamp,
	};
};

const formatTimeUnit = (value: number): string => (value < 10 ? `0${value}` : `${value}`);

const formatTime = (time: ReturnType<typeof getCurrentTimeData>, format: ClockFormat = '24h', showSeconds: boolean = true): string => {
	const { hours, minutes, seconds, ampm } = time;

	const formattedHours = format === '12h' ? formatTimeUnit(hours % 12 || 12) : formatTimeUnit(hours);

	const formattedMinutes = formatTimeUnit(minutes);
	const formattedSeconds = formatTimeUnit(seconds);
	let timeString = `${formattedHours}:${formattedMinutes}`;
	if (showSeconds) timeString += `:${formattedSeconds}`;
	if (format === '12h') timeString += ` ${ampm}`;

	return timeString;
};

const formatDate = (time: ReturnType<typeof getCurrentTimeData>): string => {
	const { dayName, day, monthName, year } = time;
	return `${dayName}, ${day} de ${monthName} de ${year}`;
};

const getTimeUnitClasses = (unit: TimeUnit, size?: 'sm' | 'md' | 'lg', animation?: ClockAnimation): string => {
	const baseClasses = 'transition-all duration-300 ease-in-out';

	const animationClasses = {
		fade: 'animate-fadeIn',
		slide: 'animate-slideIn',
		pulse: 'animate-pulse',
		none: '',
	};

	const sizeClasses = {
		sm: {
			hours: 'text-4xl font-bold',
			minutes: 'text-4xl font-medium',
			seconds: 'text-xl font-light opacity-70',
		},
		md: {
			hours: 'text-5xl font-bold',
			minutes: 'text-5xl font-medium',
			seconds: 'text-2xl font-light opacity-70',
		},
		lg: {
			hours: 'text-6xl md:text-7xl font-bold',
			minutes: 'text-6xl md:text-7xl font-medium',
			seconds: 'text-3xl md:text-4xl font-light opacity-70',
		},
	};

	const selectedSize = size || 'md';
	const animationClass = animation ? animationClasses[animation] : '';

	return `${baseClasses} ${sizeClasses[selectedSize][unit]} ${animationClass}`;
};

const getContainerClasses = (theme: 'light' | 'dark' | 'auto', className?: string, variant?: ClockVariant): string => {
	const baseClasses = 'rounded-xl flex flex-col items-center justify-center p-4 transition-all duration-300';

	const variantClasses = {
		digital: '',
		analog: 'aspect-square relative',
		flip: 'perspective-1000',
	};

	const themeClasses = {
		light: 'bg-white/10 text-black backdrop-blur-sm shadow-lg',
		dark: 'bg-black/20 text-white backdrop-blur-sm shadow-lg',
		auto: 'bg-white/10 dark:bg-black/20 text-black dark:text-white backdrop-blur-sm shadow-lg',
	};
	const selectedVariant = variant || 'digital';
	return `${baseClasses} ${themeClasses[theme]} ${variantClasses[selectedVariant]}  ${className || ''}`;
};

const ResponsiveClock: React.FC<ResponsiveClockProps> = ({ format = '24h', showSeconds = true, showDate = true, theme = 'auto', size = 'lg', className }) => {
	const [time, setTime] = useState(getCurrentTimeData());

	useInterval(() => {
		setTime(getCurrentTimeData());
	}, 1000);

	const formattedTime = formatTime(time, format, showSeconds);
	const formattedDate = formatDate(time);
	const { hours, minutes, seconds, ampm } = time;
	const formattedHours = format === '12h' ? formatTimeUnit(hours % 12 || 12) : formatTimeUnit(hours);
	const formattedMinutes = formatTimeUnit(minutes);
	const formattedSeconds = formatTimeUnit(seconds);

	return (
		<div
			className={getContainerClasses(theme, className)}
			aria-live="polite"
			aria-atomic="true"
			role="timer"
			tabIndex={0}
			aria-label={`Relógio mostrando ${formattedTime}${showDate ? `, ${formattedDate}` : ''}`}
		>
			<div className="flex items-end justify-center">
				<span className={getTimeUnitClasses('hours', size)}>{formattedHours}</span>
				<span className={`mx-1 ${size === 'lg' ? 'text-5xl' : size === 'md' ? 'text-4xl' : 'text-3xl'} font-light`}>:</span>
				<span className={getTimeUnitClasses('minutes', size)}>{formattedMinutes}</span>
				{showSeconds && (
					<>
						<span className={`mx-1 ${size === 'lg' ? 'text-3xl' : size === 'md' ? 'text-2xl' : 'text-xl'} font-light opacity-70`}>:</span>
						<span className={getTimeUnitClasses('seconds', size)}>{formattedSeconds}</span>
					</>
				)}
				{format === '12h' && <span className="ml-2 text-sm font-medium">{ampm}</span>}
			</div>
			{showDate && (
				<div className={`mt-2 text-center ${size === 'lg' ? 'text-lg' : size === 'md' ? 'text-base' : 'text-sm'} font-medium opacity-80`}>
					{formattedDate}
				</div>
			)}
		</div>
	);
};

export default ResponsiveClock;
