import { IWeather } from '@/pages/edit-apresentation/components/properties-panel/item-editors/weather/weather-editor.type';
import { DEFAULT_WEATHER_SETTINGS } from '@/pages/edit-apresentation/data/base-format-elements/weather';

import { motion } from 'framer-motion';
import { useCallback, useEffect, useMemo, useState } from 'react';

interface GeoCoordinates {
	latitude: number;
	longitude: number;
}

interface NominatimResult {
	lat: string;
	lon: string;
}

interface FormatCoordinatesParams {
	coordinate: number;
	shouldRound?: boolean;
}

interface BuildForecastUrlParams {
	latitude: number;
	longitude: number;
	cityName: string;
}

function useUserLocation() {
	const [location, setLocation] = useState<GeoCoordinates | null>(null);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (!navigator.geolocation) {
			setError('Geolocalização não é suportada por este navegador.');
			return;
		}

		navigator.geolocation.getCurrentPosition(
			({ coords }) => setLocation({ latitude: coords.latitude, longitude: coords.longitude }),
			(err) => setError(err.message),
		);
	}, []);

	return { location, error };
}

function useCity(location: GeoCoordinates | null) {
	const [city, setCity] = useState<string | null>(null);

	useEffect(() => {
		let isCancelled = false;
		if (location && !city) {
			const fetchCity = async () => {
				const { latitude, longitude } = location;
				const url = `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json`;
				try {
					const res = await fetch(url);
					const data = await res.json();
					if (!isCancelled) {
						const address = data.address || {};
						const selectedCity = address.city || address.town || address.village || '';
						setCity(selectedCity);
					}
				} catch (err) {
					console.error('Erro ao obter cidade:', err);
				}
			};
			fetchCity();
		}
		return () => {
			isCancelled = true;
		};
	}, [location, city]);

	return { city };
}

function useCityCoordinates(city: string | null) {
	const [center, setCenter] = useState<GeoCoordinates | null>(null);

	useEffect(() => {
		let isCancelled = false;
		if (city && !center) {
			const fetchCoordinates = async () => {
				const url = `https://nominatim.openstreetmap.org/search?city=${encodeURIComponent(city)}&format=json&limit=1`;
				try {
					const res = await fetch(url);
					const data: NominatimResult[] = await res.json();
					if (!isCancelled && data.length) {
						const { lat, lon } = data[0];
						setCenter({ latitude: parseFloat(lat), longitude: parseFloat(lon) });
					} else if (!isCancelled) {
						console.error('Nenhum resultado encontrado para a cidade:', city);
					}
				} catch (err) {
					console.error('Erro ao buscar coordenadas da cidade:', err);
				}
			};
			fetchCoordinates();
		}
		return () => {
			isCancelled = true;
		};
	}, [city, center]);

	return center;
}

export const Weather = ({ content }: { content: IWeather }) => {
	const { location, error } = useUserLocation();
	const { city } = useCity(location);
	const center = useCityCoordinates(city);
	const [weatherUrl, setWeatherUrl] = useState('');

	const formatCoordinates = useCallback(({ coordinate, shouldRound = false }: FormatCoordinatesParams): string => {
		const absValue = Math.abs(coordinate);
		const integerPart = Math.floor(absValue);
		const decimalPart = shouldRound ? Math.round((absValue - integerPart) * 100) : Math.floor((absValue - integerPart) * 100);
		return `${integerPart}d${decimalPart.toString().padStart(2, '0')}`;
	}, []);

	const buildForecastUrl = useCallback(
		({ latitude, longitude, cityName }: BuildForecastUrlParams): string => {
			const latFormatted = formatCoordinates({ coordinate: latitude });
			const lonFormatted = formatCoordinates({ coordinate: longitude, shouldRound: true });
			const formattedCity = cityName
				.toLowerCase()
				.replace(/\s+/g, '-')
				.normalize('NFD')
				.replace(/[\u0300-\u036f]/g, '');
			return `https://forecast7.com/pt/n${latFormatted}n${lonFormatted}/${formattedCity}/`;
		},
		[formatCoordinates],
	);

	useEffect(() => {
		if (center?.latitude && center?.longitude && city) {
			const url = buildForecastUrl({
				latitude: center.latitude,
				longitude: center.longitude,
				cityName: city,
			});
			setWeatherUrl(url);
		}
	}, [center, city, buildForecastUrl]);

	const weatherDataParsed = useMemo(() => {
		try {
			return content;
		} catch (error) {
			console.error('Erro ao analisar dados do clima:', error);
			return DEFAULT_WEATHER_SETTINGS;
		}
	}, [content]);

	useEffect(() => {
		const existingScript = document.querySelector('script[src="https://weatherwidget.io/js/widget.min.js"]');
		if (existingScript) {
			existingScript.remove();
		}
		const script = document.createElement('script');
		script.src = 'https://weatherwidget.io/js/widget.min.js';
		script.async = true;
		document.body.appendChild(script);
	}, [weatherDataParsed]);

	if (error) return <p>Erro ao obter localização: {error}</p>;
	if (!weatherUrl) return <p>Obtendo localização...</p>;

	const customThemeAttributes =
		weatherDataParsed?.themeOptions?.theme === 'custom'
			? {
					'data-accent': weatherDataParsed?.themeOptions?.customTheme?.accent,
					'data-basecolor': weatherDataParsed?.themeOptions?.customTheme?.background,
					'data-textcolor': weatherDataParsed?.themeOptions?.customTheme?.text,
					'data-shadowcolor': weatherDataParsed?.themeOptions?.customTheme?.shadow,
					'data-highcolor': weatherDataParsed?.themeOptions?.customTheme?.highTemp,
					'data-lowcolor': weatherDataParsed?.themeOptions?.customTheme?.lowTemp,
					'data-sunandthundercolor': weatherDataParsed?.themeOptions?.customTheme?.sunAndThunder,
					'data-mooncolor': weatherDataParsed?.themeOptions?.customTheme?.moon,
					'data-cloudcolor': weatherDataParsed?.themeOptions?.customTheme?.cloud,
					'data-snowcolor': weatherDataParsed?.themeOptions?.customTheme?.snow,
					'data-cloudfill': weatherDataParsed?.themeOptions?.customTheme?.cloudFill,
					'data-raincolor': weatherDataParsed?.themeOptions?.customTheme?.rain,
				}
			: {};

	return (
		<motion.a
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.8 }}
			className="weatherwidget-io"
			data-mode={weatherDataParsed?.forecastMode}
			href={weatherUrl}
			data-label_1={city ?? ''}
			data-label_2={weatherDataParsed?.labelTwo}
			data-icons={weatherDataParsed?.iconType}
			data-days={weatherDataParsed?.forecastDays}
			data-theme={weatherDataParsed?.themeOptions?.theme}
			{...customThemeAttributes}
		/>
	);
};
