import { IShapeSquareStyle } from '@/pages/edit-apresentation/components/properties-panel/item-editors/shape/square-editor.types';
import { DEFAULT_SQUARE_STYLE } from '@/pages/edit-apresentation/data/base-format-elements/square';
import React, { useMemo } from 'react';

const getBackground = (style: IShapeSquareStyle): string => {
	if (!style) return DEFAULT_SQUARE_STYLE.backgroundColor ?? 'transparent';
	const { backgroundStyle, backgroundColor, gradientOptions } = style;
	if (backgroundStyle === 'opaque') return backgroundColor ?? DEFAULT_SQUARE_STYLE.backgroundColor ?? 'transparent';
	if (!gradientOptions?.stops?.length) return DEFAULT_SQUARE_STYLE.backgroundColor ?? 'transparent';
	const stopsStr = gradientOptions.stops.map((stop) => `${stop.color} ${stop.offset}%`).join(', ');
	if (backgroundStyle === 'linearGradient') {
		const angle = gradientOptions.angle ?? 0;
		return `linear-gradient(${angle}deg, ${stopsStr})`;
	}
	if (backgroundStyle === 'radialGradient') {
		const shape = gradientOptions.shape ?? 'circle';
		if (gradientOptions.position) {
			const { x, y } = gradientOptions.position;
			return `radial-gradient(${shape} at ${x}% ${y}%, ${stopsStr})`;
		}
		return `radial-gradient(${shape}, ${stopsStr})`;
	}
	return DEFAULT_SQUARE_STYLE.backgroundColor ?? 'transparent';
};

const buildStyle = (styleData: IShapeSquareStyle): React.CSSProperties => {
	const background = getBackground(styleData);
	const borderWidth = styleData.border?.width;
	const borderStyle = styleData.border?.style;
	const borderColor = styleData.border?.color;
	const computedOpacity = styleData.opacity > 1 ? styleData.opacity / 100 : styleData.opacity;

	return {
		background,
		opacity: computedOpacity,
		borderRadius: `${styleData.border?.radius ?? DEFAULT_SQUARE_STYLE.border?.radius}px`,
		border: borderStyle === 'none' ? 'none' : `${borderWidth}px ${borderStyle} ${borderColor}`,
		boxShadow: styleData.boxShadow,
		width: '100%',
		height: '100%',
	};
};

export const Rectangle = ({ content }: { content: IShapeSquareStyle }) => {
	const rectangleData = useMemo(() => content ?? DEFAULT_SQUARE_STYLE, [content]);
	const rectangleStyle = useMemo(() => buildStyle(rectangleData), [rectangleData]);

	return <div style={rectangleStyle} />;
};
