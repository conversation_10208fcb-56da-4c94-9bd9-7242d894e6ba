import { HTMLOverlayProps } from '@/pages/edit-apresentation/types/html-overlay/container.type';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { useAtomValue } from 'jotai';
import { componentRegistry } from './components';
import { HTMLOverlayItem } from './item';

export function HTMLOverlay({ scale, containerSize }: HTMLOverlayProps) {
	const items = useAtomValue(itemsAtom);
	const renderableItems = items.filter((item) => item.type in componentRegistry).sort((a, b) => b.layer - a.layer);

	return (
		<div style={{ pointerEvents: 'none' }}>
			{renderableItems.map((item) => (
				<HTMLOverlayItem key={item.tempId} item={item} scale={scale} containerSize={containerSize} />
			))}
		</div>
	);
}
