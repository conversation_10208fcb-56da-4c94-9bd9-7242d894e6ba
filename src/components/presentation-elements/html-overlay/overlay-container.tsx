export const OverlayContainer = ({
	canvasProps,
	children,
	padding = 0,
}: {
	canvasProps: { width: number; height: number };
	children: React.ReactNode;
	padding?: number;
}) => (
	<div
		style={{
			position: 'absolute',
			top: 0,
			left: 0,
			width: canvasProps.width,
			height: canvasProps.height,
			pointerEvents: 'none',
			zIndex: 1,
		}}
	>
		<div
			style={{
				position: 'absolute',
				backgroundColor: 'rgba(128, 128, 128, 0.05)',
				top: padding,
				left: padding,
				width: canvasProps.width - padding * 2,
				height: canvasProps.height - padding * 2,
				pointerEvents: 'none',
			}}
		>
			{children}
		</div>
	</div>
);
