import { ICalendarObject } from '@/pages/edit-apresentation/components/properties-panel/item-editors/calendar/calendar.types';
import { ICarouselObject } from '@/pages/edit-apresentation/components/properties-panel/item-editors/carousel/corousel.types';
import { IShapeSquareStyle } from '@/pages/edit-apresentation/components/properties-panel/item-editors/shape/square-editor.types';
import { IWeather } from '@/pages/edit-apresentation/components/properties-panel/item-editors/weather/weather-editor.type';
import { ITEM_SHAPE_TYPE } from '@/pages/edit-apresentation/types/item.type';
import { CalendarDemo } from '../elements-overlay-base/calendar/calendar';
import { CarouselDemo } from '../elements-overlay-base/carrousel-demo';
import ResponsiveClock from '../elements-overlay-base/clock';
import { ImagePreview } from '../elements-overlay-base/image';
import { Rectangle } from '../elements-overlay-base/rectangle';
import { Weather } from '../elements-overlay-base/weather';

export const componentRegistry = {
	[ITEM_SHAPE_TYPE.CALENDAR]: ({ id, content }: { id: string; content: object }) => <CalendarDemo id={id} content={content as ICalendarObject} />,
	[ITEM_SHAPE_TYPE.CAROUSEL]: ({ content, id }: { content: object; id: string }) => <CarouselDemo content={content as ICarouselObject} id={id} />,
	[ITEM_SHAPE_TYPE.RECTANGLE]: ({ content }: { content: object }) => <Rectangle content={content as IShapeSquareStyle} />,
	[ITEM_SHAPE_TYPE.IMAGE]: ({ id }: { id: string }) => <ImagePreview id={id} />,
	[ITEM_SHAPE_TYPE.WEATHER]: ({ content }: { content: object }) => <Weather content={content as IWeather} />,
	[ITEM_SHAPE_TYPE.CLOCK]: () => <ResponsiveClock />,
};
