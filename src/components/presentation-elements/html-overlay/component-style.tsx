import { ContainerSize, PositionHtmlOverlay, SizeInHtmlOverlay } from '@/pages/edit-apresentation/types/html-overlay/container.type';

export const calculatePositionStyle = (
	position: PositionHtmlOverlay,
	size: SizeInHtmlOverlay,
	scale: number,
	layer: number,
	containerSize: ContainerSize,
	isDragging: boolean = false,
): React.CSSProperties => {
	const scaledX = position.x * scale;
	const scaledY = position.y * scale;

	const posX = Math.round(Math.min(scaledX, containerSize.width - size.width * scale));
	const posY = Math.round(Math.min(scaledY, containerSize.height - size.height * scale));

	const width = Math.round(size.width * scale);
	const height = Math.round(size.height * scale);

	return {
		position: 'absolute',
		transform: `translate3d(${posX}px, ${posY}px, 0)`,
		width,
		height,
		pointerEvents: 'none',
		overflow: 'hidden',
		border: 'none',
		zIndex: layer,
		willChange: isDragging ? 'transform' : 'auto',
		opacity: isDragging ? 0 : 1,
		backfaceVisibility: 'hidden',
		transition: isDragging ? 'none' : 'opacity 0.15s ease-out',
	};
};
