import { ContainerSize } from '@/pages/edit-apresentation/types/html-overlay/container.type';
import { IItem } from '@/pages/edit-apresentation/types/item.type';
import React, { useMemo } from 'react';
import { calculatePositionStyle } from './component-style';
import { componentRegistry } from './components';

interface HTMLOverlayItemProps {
	item: IItem;
	scale: number;
	containerSize: ContainerSize;
}

function renderComponent(item: IItem) {
	const renderer = componentRegistry[item.type as keyof typeof componentRegistry];
	if (!renderer) return null;
	return item.content
		? (renderer as (props: { content: object; id: string }) => JSX.Element)({ content: item.content, id: item.id ?? '' })
		: (renderer as (props: { id: string }) => JSX.Element)({ id: item.id ?? '' });
}

export const HTMLOverlayItem: React.FC<HTMLOverlayItemProps> = React.memo(({ item, scale, containerSize }) => {
	const style = useMemo(
		() => calculatePositionStyle(item.position, item.size, scale, item.layer, containerSize, item.isDragging),
		[item.position, item.size, scale, item.layer, containerSize, item.isDragging],
	);

	const renderedComponent = useMemo(() => renderComponent(item), [item]);

	return (
		<div key={item.tempId} style={style} className="absolute" data-item-id={item.tempId} data-dragging={item.isDragging}>
			{renderedComponent}
		</div>
	);
});
