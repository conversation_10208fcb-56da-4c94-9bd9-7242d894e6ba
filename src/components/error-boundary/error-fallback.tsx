import { motion } from 'framer-motion';
import { AlertOctagon } from 'lucide-react';
import Particles from '../shadcnui/particles';
import { ErrorMessage } from './error-message';
import { ReloadButton } from './reload-button';

interface ErrorFallbackProps {
	error?: Error;
}

export const ErrorFallback = ({ error }: ErrorFallbackProps) => {
	return (
		<div className="relative flex min-h-screen w-full items-center justify-center bg-gradient-to-b from-background to-background/95 p-4">
			<motion.div
				className="relative z-10 flex max-w-md flex-col items-center rounded-xl border border-border/40 bg-background/95 p-10 shadow-2xl backdrop-blur-xl"
				initial={{ opacity: 0, scale: 0.8, y: 20 }}
				animate={{ opacity: 1, scale: 1, y: 0 }}
				transition={{ duration: 0.6, ease: [0.23, 1, 0.32, 1] }}
			>
				<motion.div initial={{ scale: 0.8, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} transition={{ duration: 0.5, ease: 'easeInOut' }}>
					<AlertOctagon className="mb-6 h-16 w-16 text-destructive drop-shadow-md" />
				</motion.div>

				<h2 className="mb-4 bg-gradient-to-r from-destructive to-destructive/70 bg-clip-text text-3xl font-bold text-transparent">
					Oops, algo deu errado!
				</h2>

				{error?.message && <ErrorMessage message={error.message} />}

				<p className="mb-8 text-center text-muted-foreground">Não se preocupe, você pode tentar recarregar a página para resolver o problema</p>

				<ReloadButton />
			</motion.div>

			<Particles className="absolute inset-0" quantity={200} refresh />
		</div>
	);
};
