import { Component, ErrorInfo, ReactNode } from 'react';
import { ErrorFallback } from './error-fallback';

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
}

interface State {
	hasError: boolean;
	error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
	public state: State = {
		hasError: false,
		error: undefined,
	};

	public static getDerivedStateFromError(error: Error): State {
		return {
			hasError: true,
			error,
		};
	}

	public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		console.error('Error caught by error boundary:', error);
		console.error('Error info:', errorInfo);
	}

	public render() {
		if (this.state.hasError) {
			return this.props.fallback || <ErrorFallback error={this.state.error} />;
		}

		return this.props.children;
	}
}
