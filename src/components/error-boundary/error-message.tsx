import { motion } from 'framer-motion';

interface ErrorMessageProps {
	message: string;
}

export const ErrorMessage = ({ message }: ErrorMessageProps) => {
	return (
		<motion.div className="w-full" initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: 'auto' }}>
			<p className="mb-6 min-h-[40px] w-full overflow-hidden rounded-lg border border-border/50 bg-muted/30 p-4 backdrop-blur-sm">
				<span className="font-mono text-sm text-muted-foreground">{message}</span>
			</p>
		</motion.div>
	);
};
