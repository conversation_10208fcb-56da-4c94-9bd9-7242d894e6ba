import { motion } from 'framer-motion';
import { RefreshCw } from 'lucide-react';

export const ReloadButton = () => {
	const handleReload = () => window.location.reload();

	return (
		<motion.button
			whileHover={{ scale: 1.02, boxShadow: '0 5px 15px rgba(0,0,0,0.1)' }}
			whileTap={{ scale: 0.98 }}
			onClick={handleReload}
			className="inline-flex items-center gap-3 rounded-full bg-gradient-to-r from-primary to-primary/90 px-8 py-3 font-medium text-primary-foreground shadow-md transition-all duration-200 hover:from-primary/90 hover:to-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
		>
			<RefreshCw className="h-5 w-5" />
			Recarregar <PERSON>
		</motion.button>
	);
};
