import { Input } from '@/components/shadcnui/input';
import { LucideSearch, X } from 'lucide-react';
import React from 'react';

interface IGlobalFilterProps {
	globalFilter: string;
	placeholder?: string;
	setGlobalFilter: (value: string) => void;
}

export const GlobalFilter: React.FC<IGlobalFilterProps> = ({ globalFilter, setGlobalFilter, placeholder }) => {
	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setGlobalFilter(e.target.value);
	};

	const handleClear = () => {
		setGlobalFilter('');
	};

	return (
		<div className="relative inline-flex h-[40px] w-full items-center rounded-md sm:w-[480px]">
			<LucideSearch size={18} className="absolute left-2.5 top-2.5 text-gray-400" />
			<Input
				id="search"
				className="h-[40px] min-h-10 w-full rounded-lg border-white/10 bg-black/20 pl-9 pr-8 text-sm placeholder:text-gray-500 hover:border-zinc-700 focus:border-green-500 focus:ring-green-500/30"
				value={globalFilter ?? ''}
				placeholder={placeholder}
				onChange={handleChange}
			/>
			{globalFilter && (
				<button onClick={handleClear} className="absolute right-2.5 top-2.5 rounded-full p-0.5 text-gray-400 hover:bg-gray-600/30 hover:text-white">
					<X size={14} />
					<span className="sr-only">Limpar busca</span>
				</button>
			)}
		</div>
	);
};
