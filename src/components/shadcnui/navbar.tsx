import {
	Avatar,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownTrigger,
	Image,
	Navbar as Nav,
	NavbarContent,
	NavbarItem,
	NavbarMenu,
	NavbarMenuItem,
	NavbarMenuToggle,
} from '@nextui-org/react';
import { LogOut, User } from 'lucide-react';
import { useState } from 'react';
import { NavLink } from 'react-router-dom';

export function Navbar() {
	const [isMenuOpen, setIsMenuOpen] = useState(false);

	const pages = [
		{ to: '/devices', label: 'Dispositivos', id: 'devices' },
		{ to: '/presentations', label: 'Apresentações', id: 'presentations' },
		{ to: '/programacao', label: 'Programação', id: 'programming' },
	];

	const mockUser = {
		name: '<PERSON>',
		email: '<EMAIL>',
		avatar: 'https://i.pravatar.cc/150',
	};

	const handleLogout = () => {
		console.log('Logout clicked');
	};

	const closeMenu = () => {
		setIsMenuOpen(false);
	};

	return (
		<Nav maxWidth="2xl" className="bg-muted px-2 py-2 md:px-4" isBordered isMenuOpen={isMenuOpen} onMenuOpenChange={setIsMenuOpen}>
			<NavbarContent justify="start" className="flex gap-6 md:gap-6">
				<NavbarMenuToggle className="text-white md:hidden" />
				<NavLink to="/home" className="group flex w-full flex-1 items-center justify-center md:justify-start md:gap-3">
					<Image
						className="max-h-[36px] max-w-[36px] rounded-none object-contain drop-shadow-lg md:max-h-[40px] md:max-w-[40px]"
						src="/assets/svgs/reduced-logo.svg"
						width={36}
						height={36}
						alt="StreamHub Logo"
					/>
					<div className="flex flex-col items-center justify-center px-2 py-1">
						<span className="whitespace-nowrap text-xl font-extrabold tracking-tight text-white transition-colors group-hover:text-green-400 md:text-2xl">
							Stream <span className="text-green-400 transition-colors group-hover:text-white">Hub</span>
						</span>
						<span className="-mt-1 whitespace-nowrap text-xs font-semibold tracking-widest text-white group-hover:text-green-400">
							Pormade
						</span>
					</div>
				</NavLink>
				{pages.map((page) => (
					<NavbarItem key={page.id} className="hidden md:block">
						<NavLink
							to={page.to}
							className="rounded-md px-3 py-1 font-medium text-white transition-all duration-200 hover:bg-green-500/10 hover:text-green-400"
							style={({ isActive }) => ({
								color: isActive ? '#4ade80' : undefined,
								fontWeight: isActive ? 'bold' : undefined,
								background: isActive ? 'rgba(74, 222, 128, 0.1)' : undefined,
								boxShadow: isActive ? '0 0 0 1px rgba(74, 222, 128, 0.3)' : undefined,
							})}
						>
							{page.label}
						</NavLink>
					</NavbarItem>
				))}
			</NavbarContent>

			<NavbarContent justify="end">
				<NavbarItem>
					<Dropdown placement="bottom-end">
						<DropdownTrigger>
							<div className="flex cursor-pointer items-center gap-2 rounded-md px-1 py-1 transition hover:bg-gray-800/50 md:gap-3 md:px-2">
								<div className="hidden flex-col items-end md:flex">
									<span className="text-md font-semibold text-white">{mockUser.name}</span>
									<span className="text-xs text-gray-400">{mockUser.email}</span>
								</div>
								<Avatar
									size="md"
									src={mockUser.avatar}
									className="ring-2 ring-green-400 ring-offset-1 ring-offset-[#121214] transition-transform hover:scale-105"
								/>
							</div>
						</DropdownTrigger>
						<DropdownMenu
							aria-label="Ações do perfil"
							className="border border-gray-800 bg-[#1c1c20]"
							onAction={(key) => {
								if (key === 'logout') {
									handleLogout();
								}
							}}
						>
							<DropdownItem
								key="profile"
								startContent={<User size={16} className="text-green-400" />}
								className="text-white hover:bg-green-400/10"
							>
								Meu Perfil
							</DropdownItem>
							<DropdownItem key="logout" className="text-red-400 hover:bg-red-400/10" startContent={<LogOut size={16} />}>
								Sair
							</DropdownItem>
						</DropdownMenu>
					</Dropdown>
				</NavbarItem>
			</NavbarContent>

			<NavbarMenu className="border-t border-gray-800 bg-muted bg-gradient-to-b from-background/50 to-background/95 p-5">
				{pages.map((page) => (
					<NavbarMenuItem key={page.id}>
						<NavLink
							to={page.to}
							onClick={closeMenu}
							className={({ isActive }) =>
								`block rounded-md px-4 py-3 text-base transition-colors ${
									isActive
										? 'bg-green-500/10 font-bold text-green-400 shadow-[0_0_0_1px_rgba(74,222,128,0.3)]'
										: 'text-white hover:bg-green-500/10 hover:text-green-400'
								}`
							}
						>
							{page.label}
						</NavLink>
					</NavbarMenuItem>
				))}
			</NavbarMenu>
		</Nav>
	);
}
