import { NextUIProvider } from '@nextui-org/react';
import { createStore, Provider as <PERSON><PERSON><PERSON>rovider } from 'jotai';
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './app';
import './index.css';

export const StreamHubStore = createStore();

ReactDOM.createRoot(document.getElementById('root')!).render(
	<React.StrictMode>
		<JotaiProvider store={StreamHubStore}>
			<NextUIProvider>
				<App />
			</NextUIProvider>
		</JotaiProvider>
	</React.StrictMode>,
);
